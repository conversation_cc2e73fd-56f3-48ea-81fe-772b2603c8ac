2025-06-15 21:15:47,967 ERROR scheduler Skipped queueing frappe.utils.change_log.check_for_update because it was found in queue for site1
2025-06-15 21:15:47,973 ERROR scheduler Skipped queueing erpnext.setup.doctype.email_digest.email_digest.send because it was found in queue for site1
2025-06-15 21:15:47,977 ERROR scheduler Skipped queueing frappe.email.doctype.auto_email_report.auto_email_report.send_daily because it was found in queue for site1
2025-06-15 21:15:47,995 ERROR scheduler Skipped queueing frappe.social.doctype.energy_point_settings.energy_point_settings.allocate_review_points because it was found in queue for site1
2025-06-15 21:15:48,002 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_drive.google_drive.weekly_backup because it was found in queue for site1
2025-06-15 21:15:48,005 ERROR scheduler Skipped queueing frappe.integrations.doctype.dropbox_settings.dropbox_settings.take_backups_daily because it was found in queue for site1
2025-06-15 21:15:48,041 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.remove_unverified_record because it was found in queue for site1
2025-06-15 21:15:48,044 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_drive.google_drive.daily_backup because it was found in queue for site1
2025-06-15 21:15:48,047 ERROR scheduler Skipped queueing frappe.sessions.clear_expired_sessions because it was found in queue for site1
2025-06-15 21:15:48,061 ERROR scheduler Skipped queueing erpnext.accounts.doctype.process_subscription.process_subscription.create_subscription_process because it was found in queue for site1
2025-06-15 21:15:48,065 ERROR scheduler Skipped queueing frappe.automation.doctype.auto_repeat.auto_repeat.make_auto_repeat_entry because it was found in queue for site1
2025-06-15 21:16:49,712 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_schedule_assignment.shift_schedule_assignment.process_auto_shift_creation because it was found in queue for site1
2025-06-15 21:16:49,723 ERROR scheduler Skipped queueing frappe.desk.notifications.clear_notifications because it was found in queue for site1
2025-06-15 21:16:49,729 ERROR scheduler Skipped queueing erpnext.setup.doctype.email_digest.email_digest.send because it was found in queue for site1
2025-06-15 21:16:49,736 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.update_last_sync_of_checkin because it was found in queue for site1
2025-06-15 21:16:49,745 ERROR scheduler Skipped queueing frappe.social.doctype.energy_point_settings.energy_point_settings.allocate_review_points because it was found in queue for site1
2025-06-15 21:16:49,755 ERROR scheduler Skipped queueing frappe.integrations.doctype.dropbox_settings.dropbox_settings.take_backups_weekly because it was found in queue for site1
2025-06-15 21:16:49,763 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for site1
2025-06-15 21:16:49,769 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_drive.google_drive.daily_backup because it was found in queue for site1
2025-06-15 21:16:49,782 ERROR scheduler Skipped queueing hrms.hr.doctype.leave_ledger_entry.leave_ledger_entry.process_expired_allocation because it was found in queue for site1
2025-06-15 21:16:49,790 ERROR scheduler Skipped queueing frappe.utils.change_log.check_for_update because it was found in queue for site1
2025-06-15 21:16:49,795 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_contacts.google_contacts.sync because it was found in queue for site1
2025-06-15 21:16:49,801 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for site1
2025-06-15 21:16:49,805 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for site1
2025-06-15 21:16:49,821 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for site1
2025-06-15 21:16:49,826 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.process_auto_attendance_for_all_shifts because it was found in queue for site1
2025-06-15 21:16:49,834 ERROR scheduler Skipped queueing frappe.desk.doctype.changelog_feed.changelog_feed.fetch_changelog_feed because it was found in queue for site1
2025-06-15 21:16:49,838 ERROR scheduler Skipped queueing frappe.integrations.doctype.dropbox_settings.dropbox_settings.take_backups_daily because it was found in queue for site1
2025-06-15 21:16:49,844 ERROR scheduler Skipped queueing erpnext.accounts.doctype.process_subscription.process_subscription.create_subscription_process because it was found in queue for site1
2025-06-15 21:16:49,848 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for site1
2025-06-15 21:16:49,865 ERROR scheduler Skipped queueing frappe.automation.doctype.auto_repeat.auto_repeat.make_auto_repeat_entry because it was found in queue for site1
2025-06-15 21:16:49,868 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_drive.google_drive.weekly_backup because it was found in queue for site1
2025-06-15 21:16:49,886 ERROR scheduler Skipped queueing frappe.sessions.clear_expired_sessions because it was found in queue for site1
2025-06-15 21:16:49,890 ERROR scheduler Skipped queueing frappe.email.doctype.auto_email_report.auto_email_report.send_daily because it was found in queue for site1
2025-06-15 21:16:49,894 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for site1
2025-06-15 21:16:49,901 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_weekly_updates because it was found in queue for site1
2025-06-15 21:16:49,907 ERROR scheduler Skipped queueing frappe.integrations.doctype.s3_backup_settings.s3_backup_settings.take_backups_weekly because it was found in queue for site1
2025-06-15 21:16:49,923 ERROR scheduler Skipped queueing hrms.hr.utils.generate_leave_encashment because it was found in queue for site1
2025-06-15 21:16:49,937 ERROR scheduler Skipped queueing frappe.social.doctype.energy_point_log.energy_point_log.send_weekly_summary because it was found in queue for site1
2025-06-15 21:16:49,949 ERROR scheduler Skipped queueing erpnext.assets.doctype.asset.depreciation.post_depreciation_entries because it was found in queue for site1
2025-06-15 21:16:49,968 ERROR scheduler Skipped queueing erpnext.crm.utils.open_leads_opportunities_based_on_todays_event because it was found in queue for site1
2025-06-15 21:16:49,973 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.remove_unverified_record because it was found in queue for site1
2025-06-15 21:16:49,983 ERROR scheduler Skipped queueing frappe.integrations.doctype.s3_backup_settings.s3_backup_settings.take_backups_daily because it was found in queue for site1
2025-06-15 21:16:49,986 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for site1
2025-06-15 21:16:49,994 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for site1
2025-06-15 21:16:49,997 ERROR scheduler Skipped queueing erpnext.manufacturing.doctype.bom_update_tool.bom_update_tool.auto_update_latest_price_in_all_boms because it was found in queue for site1
2025-06-15 21:16:50,016 ERROR scheduler Skipped queueing hrms.hr.utils.allocate_earned_leaves because it was found in queue for site1
2025-06-15 21:16:50,020 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for site1
2025-06-15 21:16:50,027 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for site1
2025-06-15 21:16:50,030 ERROR scheduler Skipped queueing frappe.core.doctype.log_settings.log_settings.run_log_clean_up because it was found in queue for site1
2025-06-15 21:17:51,087 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for site1
2025-06-15 21:17:51,103 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for site1
2025-06-15 21:17:51,108 ERROR scheduler Skipped queueing hrms.hr.utils.generate_leave_encashment because it was found in queue for site1
2025-06-15 21:17:51,114 ERROR scheduler Skipped queueing erpnext.assets.doctype.asset.depreciation.post_depreciation_entries because it was found in queue for site1
2025-06-15 21:17:51,120 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_schedule_assignment.shift_schedule_assignment.process_auto_shift_creation because it was found in queue for site1
2025-06-15 21:17:51,127 ERROR scheduler Skipped queueing frappe.integrations.doctype.dropbox_settings.dropbox_settings.take_backups_weekly because it was found in queue for site1
2025-06-15 21:17:51,146 ERROR scheduler Skipped queueing frappe.social.doctype.energy_point_settings.energy_point_settings.allocate_review_points because it was found in queue for site1
2025-06-15 21:17:51,152 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for site1
2025-06-15 21:17:51,158 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.process_auto_attendance_for_all_shifts because it was found in queue for site1
2025-06-15 21:17:51,163 ERROR scheduler Skipped queueing frappe.integrations.doctype.dropbox_settings.dropbox_settings.take_backups_daily because it was found in queue for site1
2025-06-15 21:17:51,171 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for site1
2025-06-15 21:17:51,180 ERROR scheduler Skipped queueing frappe.integrations.doctype.s3_backup_settings.s3_backup_settings.take_backups_daily because it was found in queue for site1
2025-06-15 21:17:51,184 ERROR scheduler Skipped queueing hrms.hr.doctype.interview.interview.send_interview_reminder because it was found in queue for site1
2025-06-15 21:17:51,193 ERROR scheduler Skipped queueing frappe.sessions.clear_expired_sessions because it was found in queue for site1
2025-06-15 21:17:51,205 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for site1
2025-06-15 21:17:51,223 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_weekly_updates because it was found in queue for site1
2025-06-15 21:17:51,279 ERROR scheduler Skipped queueing frappe.automation.doctype.auto_repeat.auto_repeat.make_auto_repeat_entry because it was found in queue for site1
2025-06-15 21:17:51,306 ERROR scheduler Skipped queueing frappe.email.queue.flush because it was found in queue for site1
2025-06-15 21:17:51,316 ERROR scheduler Skipped queueing hrms.hr.utils.allocate_earned_leaves because it was found in queue for site1
2025-06-15 21:17:51,348 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for site1
2025-06-15 21:17:51,384 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for site1
2025-06-15 21:17:51,388 ERROR scheduler Skipped queueing frappe.integrations.doctype.s3_backup_settings.s3_backup_settings.take_backups_weekly because it was found in queue for site1
2025-06-15 21:17:51,399 ERROR scheduler Skipped queueing frappe.monitor.flush because it was found in queue for site1
2025-06-15 21:17:51,409 ERROR scheduler Skipped queueing frappe.utils.change_log.check_for_update because it was found in queue for site1
2025-06-15 21:17:51,415 ERROR scheduler Skipped queueing frappe.desk.notifications.clear_notifications because it was found in queue for site1
2025-06-15 21:17:51,426 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for site1
2025-06-15 21:17:51,437 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_calendar.google_calendar.sync because it was found in queue for site1
2025-06-15 21:17:51,441 ERROR scheduler Skipped queueing erpnext.crm.utils.open_leads_opportunities_based_on_todays_event because it was found in queue for site1
2025-06-15 21:17:51,452 ERROR scheduler Skipped queueing erpnext.manufacturing.doctype.bom_update_tool.bom_update_tool.auto_update_latest_price_in_all_boms because it was found in queue for site1
2025-06-15 21:17:51,460 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.remove_unverified_record because it was found in queue for site1
2025-06-15 21:17:51,466 ERROR scheduler Skipped queueing erpnext.accounts.doctype.process_subscription.process_subscription.create_subscription_process because it was found in queue for site1
2025-06-15 21:17:51,472 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_drive.google_drive.daily_backup because it was found in queue for site1
2025-06-15 21:17:51,478 ERROR scheduler Skipped queueing hrms.hr.doctype.leave_ledger_entry.leave_ledger_entry.process_expired_allocation because it was found in queue for site1
2025-06-15 21:17:51,484 ERROR scheduler Skipped queueing erpnext.setup.doctype.email_digest.email_digest.send because it was found in queue for site1
2025-06-15 21:17:51,490 ERROR scheduler Skipped queueing frappe.social.doctype.energy_point_log.energy_point_log.send_weekly_summary because it was found in queue for site1
2025-06-15 21:17:51,497 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_contacts.google_contacts.sync because it was found in queue for site1
2025-06-15 21:17:51,501 ERROR scheduler Skipped queueing frappe.email.doctype.auto_email_report.auto_email_report.send_daily because it was found in queue for site1
2025-06-15 21:17:51,509 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for site1
2025-06-15 21:17:51,525 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_drive.google_drive.weekly_backup because it was found in queue for site1
2025-06-15 21:17:51,532 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.update_last_sync_of_checkin because it was found in queue for site1
2025-06-15 21:17:51,539 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for site1
2025-06-15 21:17:51,544 ERROR scheduler Skipped queueing frappe.desk.doctype.changelog_feed.changelog_feed.fetch_changelog_feed because it was found in queue for site1
2025-06-15 21:17:51,552 ERROR scheduler Skipped queueing frappe.core.doctype.log_settings.log_settings.run_log_clean_up because it was found in queue for site1
2025-06-15 21:18:52,735 ERROR scheduler Skipped queueing hrms.hr.utils.generate_leave_encashment because it was found in queue for site1
2025-06-15 21:18:52,739 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.remove_unverified_record because it was found in queue for site1
2025-06-15 21:18:52,749 ERROR scheduler Skipped queueing erpnext.manufacturing.doctype.bom_update_tool.bom_update_tool.auto_update_latest_price_in_all_boms because it was found in queue for site1
2025-06-15 21:18:52,759 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for site1
2025-06-15 21:18:52,763 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for site1
2025-06-15 21:18:52,779 ERROR scheduler Skipped queueing frappe.social.doctype.energy_point_log.energy_point_log.send_weekly_summary because it was found in queue for site1
2025-06-15 21:18:52,785 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_weekly_updates because it was found in queue for site1
2025-06-15 21:18:52,788 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_contacts.google_contacts.sync because it was found in queue for site1
2025-06-15 21:18:52,795 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for site1
2025-06-15 21:18:52,815 ERROR scheduler Skipped queueing hrms.hr.utils.allocate_earned_leaves because it was found in queue for site1
2025-06-15 21:18:52,820 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for site1
2025-06-15 21:18:52,831 ERROR scheduler Skipped queueing frappe.sessions.clear_expired_sessions because it was found in queue for site1
2025-06-15 21:18:52,834 ERROR scheduler Skipped queueing frappe.desk.notifications.clear_notifications because it was found in queue for site1
2025-06-15 21:18:52,840 ERROR scheduler Skipped queueing erpnext.crm.utils.open_leads_opportunities_based_on_todays_event because it was found in queue for site1
2025-06-15 21:18:52,848 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.process_auto_attendance_for_all_shifts because it was found in queue for site1
2025-06-15 21:18:52,856 ERROR scheduler Skipped queueing frappe.integrations.doctype.s3_backup_settings.s3_backup_settings.take_backups_daily because it was found in queue for site1
2025-06-15 21:18:52,860 ERROR scheduler Skipped queueing frappe.desk.doctype.changelog_feed.changelog_feed.fetch_changelog_feed because it was found in queue for site1
2025-06-15 21:18:52,866 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for site1
2025-06-15 21:18:52,876 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_schedule_assignment.shift_schedule_assignment.process_auto_shift_creation because it was found in queue for site1
2025-06-15 21:18:52,882 ERROR scheduler Skipped queueing frappe.automation.doctype.auto_repeat.auto_repeat.make_auto_repeat_entry because it was found in queue for site1
2025-06-15 21:18:52,885 ERROR scheduler Skipped queueing erpnext.setup.doctype.email_digest.email_digest.send because it was found in queue for site1
2025-06-15 21:18:52,894 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.update_last_sync_of_checkin because it was found in queue for site1
2025-06-15 21:18:52,898 ERROR scheduler Skipped queueing frappe.integrations.doctype.dropbox_settings.dropbox_settings.take_backups_daily because it was found in queue for site1
2025-06-15 21:18:52,916 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for site1
2025-06-15 21:18:52,950 ERROR scheduler Skipped queueing erpnext.assets.doctype.asset.depreciation.post_depreciation_entries because it was found in queue for site1
2025-06-15 21:18:52,958 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for site1
2025-06-15 21:18:52,966 ERROR scheduler Skipped queueing frappe.integrations.doctype.dropbox_settings.dropbox_settings.take_backups_weekly because it was found in queue for site1
2025-06-15 21:18:52,976 ERROR scheduler Skipped queueing hrms.hr.doctype.leave_ledger_entry.leave_ledger_entry.process_expired_allocation because it was found in queue for site1
2025-06-15 21:18:52,982 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_drive.google_drive.weekly_backup because it was found in queue for site1
2025-06-15 21:18:53,000 ERROR scheduler Skipped queueing frappe.utils.change_log.check_for_update because it was found in queue for site1
2025-06-15 21:18:53,010 ERROR scheduler Skipped queueing erpnext.accounts.doctype.process_subscription.process_subscription.create_subscription_process because it was found in queue for site1
2025-06-15 21:18:53,018 ERROR scheduler Skipped queueing frappe.social.doctype.energy_point_settings.energy_point_settings.allocate_review_points because it was found in queue for site1
2025-06-15 21:18:53,040 ERROR scheduler Skipped queueing frappe.email.doctype.auto_email_report.auto_email_report.send_daily because it was found in queue for site1
2025-06-15 21:18:53,056 ERROR scheduler Skipped queueing frappe.core.doctype.log_settings.log_settings.run_log_clean_up because it was found in queue for site1
2025-06-15 21:18:53,064 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for site1
2025-06-15 21:18:53,072 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_drive.google_drive.daily_backup because it was found in queue for site1
2025-06-15 21:18:53,078 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for site1
2025-06-15 21:18:53,083 ERROR scheduler Skipped queueing frappe.integrations.doctype.s3_backup_settings.s3_backup_settings.take_backups_weekly because it was found in queue for site1
2025-06-15 21:18:53,096 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for site1
2025-06-15 21:19:53,124 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for site1
2025-06-15 21:19:53,127 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for site1
2025-06-15 21:19:53,131 ERROR scheduler Skipped queueing frappe.integrations.doctype.s3_backup_settings.s3_backup_settings.take_backups_weekly because it was found in queue for site1
2025-06-15 21:19:53,142 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for site1
2025-06-15 21:19:53,145 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_drive.google_drive.daily_backup because it was found in queue for site1
2025-06-15 21:19:53,148 ERROR scheduler Skipped queueing frappe.core.doctype.log_settings.log_settings.run_log_clean_up because it was found in queue for site1
2025-06-15 21:19:53,151 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for site1
2025-06-15 21:19:53,155 ERROR scheduler Skipped queueing erpnext.crm.utils.open_leads_opportunities_based_on_todays_event because it was found in queue for site1
2025-06-15 21:19:53,164 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for site1
2025-06-15 21:19:53,179 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.remove_unverified_record because it was found in queue for site1
2025-06-15 21:19:53,183 ERROR scheduler Skipped queueing frappe.desk.notifications.clear_notifications because it was found in queue for site1
2025-06-15 21:19:53,186 ERROR scheduler Skipped queueing frappe.email.doctype.auto_email_report.auto_email_report.send_daily because it was found in queue for site1
2025-06-15 21:19:53,198 ERROR scheduler Skipped queueing frappe.sessions.clear_expired_sessions because it was found in queue for site1
2025-06-15 21:19:53,205 ERROR scheduler Skipped queueing erpnext.manufacturing.doctype.bom_update_tool.bom_update_tool.auto_update_latest_price_in_all_boms because it was found in queue for site1
2025-06-15 21:19:53,225 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_contacts.google_contacts.sync because it was found in queue for site1
2025-06-15 21:19:53,234 ERROR scheduler Skipped queueing frappe.social.doctype.energy_point_log.energy_point_log.send_weekly_summary because it was found in queue for site1
2025-06-15 21:19:53,240 ERROR scheduler Skipped queueing erpnext.assets.doctype.asset.depreciation.post_depreciation_entries because it was found in queue for site1
2025-06-15 21:19:53,250 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_weekly_updates because it was found in queue for site1
2025-06-15 21:19:53,254 ERROR scheduler Skipped queueing erpnext.accounts.doctype.process_subscription.process_subscription.create_subscription_process because it was found in queue for site1
2025-06-15 21:19:53,263 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for site1
2025-06-15 21:19:53,272 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for site1
2025-06-15 21:19:53,279 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_schedule_assignment.shift_schedule_assignment.process_auto_shift_creation because it was found in queue for site1
2025-06-15 21:19:53,287 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for site1
2025-06-15 21:19:53,293 ERROR scheduler Skipped queueing frappe.utils.change_log.check_for_update because it was found in queue for site1
2025-06-15 21:19:53,300 ERROR scheduler Skipped queueing erpnext.setup.doctype.email_digest.email_digest.send because it was found in queue for site1
2025-06-15 21:19:53,330 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_drive.google_drive.weekly_backup because it was found in queue for site1
2025-06-15 21:19:53,337 ERROR scheduler Skipped queueing hrms.hr.doctype.leave_ledger_entry.leave_ledger_entry.process_expired_allocation because it was found in queue for site1
2025-06-15 21:19:53,342 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.update_last_sync_of_checkin because it was found in queue for site1
2025-06-15 21:19:53,348 ERROR scheduler Skipped queueing frappe.integrations.doctype.dropbox_settings.dropbox_settings.take_backups_daily because it was found in queue for site1
2025-06-15 21:19:53,355 ERROR scheduler Skipped queueing frappe.desk.doctype.changelog_feed.changelog_feed.fetch_changelog_feed because it was found in queue for site1
2025-06-15 21:19:53,369 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for site1
2025-06-15 21:19:53,380 ERROR scheduler Skipped queueing frappe.social.doctype.energy_point_settings.energy_point_settings.allocate_review_points because it was found in queue for site1
2025-06-15 21:19:53,391 ERROR scheduler Skipped queueing frappe.integrations.doctype.s3_backup_settings.s3_backup_settings.take_backups_daily because it was found in queue for site1
2025-06-15 21:19:53,397 ERROR scheduler Skipped queueing frappe.integrations.doctype.dropbox_settings.dropbox_settings.take_backups_weekly because it was found in queue for site1
2025-06-15 21:19:53,407 ERROR scheduler Skipped queueing hrms.hr.utils.generate_leave_encashment because it was found in queue for site1
2025-06-15 21:19:53,413 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.process_auto_attendance_for_all_shifts because it was found in queue for site1
2025-06-15 21:19:53,418 ERROR scheduler Skipped queueing frappe.automation.doctype.auto_repeat.auto_repeat.make_auto_repeat_entry because it was found in queue for site1
2025-06-15 21:19:53,423 ERROR scheduler Skipped queueing hrms.hr.utils.allocate_earned_leaves because it was found in queue for site1
2025-06-15 21:19:53,433 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for site1
2025-06-15 21:20:54,709 ERROR scheduler Skipped queueing frappe.core.doctype.log_settings.log_settings.run_log_clean_up because it was found in queue for site1
2025-06-15 21:20:54,715 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_schedule_assignment.shift_schedule_assignment.process_auto_shift_creation because it was found in queue for site1
2025-06-15 21:20:54,734 ERROR scheduler Skipped queueing frappe.utils.change_log.check_for_update because it was found in queue for site1
2025-06-15 21:20:54,738 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for site1
2025-06-15 21:20:54,745 ERROR scheduler Skipped queueing hrms.hr.doctype.leave_ledger_entry.leave_ledger_entry.process_expired_allocation because it was found in queue for site1
2025-06-15 21:20:54,755 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for site1
2025-06-15 21:20:54,767 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for site1
2025-06-15 21:20:54,781 ERROR scheduler Skipped queueing erpnext.manufacturing.doctype.bom_update_tool.bom_update_tool.auto_update_latest_price_in_all_boms because it was found in queue for site1
2025-06-15 21:20:54,785 ERROR scheduler Skipped queueing hrms.hr.utils.allocate_earned_leaves because it was found in queue for site1
2025-06-15 21:20:54,792 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for site1
2025-06-15 21:20:54,797 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_drive.google_drive.weekly_backup because it was found in queue for site1
2025-06-15 21:20:54,804 ERROR scheduler Skipped queueing frappe.social.doctype.energy_point_settings.energy_point_settings.allocate_review_points because it was found in queue for site1
2025-06-15 21:20:54,811 ERROR scheduler Skipped queueing hrms.hr.utils.generate_leave_encashment because it was found in queue for site1
2025-06-15 21:20:54,817 ERROR scheduler Skipped queueing frappe.sessions.clear_expired_sessions because it was found in queue for site1
2025-06-15 21:20:54,823 ERROR scheduler Skipped queueing frappe.email.doctype.auto_email_report.auto_email_report.send_daily because it was found in queue for site1
2025-06-15 21:20:54,832 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for site1
2025-06-15 21:20:54,844 ERROR scheduler Skipped queueing erpnext.crm.utils.open_leads_opportunities_based_on_todays_event because it was found in queue for site1
2025-06-15 21:20:54,847 ERROR scheduler Skipped queueing erpnext.assets.doctype.asset.depreciation.post_depreciation_entries because it was found in queue for site1
2025-06-15 21:20:54,863 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.remove_unverified_record because it was found in queue for site1
2025-06-15 21:20:54,866 ERROR scheduler Skipped queueing frappe.integrations.doctype.dropbox_settings.dropbox_settings.take_backups_weekly because it was found in queue for site1
2025-06-15 21:20:54,869 ERROR scheduler Skipped queueing erpnext.accounts.doctype.process_subscription.process_subscription.create_subscription_process because it was found in queue for site1
2025-06-15 21:20:54,873 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_contacts.google_contacts.sync because it was found in queue for site1
2025-06-15 21:20:54,880 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for site1
2025-06-15 21:20:54,890 ERROR scheduler Skipped queueing frappe.integrations.doctype.dropbox_settings.dropbox_settings.take_backups_daily because it was found in queue for site1
2025-06-15 21:20:54,897 ERROR scheduler Skipped queueing frappe.integrations.doctype.s3_backup_settings.s3_backup_settings.take_backups_weekly because it was found in queue for site1
2025-06-15 21:20:54,901 ERROR scheduler Skipped queueing frappe.integrations.doctype.s3_backup_settings.s3_backup_settings.take_backups_daily because it was found in queue for site1
2025-06-15 21:20:54,907 ERROR scheduler Skipped queueing frappe.social.doctype.energy_point_log.energy_point_log.send_weekly_summary because it was found in queue for site1
2025-06-15 21:20:54,914 ERROR scheduler Skipped queueing erpnext.setup.doctype.email_digest.email_digest.send because it was found in queue for site1
2025-06-15 21:20:54,933 ERROR scheduler Skipped queueing frappe.desk.notifications.clear_notifications because it was found in queue for site1
2025-06-15 21:20:54,936 ERROR scheduler Skipped queueing frappe.automation.doctype.auto_repeat.auto_repeat.make_auto_repeat_entry because it was found in queue for site1
2025-06-15 21:20:54,944 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for site1
2025-06-15 21:20:54,947 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for site1
2025-06-15 21:20:54,961 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.update_last_sync_of_checkin because it was found in queue for site1
2025-06-15 21:20:54,968 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for site1
2025-06-15 21:20:54,976 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_weekly_updates because it was found in queue for site1
2025-06-15 21:20:54,979 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_drive.google_drive.daily_backup because it was found in queue for site1
2025-06-15 21:20:54,986 ERROR scheduler Skipped queueing frappe.desk.doctype.changelog_feed.changelog_feed.fetch_changelog_feed because it was found in queue for site1
2025-06-15 21:20:54,999 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for site1
2025-06-15 21:20:55,002 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.process_auto_attendance_for_all_shifts because it was found in queue for site1
2025-06-15 21:21:56,106 ERROR scheduler Skipped queueing erpnext.setup.doctype.email_digest.email_digest.send because it was found in queue for site1
2025-06-15 21:21:56,126 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_schedule_assignment.shift_schedule_assignment.process_auto_shift_creation because it was found in queue for site1
2025-06-15 21:21:56,129 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.update_last_sync_of_checkin because it was found in queue for site1
2025-06-15 21:21:56,133 ERROR scheduler Skipped queueing frappe.monitor.flush because it was found in queue for site1
2025-06-15 21:21:56,143 ERROR scheduler Skipped queueing frappe.email.queue.flush because it was found in queue for site1
2025-06-15 21:21:56,148 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for site1
2025-06-15 21:21:56,152 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.pull because it was found in queue for site1
2025-06-15 21:21:56,161 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for site1
2025-06-15 21:21:56,165 ERROR scheduler Skipped queueing frappe.integrations.doctype.s3_backup_settings.s3_backup_settings.take_backups_weekly because it was found in queue for site1
2025-06-15 21:21:56,169 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for site1
2025-06-15 21:21:56,188 ERROR scheduler Skipped queueing hrms.hr.doctype.interview.interview.send_interview_reminder because it was found in queue for site1
2025-06-15 21:21:56,197 ERROR scheduler Skipped queueing frappe.utils.change_log.check_for_update because it was found in queue for site1
2025-06-15 21:21:56,201 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_weekly_updates because it was found in queue for site1
2025-06-15 21:21:56,205 ERROR scheduler Skipped queueing hrms.hr.doctype.leave_ledger_entry.leave_ledger_entry.process_expired_allocation because it was found in queue for site1
2025-06-15 21:21:56,209 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for site1
2025-06-15 21:21:56,212 ERROR scheduler Skipped queueing erpnext.crm.utils.open_leads_opportunities_based_on_todays_event because it was found in queue for site1
2025-06-15 21:21:56,225 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for site1
2025-06-15 21:21:56,231 ERROR scheduler Skipped queueing frappe.integrations.doctype.dropbox_settings.dropbox_settings.take_backups_daily because it was found in queue for site1
2025-06-15 21:21:56,236 ERROR scheduler Skipped queueing frappe.integrations.doctype.dropbox_settings.dropbox_settings.take_backups_weekly because it was found in queue for site1
2025-06-15 21:21:56,240 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.remove_unverified_record because it was found in queue for site1
2025-06-15 21:21:56,247 ERROR scheduler Skipped queueing frappe.desk.doctype.changelog_feed.changelog_feed.fetch_changelog_feed because it was found in queue for site1
2025-06-15 21:21:56,254 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for site1
2025-06-15 21:21:56,269 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for site1
2025-06-15 21:21:56,279 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.process_auto_attendance_for_all_shifts because it was found in queue for site1
2025-06-15 21:21:56,286 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for site1
2025-06-15 21:21:56,290 ERROR scheduler Skipped queueing frappe.social.doctype.energy_point_settings.energy_point_settings.allocate_review_points because it was found in queue for site1
2025-06-15 21:21:56,301 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_calendar.google_calendar.sync because it was found in queue for site1
2025-06-15 21:21:56,305 ERROR scheduler Skipped queueing frappe.social.doctype.energy_point_log.energy_point_log.send_weekly_summary because it was found in queue for site1
2025-06-15 21:21:56,308 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_drive.google_drive.weekly_backup because it was found in queue for site1
2025-06-15 21:21:56,312 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_drive.google_drive.daily_backup because it was found in queue for site1
2025-06-15 21:21:56,320 ERROR scheduler Skipped queueing frappe.integrations.doctype.s3_backup_settings.s3_backup_settings.take_backups_daily because it was found in queue for site1
2025-06-15 21:21:56,324 ERROR scheduler Skipped queueing frappe.desk.notifications.clear_notifications because it was found in queue for site1
2025-06-15 21:21:56,330 ERROR scheduler Skipped queueing hrms.hr.utils.allocate_earned_leaves because it was found in queue for site1
2025-06-15 21:21:56,338 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for site1
2025-06-15 21:21:56,342 ERROR scheduler Skipped queueing erpnext.accounts.doctype.process_subscription.process_subscription.create_subscription_process because it was found in queue for site1
2025-06-15 21:21:56,353 ERROR scheduler Skipped queueing frappe.automation.doctype.auto_repeat.auto_repeat.make_auto_repeat_entry because it was found in queue for site1
2025-06-15 21:21:56,361 ERROR scheduler Skipped queueing hrms.hr.utils.generate_leave_encashment because it was found in queue for site1
2025-06-15 21:21:56,364 ERROR scheduler Skipped queueing frappe.core.doctype.log_settings.log_settings.run_log_clean_up because it was found in queue for site1
2025-06-15 21:21:56,369 ERROR scheduler Skipped queueing frappe.sessions.clear_expired_sessions because it was found in queue for site1
2025-06-15 21:21:56,381 ERROR scheduler Skipped queueing erpnext.manufacturing.doctype.bom_update_tool.bom_update_tool.auto_update_latest_price_in_all_boms because it was found in queue for site1
2025-06-15 21:21:56,390 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for site1
2025-06-15 21:21:56,403 ERROR scheduler Skipped queueing erpnext.assets.doctype.asset.depreciation.post_depreciation_entries because it was found in queue for site1
2025-06-15 21:21:56,409 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_contacts.google_contacts.sync because it was found in queue for site1
2025-06-15 21:21:56,429 ERROR scheduler Skipped queueing frappe.email.doctype.auto_email_report.auto_email_report.send_daily because it was found in queue for site1
2025-06-15 21:22:57,114 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_calendar.google_calendar.sync because it was found in queue for site1
2025-06-15 21:22:57,118 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.update_last_sync_of_checkin because it was found in queue for site1
2025-06-15 21:22:57,134 ERROR scheduler Skipped queueing frappe.desk.doctype.changelog_feed.changelog_feed.fetch_changelog_feed because it was found in queue for site1
2025-06-15 21:22:57,146 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for site1
2025-06-15 21:22:57,152 ERROR scheduler Skipped queueing frappe.integrations.doctype.dropbox_settings.dropbox_settings.take_backups_weekly because it was found in queue for site1
2025-06-15 21:22:57,159 ERROR scheduler Skipped queueing erpnext.manufacturing.doctype.bom_update_tool.bom_update_tool.auto_update_latest_price_in_all_boms because it was found in queue for site1
2025-06-15 21:22:57,165 ERROR scheduler Skipped queueing frappe.monitor.flush because it was found in queue for site1
2025-06-15 21:22:57,172 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_schedule_assignment.shift_schedule_assignment.process_auto_shift_creation because it was found in queue for site1
2025-06-15 21:22:57,182 ERROR scheduler Skipped queueing frappe.core.doctype.log_settings.log_settings.run_log_clean_up because it was found in queue for site1
2025-06-15 21:22:57,185 ERROR scheduler Skipped queueing hrms.hr.doctype.leave_ledger_entry.leave_ledger_entry.process_expired_allocation because it was found in queue for site1
2025-06-15 21:22:57,195 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for site1
2025-06-15 21:22:57,201 ERROR scheduler Skipped queueing frappe.integrations.doctype.s3_backup_settings.s3_backup_settings.take_backups_weekly because it was found in queue for site1
2025-06-15 21:22:57,204 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for site1
2025-06-15 21:22:57,220 ERROR scheduler Skipped queueing erpnext.setup.doctype.email_digest.email_digest.send because it was found in queue for site1
2025-06-15 21:22:57,228 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_contacts.google_contacts.sync because it was found in queue for site1
2025-06-15 21:22:57,232 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_weekly_updates because it was found in queue for site1
2025-06-15 21:22:57,238 ERROR scheduler Skipped queueing frappe.sessions.clear_expired_sessions because it was found in queue for site1
2025-06-15 21:22:57,242 ERROR scheduler Skipped queueing frappe.social.doctype.energy_point_settings.energy_point_settings.allocate_review_points because it was found in queue for site1
2025-06-15 21:22:57,246 ERROR scheduler Skipped queueing hrms.hr.doctype.interview.interview.send_interview_reminder because it was found in queue for site1
2025-06-15 21:22:57,254 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for site1
2025-06-15 21:22:57,267 ERROR scheduler Skipped queueing frappe.email.queue.flush because it was found in queue for site1
2025-06-15 21:22:57,273 ERROR scheduler Skipped queueing frappe.automation.doctype.auto_repeat.auto_repeat.make_auto_repeat_entry because it was found in queue for site1
2025-06-15 21:22:57,277 ERROR scheduler Skipped queueing erpnext.assets.doctype.asset.depreciation.post_depreciation_entries because it was found in queue for site1
2025-06-15 21:22:57,281 ERROR scheduler Skipped queueing frappe.integrations.doctype.dropbox_settings.dropbox_settings.take_backups_daily because it was found in queue for site1
2025-06-15 21:22:57,286 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for site1
2025-06-15 21:22:57,290 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for site1
2025-06-15 21:22:57,296 ERROR scheduler Skipped queueing frappe.email.doctype.auto_email_report.auto_email_report.send_daily because it was found in queue for site1
2025-06-15 21:22:57,300 ERROR scheduler Skipped queueing frappe.social.doctype.energy_point_log.energy_point_log.send_weekly_summary because it was found in queue for site1
2025-06-15 21:22:57,306 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.process_auto_attendance_for_all_shifts because it was found in queue for site1
2025-06-15 21:22:57,316 ERROR scheduler Skipped queueing frappe.desk.notifications.clear_notifications because it was found in queue for site1
2025-06-15 21:22:57,325 ERROR scheduler Skipped queueing erpnext.accounts.doctype.process_subscription.process_subscription.create_subscription_process because it was found in queue for site1
2025-06-15 21:22:57,335 ERROR scheduler Skipped queueing frappe.utils.change_log.check_for_update because it was found in queue for site1
2025-06-15 21:22:57,351 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_drive.google_drive.daily_backup because it was found in queue for site1
2025-06-15 21:22:57,354 ERROR scheduler Skipped queueing erpnext.crm.utils.open_leads_opportunities_based_on_todays_event because it was found in queue for site1
2025-06-15 21:22:57,362 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for site1
2025-06-15 21:22:57,370 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.pull because it was found in queue for site1
2025-06-15 21:22:57,379 ERROR scheduler Skipped queueing hrms.hr.utils.allocate_earned_leaves because it was found in queue for site1
2025-06-15 21:22:57,382 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for site1
2025-06-15 21:22:57,390 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.remove_unverified_record because it was found in queue for site1
2025-06-15 21:22:57,394 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for site1
2025-06-15 21:22:57,408 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_drive.google_drive.weekly_backup because it was found in queue for site1
2025-06-15 21:22:57,413 ERROR scheduler Skipped queueing hrms.hr.utils.generate_leave_encashment because it was found in queue for site1
2025-06-15 21:22:57,423 ERROR scheduler Skipped queueing frappe.integrations.doctype.s3_backup_settings.s3_backup_settings.take_backups_daily because it was found in queue for site1
2025-06-15 21:22:57,429 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for site1
2025-06-15 21:23:58,561 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.pull because it was found in queue for site1
2025-06-15 21:23:58,565 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for site1
2025-06-15 21:23:58,568 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for site1
2025-06-15 21:23:58,579 ERROR scheduler Skipped queueing frappe.sessions.clear_expired_sessions because it was found in queue for site1
2025-06-15 21:23:58,585 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_calendar.google_calendar.sync because it was found in queue for site1
2025-06-15 21:23:58,591 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.process_auto_attendance_for_all_shifts because it was found in queue for site1
2025-06-15 21:23:58,595 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for site1
2025-06-15 21:23:58,601 ERROR scheduler Skipped queueing hrms.hr.utils.generate_leave_encashment because it was found in queue for site1
2025-06-15 21:23:58,604 ERROR scheduler Skipped queueing frappe.monitor.flush because it was found in queue for site1
2025-06-15 21:23:58,608 ERROR scheduler Skipped queueing frappe.integrations.doctype.dropbox_settings.dropbox_settings.take_backups_weekly because it was found in queue for site1
2025-06-15 21:23:58,611 ERROR scheduler Skipped queueing erpnext.assets.doctype.asset.depreciation.post_depreciation_entries because it was found in queue for site1
2025-06-15 21:23:58,616 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for site1
2025-06-15 21:23:58,631 ERROR scheduler Skipped queueing frappe.integrations.doctype.dropbox_settings.dropbox_settings.take_backups_daily because it was found in queue for site1
2025-06-15 21:23:58,635 ERROR scheduler Skipped queueing hrms.hr.doctype.interview.interview.send_interview_reminder because it was found in queue for site1
2025-06-15 21:23:58,639 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for site1
2025-06-15 21:23:58,643 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.remove_unverified_record because it was found in queue for site1
2025-06-15 21:23:58,654 ERROR scheduler Skipped queueing frappe.email.doctype.auto_email_report.auto_email_report.send_daily because it was found in queue for site1
2025-06-15 21:23:58,669 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for site1
2025-06-15 21:23:58,673 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for site1
2025-06-15 21:23:58,679 ERROR scheduler Skipped queueing frappe.utils.change_log.check_for_update because it was found in queue for site1
2025-06-15 21:23:58,694 ERROR scheduler Skipped queueing frappe.social.doctype.energy_point_log.energy_point_log.send_weekly_summary because it was found in queue for site1
2025-06-15 21:23:58,707 ERROR scheduler Skipped queueing hrms.hr.utils.allocate_earned_leaves because it was found in queue for site1
2025-06-15 21:23:58,727 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for site1
2025-06-15 21:23:58,739 ERROR scheduler Skipped queueing frappe.integrations.doctype.s3_backup_settings.s3_backup_settings.take_backups_weekly because it was found in queue for site1
2025-06-15 21:23:58,743 ERROR scheduler Skipped queueing erpnext.crm.utils.open_leads_opportunities_based_on_todays_event because it was found in queue for site1
2025-06-15 21:23:58,749 ERROR scheduler Skipped queueing erpnext.manufacturing.doctype.bom_update_tool.bom_update_tool.auto_update_latest_price_in_all_boms because it was found in queue for site1
2025-06-15 21:23:58,757 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_contacts.google_contacts.sync because it was found in queue for site1
2025-06-15 21:23:58,761 ERROR scheduler Skipped queueing frappe.desk.doctype.changelog_feed.changelog_feed.fetch_changelog_feed because it was found in queue for site1
2025-06-15 21:23:58,775 ERROR scheduler Skipped queueing erpnext.setup.doctype.email_digest.email_digest.send because it was found in queue for site1
2025-06-15 21:23:58,781 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for site1
2025-06-15 21:23:58,793 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_weekly_updates because it was found in queue for site1
2025-06-15 21:23:58,797 ERROR scheduler Skipped queueing frappe.automation.doctype.auto_repeat.auto_repeat.make_auto_repeat_entry because it was found in queue for site1
2025-06-15 21:23:58,801 ERROR scheduler Skipped queueing erpnext.accounts.doctype.process_subscription.process_subscription.create_subscription_process because it was found in queue for site1
2025-06-15 21:23:58,804 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_drive.google_drive.weekly_backup because it was found in queue for site1
2025-06-15 21:23:58,810 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for site1
2025-06-15 21:23:58,816 ERROR scheduler Skipped queueing frappe.email.queue.flush because it was found in queue for site1
2025-06-15 21:23:58,826 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_schedule_assignment.shift_schedule_assignment.process_auto_shift_creation because it was found in queue for site1
2025-06-15 21:23:58,830 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_drive.google_drive.daily_backup because it was found in queue for site1
2025-06-15 21:23:58,841 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.update_last_sync_of_checkin because it was found in queue for site1
2025-06-15 21:23:58,847 ERROR scheduler Skipped queueing hrms.hr.doctype.leave_ledger_entry.leave_ledger_entry.process_expired_allocation because it was found in queue for site1
2025-06-15 21:23:58,851 ERROR scheduler Skipped queueing frappe.social.doctype.energy_point_settings.energy_point_settings.allocate_review_points because it was found in queue for site1
2025-06-15 21:23:58,859 ERROR scheduler Skipped queueing frappe.desk.notifications.clear_notifications because it was found in queue for site1
2025-06-15 21:23:58,863 ERROR scheduler Skipped queueing frappe.core.doctype.log_settings.log_settings.run_log_clean_up because it was found in queue for site1
2025-06-15 21:23:58,867 ERROR scheduler Skipped queueing frappe.integrations.doctype.s3_backup_settings.s3_backup_settings.take_backups_daily because it was found in queue for site1
2025-06-15 21:31:06,877 ERROR scheduler Skipped queueing erpnext.accounts.doctype.gl_entry.gl_entry.rename_gle_sle_docs because it was found in queue for site1
2025-06-15 21:31:06,937 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.notify_unreplied because it was found in queue for site1
2025-06-15 21:31:06,953 ERROR scheduler Skipped queueing frappe.deferred_insert.save_to_db because it was found in queue for site1
2025-06-15 21:31:06,978 ERROR scheduler Skipped queueing csf_tz.stanbic.sftp.process_download_files because it was found in queue for site1
2025-06-15 21:31:06,982 ERROR scheduler Skipped queueing erpnext.manufacturing.doctype.bom_update_log.bom_update_log.resume_bom_cost_update_jobs because it was found in queue for site1
2025-06-15 21:31:06,994 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.pull because it was found in queue for site1
2025-06-15 21:31:07,003 ERROR scheduler Skipped queueing erpnext.utilities.doctype.video.video.update_youtube_data because it was found in queue for site1
2025-06-15 21:31:07,008 ERROR scheduler Skipped queueing csf_tz.stanbic.sftp.sync_all_stanbank_files because it was found in queue for site1
2025-06-15 21:31:07,025 ERROR scheduler Skipped queueing csf_tz.csftz_hooks.items_revaluation.process_incorrect_balance_qty because it was found in queue for site1
2025-06-15 21:31:07,035 ERROR scheduler Skipped queueing frappe.utils.global_search.sync_global_search because it was found in queue for site1
2025-06-15 21:31:07,044 ERROR scheduler Skipped queueing frappe.model.utils.link_count.update_link_count because it was found in queue for site1
2025-06-15 21:31:07,099 ERROR scheduler Skipped queueing frappe.automation.doctype.reminder.reminder.send_reminders because it was found in queue for site1
2025-06-15 22:01:44,861 ERROR scheduler Skipped queueing erpnext.manufacturing.doctype.bom_update_log.bom_update_log.resume_bom_cost_update_jobs because it was found in queue for site1
2025-06-15 22:01:44,882 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for site1
2025-06-15 22:01:44,894 ERROR scheduler Skipped queueing csf_tz.csf_tz.doctype.vehicle_fine_record.vehicle_fine_record.check_fine_all_vehicles because it was found in queue for site1
2025-06-15 22:01:44,905 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_calendar.google_calendar.sync because it was found in queue for site1
2025-06-15 22:01:44,915 ERROR scheduler Skipped queueing frappe.deferred_insert.save_to_db because it was found in queue for site1
2025-06-15 22:01:44,976 ERROR scheduler Skipped queueing csf_tz.stanbic.sftp.process_download_files because it was found in queue for site1
2025-06-15 22:01:45,027 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for site1
2025-06-15 22:01:45,034 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for site1
2025-06-15 22:01:45,061 ERROR scheduler Skipped queueing hrms.hr.doctype.daily_work_summary_group.daily_work_summary_group.trigger_emails because it was found in queue for site1
2025-06-15 22:01:45,070 ERROR scheduler Skipped queueing csf_tz.csftz_hooks.items_revaluation.process_incorrect_balance_qty because it was found in queue for site1
2025-06-15 22:01:45,094 ERROR scheduler Skipped queueing frappe.utils.global_search.sync_global_search because it was found in queue for site1
2025-06-15 22:01:45,103 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for site1
2025-06-15 22:01:45,108 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for site1
2025-06-15 22:01:45,124 ERROR scheduler Skipped queueing frappe.monitor.flush because it was found in queue for site1
2025-06-15 22:01:45,131 ERROR scheduler Skipped queueing erpnext.projects.doctype.project.project.collect_project_status because it was found in queue for site1
2025-06-15 22:01:45,135 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for site1
2025-06-15 22:01:45,144 ERROR scheduler Skipped queueing hrms.hr.doctype.interview.interview.send_interview_reminder because it was found in queue for site1
2025-06-15 22:01:45,149 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.process_auto_attendance_for_all_shifts because it was found in queue for site1
2025-06-15 22:01:45,159 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.pull because it was found in queue for site1
2025-06-15 22:01:45,170 ERROR scheduler Skipped queueing erpnext.erpnext_integrations.doctype.plaid_settings.plaid_settings.automatic_synchronization because it was found in queue for site1
2025-06-15 22:01:45,191 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.notify_unreplied because it was found in queue for site1
2025-06-15 22:01:45,203 ERROR scheduler Skipped queueing frappe.automation.doctype.reminder.reminder.send_reminders because it was found in queue for site1
2025-06-15 22:01:45,207 ERROR scheduler Skipped queueing frappe.model.utils.link_count.update_link_count because it was found in queue for site1
2025-06-15 22:01:45,215 ERROR scheduler Skipped queueing frappe.email.queue.flush because it was found in queue for site1
2025-06-15 22:01:45,221 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for site1
2025-06-15 22:01:45,227 ERROR scheduler Skipped queueing erpnext.utilities.doctype.video.video.update_youtube_data because it was found in queue for site1
2025-06-15 22:01:45,235 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for site1
2025-06-15 22:01:45,244 ERROR scheduler Skipped queueing frappe.email.doctype.newsletter.newsletter.send_scheduled_email because it was found in queue for site1
2025-06-15 22:01:45,251 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.update_last_sync_of_checkin because it was found in queue for site1
2025-06-15 22:01:45,261 ERROR scheduler Skipped queueing erpnext.projects.doctype.project.project.project_status_update_reminder because it was found in queue for site1
2025-06-15 22:01:45,268 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for site1
2025-06-15 22:01:45,272 ERROR scheduler Skipped queueing csf_tz.stanbic.sftp.sync_all_stanbank_files because it was found in queue for site1
2025-06-15 22:01:45,277 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for site1
2025-06-15 22:01:45,281 ERROR scheduler Skipped queueing erpnext.projects.doctype.project.project.hourly_reminder because it was found in queue for site1
2025-06-15 22:01:45,285 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_schedule_assignment.shift_schedule_assignment.process_auto_shift_creation because it was found in queue for site1
2025-06-15 22:02:46,279 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for site1
2025-06-15 22:02:46,302 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for site1
2025-06-15 22:02:46,310 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for site1
2025-06-15 22:02:46,319 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for site1
2025-06-15 22:02:46,327 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.update_last_sync_of_checkin because it was found in queue for site1
2025-06-15 22:02:46,361 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for site1
2025-06-15 22:02:46,368 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for site1
2025-06-15 22:02:46,406 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for site1
2025-06-15 22:02:46,417 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for site1
2025-06-15 22:02:46,422 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.process_auto_attendance_for_all_shifts because it was found in queue for site1
2025-06-15 22:02:46,462 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for site1
2025-06-15 22:02:46,470 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_schedule_assignment.shift_schedule_assignment.process_auto_shift_creation because it was found in queue for site1
2025-06-15 22:02:46,510 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for site1
2025-06-15 22:03:48,489 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for site1
2025-06-15 22:03:48,519 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for site1
2025-06-15 22:03:48,526 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for site1
2025-06-15 22:03:48,539 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for site1
2025-06-15 22:03:48,579 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.process_auto_attendance_for_all_shifts because it was found in queue for site1
2025-06-15 22:03:48,603 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for site1
2025-06-15 22:03:48,612 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for site1
2025-06-15 22:03:48,625 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for site1
2025-06-15 22:03:48,645 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.update_last_sync_of_checkin because it was found in queue for site1
2025-06-15 22:03:48,651 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_schedule_assignment.shift_schedule_assignment.process_auto_shift_creation because it was found in queue for site1
2025-06-15 22:03:48,664 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for site1
2025-06-15 22:03:48,698 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for site1
2025-06-15 22:03:48,716 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for site1
2025-06-15 22:04:49,535 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for site1
2025-06-15 22:04:49,591 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for site1
2025-06-15 22:04:49,596 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.update_last_sync_of_checkin because it was found in queue for site1
2025-06-15 22:04:49,606 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for site1
2025-06-15 22:04:49,612 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for site1
2025-06-15 22:04:49,622 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for site1
2025-06-15 22:04:49,654 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.process_auto_attendance_for_all_shifts because it was found in queue for site1
2025-06-15 22:04:49,685 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_schedule_assignment.shift_schedule_assignment.process_auto_shift_creation because it was found in queue for site1
2025-06-15 22:04:49,702 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for site1
2025-06-15 22:04:49,728 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for site1
2025-06-15 22:04:49,752 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for site1
2025-06-15 22:04:49,762 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for site1
2025-06-15 22:04:49,771 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for site1
2025-06-15 22:05:50,136 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for site1
2025-06-15 22:05:50,171 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for site1
2025-06-15 22:05:50,223 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for site1
2025-06-15 22:05:50,236 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for site1
2025-06-15 22:05:50,245 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.process_auto_attendance_for_all_shifts because it was found in queue for site1
2025-06-15 22:05:50,249 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for site1
2025-06-15 22:05:50,313 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for site1
2025-06-15 22:05:50,337 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for site1
2025-06-15 22:05:50,341 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for site1
2025-06-15 22:05:50,366 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_schedule_assignment.shift_schedule_assignment.process_auto_shift_creation because it was found in queue for site1
2025-06-15 22:05:50,375 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for site1
2025-06-15 22:05:50,387 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.update_last_sync_of_checkin because it was found in queue for site1
2025-06-15 22:05:50,391 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for site1
2025-06-15 22:06:51,967 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.process_auto_attendance_for_all_shifts because it was found in queue for site1
2025-06-15 22:06:51,995 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for site1
2025-06-15 22:06:51,998 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for site1
2025-06-15 22:06:52,006 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.update_last_sync_of_checkin because it was found in queue for site1
2025-06-15 22:06:52,023 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for site1
2025-06-15 22:06:52,083 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_schedule_assignment.shift_schedule_assignment.process_auto_shift_creation because it was found in queue for site1
2025-06-15 22:06:52,098 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for site1
2025-06-15 22:06:52,102 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for site1
2025-06-15 22:06:52,107 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for site1
2025-06-15 22:06:52,123 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for site1
2025-06-15 22:06:52,152 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for site1
2025-06-15 22:06:52,187 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for site1
2025-06-15 22:06:52,190 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for site1
2025-06-15 22:07:52,576 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for site1
2025-06-15 22:07:52,578 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for site1
2025-06-15 22:07:52,624 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for site1
2025-06-15 22:07:52,664 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for site1
2025-06-15 22:07:52,714 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.process_auto_attendance_for_all_shifts because it was found in queue for site1
2025-06-15 22:07:52,725 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.update_last_sync_of_checkin because it was found in queue for site1
2025-06-15 22:07:52,742 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for site1
2025-06-15 22:07:52,758 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for site1
2025-06-15 22:07:52,779 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for site1
2025-06-15 22:07:52,804 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for site1
2025-06-15 22:07:52,807 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_schedule_assignment.shift_schedule_assignment.process_auto_shift_creation because it was found in queue for site1
2025-06-15 22:07:52,811 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for site1
2025-06-15 22:07:52,818 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for site1
2025-06-15 22:08:53,642 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.update_last_sync_of_checkin because it was found in queue for site1
2025-06-15 22:08:53,662 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for site1
2025-06-15 22:08:53,694 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for site1
2025-06-15 22:08:53,726 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_schedule_assignment.shift_schedule_assignment.process_auto_shift_creation because it was found in queue for site1
2025-06-15 22:08:53,737 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for site1
2025-06-15 22:08:53,750 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for site1
2025-06-15 22:08:53,788 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for site1
2025-06-15 22:08:53,828 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for site1
2025-06-15 22:08:53,836 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.process_auto_attendance_for_all_shifts because it was found in queue for site1
2025-06-15 22:08:53,847 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for site1
2025-06-15 22:08:53,854 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for site1
2025-06-15 22:08:53,865 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for site1
2025-06-15 22:08:53,877 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for site1
2025-06-15 22:09:54,756 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for site1
2025-06-15 22:09:54,759 ERROR scheduler Skipped queueing hrms.hr.doctype.interview.interview.send_interview_reminder because it was found in queue for site1
2025-06-15 22:09:54,766 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.process_auto_attendance_for_all_shifts because it was found in queue for site1
2025-06-15 22:09:54,788 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for site1
2025-06-15 22:09:54,804 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for site1
2025-06-15 22:09:54,832 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_schedule_assignment.shift_schedule_assignment.process_auto_shift_creation because it was found in queue for site1
2025-06-15 22:09:54,843 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for site1
2025-06-15 22:09:54,856 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for site1
2025-06-15 22:09:54,860 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for site1
2025-06-15 22:09:54,880 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_calendar.google_calendar.sync because it was found in queue for site1
2025-06-15 22:09:54,885 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.update_last_sync_of_checkin because it was found in queue for site1
2025-06-15 22:09:54,938 ERROR scheduler Skipped queueing frappe.monitor.flush because it was found in queue for site1
2025-06-15 22:09:54,942 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for site1
2025-06-15 22:09:54,954 ERROR scheduler Skipped queueing frappe.email.queue.flush because it was found in queue for site1
2025-06-15 22:09:54,958 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for site1
2025-06-15 22:09:54,973 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for site1
2025-06-15 22:09:54,977 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for site1
2025-06-15 22:11:58,119 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.pull because it was found in queue for site1
2025-06-15 22:16:02,813 ERROR scheduler Skipped queueing csf_tz.stanbic.sftp.process_download_files because it was found in queue for site1
2025-06-15 22:16:02,867 ERROR scheduler Skipped queueing frappe.utils.global_search.sync_global_search because it was found in queue for site1
2025-06-15 22:16:02,897 ERROR scheduler Skipped queueing erpnext.manufacturing.doctype.bom_update_log.bom_update_log.resume_bom_cost_update_jobs because it was found in queue for site1
2025-06-15 22:16:02,922 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.notify_unreplied because it was found in queue for site1
2025-06-15 22:16:02,942 ERROR scheduler Skipped queueing frappe.deferred_insert.save_to_db because it was found in queue for site1
2025-06-15 22:16:02,963 ERROR scheduler Skipped queueing frappe.automation.doctype.reminder.reminder.send_reminders because it was found in queue for site1
2025-06-15 22:16:03,038 ERROR scheduler Skipped queueing frappe.model.utils.link_count.update_link_count because it was found in queue for site1
2025-06-15 22:16:03,044 ERROR scheduler Skipped queueing csf_tz.stanbic.sftp.sync_all_stanbank_files because it was found in queue for site1
2025-06-15 22:16:03,049 ERROR scheduler Skipped queueing csf_tz.csftz_hooks.items_revaluation.process_incorrect_balance_qty because it was found in queue for site1
2025-06-16 08:44:48,832 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.update_last_sync_of_checkin because it was found in queue for site1
2025-06-16 08:44:48,836 ERROR scheduler Skipped queueing frappe.desk.doctype.event.event.send_event_digest because it was found in queue for site1
2025-06-16 08:44:48,840 ERROR scheduler Skipped queueing frappe.integrations.doctype.s3_backup_settings.s3_backup_settings.take_backups_daily because it was found in queue for site1
2025-06-16 08:44:48,845 ERROR scheduler Skipped queueing erpnext.accounts.utils.run_ledger_health_checks because it was found in queue for site1
2025-06-16 08:44:48,848 ERROR scheduler Skipped queueing hrms.hr.utils.allocate_earned_leaves because it was found in queue for site1
2025-06-16 08:44:48,852 ERROR scheduler Skipped queueing csf_tz.csf_tz.doctype.vehicle_fine_record.vehicle_fine_record.check_fine_all_vehicles because it was found in queue for site1
2025-06-16 08:44:48,858 ERROR scheduler Skipped queueing frappe.email.queue.flush because it was found in queue for site1
2025-06-16 08:44:48,865 ERROR scheduler Skipped queueing hrms.hr.doctype.job_opening.job_opening.close_expired_job_openings because it was found in queue for site1
2025-06-16 08:44:48,869 ERROR scheduler Skipped queueing erpnext.manufacturing.doctype.bom_update_tool.bom_update_tool.auto_update_latest_price_in_all_boms because it was found in queue for site1
2025-06-16 08:44:48,877 ERROR scheduler Skipped queueing erpnext.setup.doctype.company.company.cache_companies_monthly_sales_history because it was found in queue for site1
2025-06-16 08:44:48,881 ERROR scheduler Skipped queueing hrms.hr.doctype.daily_work_summary_group.daily_work_summary_group.trigger_emails because it was found in queue for site1
2025-06-16 08:44:48,884 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for site1
2025-06-16 08:44:48,888 ERROR scheduler Skipped queueing erpnext.controllers.accounts_controller.update_invoice_status because it was found in queue for site1
2025-06-16 08:44:48,891 ERROR scheduler Skipped queueing erpnext.projects.doctype.project.project.project_status_update_reminder because it was found in queue for site1
2025-06-16 08:44:48,894 ERROR scheduler Skipped queueing hrms.controllers.employee_reminders.send_birthday_reminders because it was found in queue for site1
2025-06-16 08:44:48,897 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_schedule_assignment.shift_schedule_assignment.process_auto_shift_creation because it was found in queue for site1
2025-06-16 08:44:48,901 ERROR scheduler Skipped queueing frappe.email.doctype.newsletter.newsletter.send_scheduled_email because it was found in queue for site1
2025-06-16 08:44:48,904 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for site1
2025-06-16 08:44:48,907 ERROR scheduler Skipped queueing erpnext.crm.doctype.email_campaign.email_campaign.send_email_to_leads_or_contacts because it was found in queue for site1
2025-06-16 08:44:48,911 ERROR scheduler Skipped queueing erpnext.projects.doctype.task.task.set_tasks_as_overdue because it was found in queue for site1
2025-06-16 08:44:48,915 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.remove_unverified_record because it was found in queue for site1
2025-06-16 08:44:48,919 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.pull because it was found in queue for site1
2025-06-16 08:44:48,924 ERROR scheduler Skipped queueing erpnext.crm.doctype.email_campaign.email_campaign.set_email_campaign_status because it was found in queue for site1
2025-06-16 08:44:48,927 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.process_auto_attendance_for_all_shifts because it was found in queue for site1
2025-06-16 08:44:48,930 ERROR scheduler Skipped queueing erpnext.crm.doctype.contract.contract.update_status_for_contracts because it was found in queue for site1
2025-06-16 08:44:48,934 ERROR scheduler Skipped queueing erpnext.assets.doctype.asset_maintenance_log.asset_maintenance_log.update_asset_maintenance_log_status because it was found in queue for site1
2025-06-16 08:44:48,939 ERROR scheduler Skipped queueing erpnext.accounts.doctype.fiscal_year.fiscal_year.auto_create_fiscal_year because it was found in queue for site1
2025-06-16 08:44:48,942 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_drive.google_drive.daily_backup because it was found in queue for site1
2025-06-16 08:44:48,946 ERROR scheduler Skipped queueing propms.auto_custom.statusChangeBeforeLeaseExpire because it was found in queue for site1
2025-06-16 08:44:48,950 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.notify_unreplied because it was found in queue for site1
2025-06-16 08:44:48,954 ERROR scheduler Skipped queueing erpnext.buying.doctype.supplier_quotation.supplier_quotation.set_expired_status because it was found in queue for site1
2025-06-16 08:44:48,959 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for site1
2025-06-16 08:44:48,965 ERROR scheduler Skipped queueing erpnext.assets.doctype.asset.asset.make_post_gl_entry because it was found in queue for site1
2025-06-16 08:44:48,968 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_contacts.google_contacts.sync because it was found in queue for site1
2025-06-16 08:44:48,972 ERROR scheduler Skipped queueing erpnext.accounts.doctype.process_subscription.process_subscription.create_subscription_process because it was found in queue for site1
2025-06-16 08:44:48,976 ERROR scheduler Skipped queueing hrms.hr.doctype.leave_ledger_entry.leave_ledger_entry.process_expired_allocation because it was found in queue for site1
2025-06-16 08:44:48,982 ERROR scheduler Skipped queueing erpnext.crm.utils.open_leads_opportunities_based_on_todays_event because it was found in queue for site1
2025-06-16 08:44:48,987 ERROR scheduler Skipped queueing erpnext.projects.doctype.project.project.collect_project_status because it was found in queue for site1
2025-06-16 08:44:48,991 ERROR scheduler Skipped queueing erpnext.support.doctype.issue.issue.auto_close_tickets because it was found in queue for site1
2025-06-16 08:44:48,995 ERROR scheduler Skipped queueing erpnext.utilities.doctype.video.video.update_youtube_data because it was found in queue for site1
2025-06-16 08:44:48,999 ERROR scheduler Skipped queueing erpnext.assets.doctype.asset.asset.update_maintenance_status because it was found in queue for site1
2025-06-16 08:44:49,005 ERROR scheduler Skipped queueing erpnext.selling.doctype.quotation.quotation.set_expired_status because it was found in queue for site1
2025-06-16 08:44:49,010 ERROR scheduler Skipped queueing frappe.monitor.flush because it was found in queue for site1
2025-06-16 08:44:49,014 ERROR scheduler Skipped queueing frappe.email.doctype.auto_email_report.auto_email_report.send_daily because it was found in queue for site1
2025-06-16 08:44:49,017 ERROR scheduler Skipped queueing csf_tz.csftz_hooks.material_request.auto_close_material_request because it was found in queue for site1
2025-06-16 08:44:49,026 ERROR scheduler Skipped queueing csf_tz.custom_api.create_delivery_note_for_all_pending_sales_invoice because it was found in queue for site1
2025-06-16 08:44:49,029 ERROR scheduler Skipped queueing erpnext.stock.doctype.serial_no.serial_no.update_maintenance_status because it was found in queue for site1
2025-06-16 08:44:49,033 ERROR scheduler Skipped queueing erpnext.stock.reorder_item.reorder_item because it was found in queue for site1
2025-06-16 08:44:49,038 ERROR scheduler Skipped queueing erpnext.buying.doctype.supplier_scorecard.supplier_scorecard.refresh_scorecards because it was found in queue for site1
2025-06-16 08:44:49,043 ERROR scheduler Skipped queueing hrms.hr.doctype.daily_work_summary_group.daily_work_summary_group.send_summary because it was found in queue for site1
2025-06-16 08:44:49,047 ERROR scheduler Skipped queueing erpnext.quality_management.doctype.quality_review.quality_review.review because it was found in queue for site1
2025-06-16 08:44:49,050 ERROR scheduler Skipped queueing csf_tz.csf_tz.doctype.parking_bill.parking_bill.check_bills_all_vehicles because it was found in queue for site1
2025-06-16 08:44:49,055 ERROR scheduler Skipped queueing frappe.model.utils.link_count.update_link_count because it was found in queue for site1
2025-06-16 08:44:49,059 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_calendar.google_calendar.sync because it was found in queue for site1
2025-06-16 08:44:49,068 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for site1
2025-06-16 08:44:49,073 ERROR scheduler Skipped queueing csf_tz.bank_api.reconciliation because it was found in queue for site1
2025-06-16 08:44:49,079 ERROR scheduler Skipped queueing csf_tz.custom_api.auto_close_dn because it was found in queue for site1
2025-06-16 08:44:49,083 ERROR scheduler Skipped queueing frappe.utils.global_search.sync_global_search because it was found in queue for site1
2025-06-16 08:44:49,088 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for site1
2025-06-16 08:44:49,092 ERROR scheduler Skipped queueing erpnext.projects.doctype.project.project.update_project_sales_billing because it was found in queue for site1
2025-06-16 08:44:49,096 ERROR scheduler Skipped queueing erpnext.accounts.doctype.process_statement_of_accounts.process_statement_of_accounts.send_auto_email because it was found in queue for site1
2025-06-16 08:44:49,100 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for site1
2025-06-16 08:44:49,108 ERROR scheduler Skipped queueing frappe.social.doctype.energy_point_settings.energy_point_settings.allocate_review_points because it was found in queue for site1
2025-06-16 08:44:49,112 ERROR scheduler Skipped queueing frappe.sessions.clear_expired_sessions because it was found in queue for site1
2025-06-16 08:44:49,115 ERROR scheduler Skipped queueing erpnext.crm.doctype.opportunity.opportunity.auto_close_opportunity because it was found in queue for site1
2025-06-16 08:44:49,118 ERROR scheduler Skipped queueing frappe.deferred_insert.save_to_db because it was found in queue for site1
2025-06-16 08:44:49,123 ERROR scheduler Skipped queueing erpnext.projects.doctype.project.project.hourly_reminder because it was found in queue for site1
2025-06-16 08:44:49,127 ERROR scheduler Skipped queueing csf_tz.csftz_hooks.additional_salary.generate_additional_salary_records because it was found in queue for site1
2025-06-16 08:44:49,132 ERROR scheduler Skipped queueing csf_tz.stanbic.sftp.sync_all_stanbank_files because it was found in queue for site1
2025-06-16 08:44:49,136 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for site1
2025-06-16 08:44:49,140 ERROR scheduler Skipped queueing csf_tz.csf_tz.doctype.visibility.visibility.trigger_daily_alerts because it was found in queue for site1
2025-06-16 08:44:49,144 ERROR scheduler Skipped queueing hrms.hr.utils.generate_leave_encashment because it was found in queue for site1
2025-06-16 08:44:49,148 ERROR scheduler Skipped queueing erpnext.setup.doctype.email_digest.email_digest.send because it was found in queue for site1
2025-06-16 08:44:49,151 ERROR scheduler Skipped queueing frappe.core.doctype.log_settings.log_settings.run_log_clean_up because it was found in queue for site1
2025-06-16 08:44:49,156 ERROR scheduler Skipped queueing csf_tz.stanbic.sftp.process_download_files because it was found in queue for site1
2025-06-16 08:44:49,162 ERROR scheduler Skipped queueing hrms.hr.doctype.interview.interview.send_interview_reminder because it was found in queue for site1
2025-06-16 08:44:49,166 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for site1
2025-06-16 08:44:49,173 ERROR scheduler Skipped queueing frappe.desk.notifications.clear_notifications because it was found in queue for site1
2025-06-16 08:44:49,178 ERROR scheduler Skipped queueing erpnext.erpnext_integrations.doctype.plaid_settings.plaid_settings.automatic_synchronization because it was found in queue for site1
2025-06-16 08:44:49,184 ERROR scheduler Skipped queueing erpnext.projects.doctype.project.project.send_project_status_email_to_users because it was found in queue for site1
2025-06-16 08:44:49,188 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_daily_updates because it was found in queue for site1
2025-06-16 08:44:49,192 ERROR scheduler Skipped queueing erpnext.support.doctype.service_level_agreement.service_level_agreement.check_agreement_status because it was found in queue for site1
2025-06-16 08:44:49,196 ERROR scheduler Skipped queueing propms.auto_custom.statusChangeAfterLeaseExpire because it was found in queue for site1
2025-06-16 08:44:49,199 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for site1
2025-06-16 08:44:49,205 ERROR scheduler Skipped queueing erpnext.accounts.doctype.gl_entry.gl_entry.rename_gle_sle_docs because it was found in queue for site1
2025-06-16 08:44:49,209 ERROR scheduler Skipped queueing erpnext.assets.doctype.asset.depreciation.post_depreciation_entries because it was found in queue for site1
2025-06-16 08:44:49,213 ERROR scheduler Skipped queueing frappe.automation.doctype.reminder.reminder.send_reminders because it was found in queue for site1
2025-06-16 08:44:49,218 ERROR scheduler Skipped queueing erpnext.manufacturing.doctype.bom_update_log.bom_update_log.resume_bom_cost_update_jobs because it was found in queue for site1
2025-06-16 08:44:49,222 ERROR scheduler Skipped queueing hrms.controllers.employee_reminders.send_work_anniversary_reminders because it was found in queue for site1
2025-06-16 08:44:49,227 ERROR scheduler Skipped queueing frappe.email.doctype.notification.notification.trigger_daily_alerts because it was found in queue for site1
2025-06-16 08:44:49,233 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for site1
2025-06-16 08:44:49,237 ERROR scheduler Skipped queueing hrms.hr.doctype.interview.interview.send_daily_feedback_reminder because it was found in queue for site1
2025-06-16 08:44:49,240 ERROR scheduler Skipped queueing propms.lease_invoice_schedule.make_lease_invoice_schedule because it was found in queue for site1
2025-06-16 08:44:49,244 ERROR scheduler Skipped queueing frappe.automation.doctype.auto_repeat.auto_repeat.make_auto_repeat_entry because it was found in queue for site1
2025-06-16 08:44:49,247 ERROR scheduler Skipped queueing erpnext.accounts.utils.auto_create_exchange_rate_revaluation_daily because it was found in queue for site1
2025-06-16 08:44:49,251 ERROR scheduler Skipped queueing csf_tz.csftz_hooks.items_revaluation.process_incorrect_balance_qty because it was found in queue for site1
2025-06-16 08:44:49,254 ERROR scheduler Skipped queueing frappe.integrations.doctype.dropbox_settings.dropbox_settings.take_backups_daily because it was found in queue for site1
2025-06-16 08:45:50,558 ERROR scheduler Skipped queueing erpnext.projects.doctype.project.project.hourly_reminder because it was found in queue for site1
2025-06-16 08:45:50,562 ERROR scheduler Skipped queueing hrms.hr.doctype.job_opening.job_opening.close_expired_job_openings because it was found in queue for site1
2025-06-16 08:45:50,566 ERROR scheduler Skipped queueing csf_tz.csftz_hooks.material_request.auto_close_material_request because it was found in queue for site1
2025-06-16 08:45:50,570 ERROR scheduler Skipped queueing frappe.desk.doctype.event.event.send_event_digest because it was found in queue for site1
2025-06-16 08:45:50,573 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_daily_updates because it was found in queue for site1
2025-06-16 08:45:50,576 ERROR scheduler Skipped queueing erpnext.crm.utils.open_leads_opportunities_based_on_todays_event because it was found in queue for site1
2025-06-16 08:45:50,580 ERROR scheduler Skipped queueing erpnext.controllers.accounts_controller.update_invoice_status because it was found in queue for site1
2025-06-16 08:45:50,584 ERROR scheduler Skipped queueing erpnext.support.doctype.service_level_agreement.service_level_agreement.check_agreement_status because it was found in queue for site1
2025-06-16 08:45:50,587 ERROR scheduler Skipped queueing erpnext.accounts.doctype.process_subscription.process_subscription.create_subscription_process because it was found in queue for site1
2025-06-16 08:45:50,590 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_drive.google_drive.daily_backup because it was found in queue for site1
2025-06-16 08:45:50,594 ERROR scheduler Skipped queueing csf_tz.stanbic.sftp.process_download_files because it was found in queue for site1
