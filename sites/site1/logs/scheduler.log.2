2025-06-23 09:07:05,137 ERROR scheduler Skipped queueing frappe.integrations.doctype.s3_backup_settings.s3_backup_settings.take_backups_weekly because it was found in queue for site1
2025-06-23 09:07:05,140 ERROR scheduler Skipped queueing frappe.desk.notifications.clear_notifications because it was found in queue for site1
2025-06-23 09:07:05,159 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for site1
2025-06-23 09:07:05,164 ERROR scheduler Skipped queueing frappe.desk.doctype.changelog_feed.changelog_feed.fetch_changelog_feed because it was found in queue for site1
2025-06-23 09:07:05,173 ERROR scheduler Skipped queueing frappe.sessions.clear_expired_sessions because it was found in queue for site1
2025-06-23 09:07:05,176 ERROR scheduler Skipped queueing frappe.integrations.doctype.s3_backup_settings.s3_backup_settings.take_backups_daily because it was found in queue for site1
2025-06-23 09:08:06,099 ERROR scheduler Skipped queueing erpnext.accounts.doctype.process_subscription.process_subscription.create_subscription_process because it was found in queue for site1
2025-06-23 09:08:06,111 ERROR scheduler Skipped queueing hrms.hr.utils.allocate_earned_leaves because it was found in queue for site1
2025-06-23 09:08:06,123 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for site1
2025-06-23 09:08:06,138 ERROR scheduler Skipped queueing frappe.sessions.clear_expired_sessions because it was found in queue for site1
2025-06-23 09:08:06,146 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for site1
2025-06-23 09:08:06,150 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_weekly_updates because it was found in queue for site1
2025-06-23 09:08:06,162 ERROR scheduler Skipped queueing frappe.integrations.doctype.s3_backup_settings.s3_backup_settings.take_backups_daily because it was found in queue for site1
2025-06-23 09:08:06,166 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.process_auto_attendance_for_all_shifts because it was found in queue for site1
2025-06-23 09:08:06,176 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for site1
2025-06-23 09:08:06,180 ERROR scheduler Skipped queueing erpnext.assets.doctype.asset.depreciation.post_depreciation_entries because it was found in queue for site1
2025-06-23 09:08:06,187 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.remove_unverified_record because it was found in queue for site1
2025-06-23 09:08:06,190 ERROR scheduler Skipped queueing erpnext.manufacturing.doctype.bom_update_tool.bom_update_tool.auto_update_latest_price_in_all_boms because it was found in queue for site1
2025-06-23 09:08:06,197 ERROR scheduler Skipped queueing frappe.social.doctype.energy_point_settings.energy_point_settings.allocate_review_points because it was found in queue for site1
2025-06-23 09:08:06,200 ERROR scheduler Skipped queueing frappe.utils.change_log.check_for_update because it was found in queue for site1
2025-06-23 09:08:06,206 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_drive.google_drive.weekly_backup because it was found in queue for site1
2025-06-23 09:08:06,208 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_schedule_assignment.shift_schedule_assignment.process_auto_shift_creation because it was found in queue for site1
2025-06-23 09:08:06,214 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for site1
2025-06-23 09:08:06,217 ERROR scheduler Skipped queueing hrms.hr.utils.generate_leave_encashment because it was found in queue for site1
2025-06-23 09:08:06,221 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for site1
2025-06-23 09:08:06,227 ERROR scheduler Skipped queueing hrms.hr.doctype.leave_ledger_entry.leave_ledger_entry.process_expired_allocation because it was found in queue for site1
2025-06-23 09:08:06,234 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for site1
2025-06-23 09:08:06,242 ERROR scheduler Skipped queueing erpnext.setup.doctype.email_digest.email_digest.send because it was found in queue for site1
2025-06-23 09:08:06,256 ERROR scheduler Skipped queueing frappe.email.doctype.auto_email_report.auto_email_report.send_daily because it was found in queue for site1
2025-06-23 09:08:06,260 ERROR scheduler Skipped queueing frappe.integrations.doctype.s3_backup_settings.s3_backup_settings.take_backups_weekly because it was found in queue for site1
2025-06-23 09:08:06,265 ERROR scheduler Skipped queueing frappe.integrations.doctype.dropbox_settings.dropbox_settings.take_backups_weekly because it was found in queue for site1
2025-06-23 09:08:06,277 ERROR scheduler Skipped queueing erpnext.crm.utils.open_leads_opportunities_based_on_todays_event because it was found in queue for site1
2025-06-23 09:08:06,291 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.update_last_sync_of_checkin because it was found in queue for site1
2025-06-23 09:08:06,311 ERROR scheduler Skipped queueing frappe.social.doctype.energy_point_log.energy_point_log.send_weekly_summary because it was found in queue for site1
2025-06-23 09:08:06,327 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for site1
2025-06-23 09:08:06,331 ERROR scheduler Skipped queueing frappe.desk.notifications.clear_notifications because it was found in queue for site1
2025-06-23 09:08:06,334 ERROR scheduler Skipped queueing frappe.automation.doctype.auto_repeat.auto_repeat.make_auto_repeat_entry because it was found in queue for site1
2025-06-23 09:08:06,340 ERROR scheduler Skipped queueing frappe.desk.doctype.changelog_feed.changelog_feed.fetch_changelog_feed because it was found in queue for site1
2025-06-23 09:08:06,354 ERROR scheduler Skipped queueing frappe.integrations.doctype.dropbox_settings.dropbox_settings.take_backups_daily because it was found in queue for site1
2025-06-23 09:08:06,360 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for site1
2025-06-23 09:08:06,363 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_drive.google_drive.daily_backup because it was found in queue for site1
2025-06-23 09:08:06,368 ERROR scheduler Skipped queueing frappe.core.doctype.log_settings.log_settings.run_log_clean_up because it was found in queue for site1
2025-06-23 09:08:06,377 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for site1
2025-06-23 09:08:06,384 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_contacts.google_contacts.sync because it was found in queue for site1
2025-06-23 09:08:06,392 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for site1
2025-06-23 09:09:07,076 ERROR scheduler Skipped queueing frappe.desk.doctype.changelog_feed.changelog_feed.fetch_changelog_feed because it was found in queue for site1
2025-06-23 09:09:07,126 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_drive.google_drive.weekly_backup because it was found in queue for site1
2025-06-23 09:09:07,131 ERROR scheduler Skipped queueing frappe.integrations.doctype.dropbox_settings.dropbox_settings.take_backups_weekly because it was found in queue for site1
2025-06-23 09:09:07,151 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_weekly_updates because it was found in queue for site1
2025-06-23 09:09:07,218 ERROR scheduler Skipped queueing frappe.integrations.doctype.s3_backup_settings.s3_backup_settings.take_backups_weekly because it was found in queue for site1
2025-06-23 09:09:07,260 ERROR scheduler Skipped queueing frappe.social.doctype.energy_point_log.energy_point_log.send_weekly_summary because it was found in queue for site1
2025-06-23 09:09:07,267 ERROR scheduler Skipped queueing frappe.utils.change_log.check_for_update because it was found in queue for site1
2025-06-23 09:10:07,656 ERROR scheduler Skipped queueing frappe.integrations.doctype.dropbox_settings.dropbox_settings.take_backups_weekly because it was found in queue for site1
2025-06-23 09:10:07,664 ERROR scheduler Skipped queueing frappe.social.doctype.energy_point_log.energy_point_log.send_weekly_summary because it was found in queue for site1
2025-06-23 09:10:07,704 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_drive.google_drive.weekly_backup because it was found in queue for site1
2025-06-23 09:10:07,737 ERROR scheduler Skipped queueing frappe.utils.change_log.check_for_update because it was found in queue for site1
2025-06-23 09:10:07,761 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_weekly_updates because it was found in queue for site1
2025-06-23 09:10:07,780 ERROR scheduler Skipped queueing frappe.integrations.doctype.s3_backup_settings.s3_backup_settings.take_backups_weekly because it was found in queue for site1
2025-06-23 09:10:07,792 ERROR scheduler Skipped queueing frappe.desk.doctype.changelog_feed.changelog_feed.fetch_changelog_feed because it was found in queue for site1
2025-06-23 09:11:09,691 ERROR scheduler Skipped queueing frappe.integrations.doctype.dropbox_settings.dropbox_settings.take_backups_weekly because it was found in queue for site1
2025-06-23 09:11:09,712 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_weekly_updates because it was found in queue for site1
2025-06-23 09:11:09,773 ERROR scheduler Skipped queueing frappe.desk.doctype.changelog_feed.changelog_feed.fetch_changelog_feed because it was found in queue for site1
2025-06-23 09:11:09,827 ERROR scheduler Skipped queueing frappe.social.doctype.energy_point_log.energy_point_log.send_weekly_summary because it was found in queue for site1
2025-06-23 09:11:09,831 ERROR scheduler Skipped queueing frappe.integrations.doctype.s3_backup_settings.s3_backup_settings.take_backups_weekly because it was found in queue for site1
2025-06-23 09:11:09,835 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.pull because it was found in queue for site1
2025-06-23 09:11:09,879 ERROR scheduler Skipped queueing frappe.utils.change_log.check_for_update because it was found in queue for site1
2025-06-23 09:11:09,922 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_drive.google_drive.weekly_backup because it was found in queue for site1
2025-06-23 09:12:10,804 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.pull because it was found in queue for site1
2025-06-23 09:12:10,860 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_weekly_updates because it was found in queue for site1
2025-06-23 09:12:10,900 ERROR scheduler Skipped queueing frappe.social.doctype.energy_point_log.energy_point_log.send_weekly_summary because it was found in queue for site1
2025-06-23 09:12:10,976 ERROR scheduler Skipped queueing frappe.desk.doctype.changelog_feed.changelog_feed.fetch_changelog_feed because it was found in queue for site1
2025-06-23 09:12:10,983 ERROR scheduler Skipped queueing frappe.utils.change_log.check_for_update because it was found in queue for site1
2025-06-23 09:12:11,057 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_drive.google_drive.weekly_backup because it was found in queue for site1
2025-06-23 09:12:11,063 ERROR scheduler Skipped queueing frappe.integrations.doctype.dropbox_settings.dropbox_settings.take_backups_weekly because it was found in queue for site1
2025-06-23 09:12:11,075 ERROR scheduler Skipped queueing frappe.integrations.doctype.s3_backup_settings.s3_backup_settings.take_backups_weekly because it was found in queue for site1
2025-06-23 09:13:11,758 ERROR scheduler Skipped queueing frappe.integrations.doctype.dropbox_settings.dropbox_settings.take_backups_weekly because it was found in queue for site1
2025-06-23 09:13:11,769 ERROR scheduler Skipped queueing frappe.email.queue.flush because it was found in queue for site1
2025-06-23 09:13:11,776 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_weekly_updates because it was found in queue for site1
2025-06-23 09:13:11,787 ERROR scheduler Skipped queueing frappe.social.doctype.energy_point_log.energy_point_log.send_weekly_summary because it was found in queue for site1
2025-06-23 09:13:11,802 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.pull because it was found in queue for site1
2025-06-23 09:13:11,816 ERROR scheduler Skipped queueing frappe.utils.change_log.check_for_update because it was found in queue for site1
2025-06-23 09:13:11,824 ERROR scheduler Skipped queueing frappe.monitor.flush because it was found in queue for site1
2025-06-23 09:13:11,834 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_calendar.google_calendar.sync because it was found in queue for site1
2025-06-23 09:13:11,852 ERROR scheduler Skipped queueing hrms.hr.doctype.interview.interview.send_interview_reminder because it was found in queue for site1
2025-06-23 09:13:11,892 ERROR scheduler Skipped queueing frappe.desk.doctype.changelog_feed.changelog_feed.fetch_changelog_feed because it was found in queue for site1
2025-06-23 09:13:11,918 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_drive.google_drive.weekly_backup because it was found in queue for site1
2025-06-23 09:13:11,935 ERROR scheduler Skipped queueing frappe.integrations.doctype.s3_backup_settings.s3_backup_settings.take_backups_weekly because it was found in queue for site1
2025-06-23 09:14:14,771 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_drive.google_drive.weekly_backup because it was found in queue for site1
2025-06-23 09:14:14,833 ERROR scheduler Skipped queueing frappe.monitor.flush because it was found in queue for site1
2025-06-23 09:14:14,860 ERROR scheduler Skipped queueing frappe.integrations.doctype.dropbox_settings.dropbox_settings.take_backups_weekly because it was found in queue for site1
2025-06-23 09:14:14,894 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.pull because it was found in queue for site1
2025-06-23 09:14:14,907 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_weekly_updates because it was found in queue for site1
2025-06-23 09:14:14,926 ERROR scheduler Skipped queueing frappe.utils.change_log.check_for_update because it was found in queue for site1
2025-06-23 09:14:14,934 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_calendar.google_calendar.sync because it was found in queue for site1
2025-06-23 09:14:14,938 ERROR scheduler Skipped queueing frappe.integrations.doctype.s3_backup_settings.s3_backup_settings.take_backups_weekly because it was found in queue for site1
2025-06-23 09:14:14,979 ERROR scheduler Skipped queueing frappe.social.doctype.energy_point_log.energy_point_log.send_weekly_summary because it was found in queue for site1
2025-06-23 09:14:15,008 ERROR scheduler Skipped queueing hrms.hr.doctype.interview.interview.send_interview_reminder because it was found in queue for site1
2025-06-23 09:14:15,037 ERROR scheduler Skipped queueing frappe.desk.doctype.changelog_feed.changelog_feed.fetch_changelog_feed because it was found in queue for site1
2025-06-23 09:14:15,060 ERROR scheduler Skipped queueing frappe.email.queue.flush because it was found in queue for site1
2025-06-23 09:15:15,708 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_drive.google_drive.weekly_backup because it was found in queue for site1
2025-06-23 09:15:15,750 ERROR scheduler Skipped queueing frappe.desk.doctype.changelog_feed.changelog_feed.fetch_changelog_feed because it was found in queue for site1
2025-06-23 09:15:15,755 ERROR scheduler Skipped queueing frappe.monitor.flush because it was found in queue for site1
2025-06-23 09:15:15,759 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_calendar.google_calendar.sync because it was found in queue for site1
2025-06-23 09:15:15,803 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_weekly_updates because it was found in queue for site1
2025-06-23 09:15:15,818 ERROR scheduler Skipped queueing hrms.hr.doctype.interview.interview.send_interview_reminder because it was found in queue for site1
2025-06-23 09:15:15,841 ERROR scheduler Skipped queueing frappe.social.doctype.energy_point_log.energy_point_log.send_weekly_summary because it was found in queue for site1
2025-06-23 09:15:15,906 ERROR scheduler Skipped queueing frappe.integrations.doctype.dropbox_settings.dropbox_settings.take_backups_weekly because it was found in queue for site1
2025-06-23 09:15:15,923 ERROR scheduler Skipped queueing frappe.email.queue.flush because it was found in queue for site1
2025-06-23 09:15:15,945 ERROR scheduler Skipped queueing frappe.utils.change_log.check_for_update because it was found in queue for site1
2025-06-23 09:15:15,997 ERROR scheduler Skipped queueing frappe.integrations.doctype.s3_backup_settings.s3_backup_settings.take_backups_weekly because it was found in queue for site1
2025-06-23 09:17:19,447 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_calendar.google_calendar.sync because it was found in queue for site1
2025-06-23 09:17:19,456 ERROR scheduler Skipped queueing hrms.hr.doctype.interview.interview.send_interview_reminder because it was found in queue for site1
2025-06-23 09:17:19,525 ERROR scheduler Skipped queueing frappe.email.queue.flush because it was found in queue for site1
2025-06-23 09:17:19,679 ERROR scheduler Skipped queueing frappe.monitor.flush because it was found in queue for site1
2025-06-23 09:18:19,965 ERROR scheduler Skipped queueing hrms.hr.doctype.interview.interview.send_interview_reminder because it was found in queue for site1
2025-06-23 09:18:19,992 ERROR scheduler Skipped queueing frappe.monitor.flush because it was found in queue for site1
2025-06-23 09:18:20,006 ERROR scheduler Skipped queueing frappe.email.queue.flush because it was found in queue for site1
2025-06-23 09:18:20,012 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_calendar.google_calendar.sync because it was found in queue for site1
2025-06-23 09:19:21,511 ERROR scheduler Skipped queueing frappe.email.queue.flush because it was found in queue for site1
2025-06-23 09:19:21,563 ERROR scheduler Skipped queueing hrms.hr.doctype.interview.interview.send_interview_reminder because it was found in queue for site1
2025-06-23 09:19:21,599 ERROR scheduler Skipped queueing frappe.monitor.flush because it was found in queue for site1
2025-06-23 09:19:21,673 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_calendar.google_calendar.sync because it was found in queue for site1
2025-06-23 09:20:23,207 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_calendar.google_calendar.sync because it was found in queue for site1
2025-06-23 09:20:23,229 ERROR scheduler Skipped queueing frappe.monitor.flush because it was found in queue for site1
2025-06-23 09:20:23,249 ERROR scheduler Skipped queueing frappe.email.queue.flush because it was found in queue for site1
2025-06-23 09:20:23,407 ERROR scheduler Skipped queueing hrms.hr.doctype.interview.interview.send_interview_reminder because it was found in queue for site1
2025-06-23 09:21:24,036 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.pull because it was found in queue for site1
2025-06-23 09:21:24,054 ERROR scheduler Skipped queueing hrms.hr.doctype.interview.interview.send_interview_reminder because it was found in queue for site1
2025-06-23 09:21:24,122 ERROR scheduler Skipped queueing frappe.monitor.flush because it was found in queue for site1
2025-06-23 09:21:24,134 ERROR scheduler Skipped queueing frappe.email.queue.flush because it was found in queue for site1
2025-06-23 09:21:24,264 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_calendar.google_calendar.sync because it was found in queue for site1
2025-06-23 09:25:29,201 ERROR scheduler Skipped queueing hrms.hr.doctype.interview.interview.send_interview_reminder because it was found in queue for site1
2025-06-23 09:25:29,205 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_calendar.google_calendar.sync because it was found in queue for site1
2025-06-23 09:25:29,208 ERROR scheduler Skipped queueing frappe.email.queue.flush because it was found in queue for site1
2025-06-23 09:25:29,267 ERROR scheduler Skipped queueing frappe.monitor.flush because it was found in queue for site1
2025-06-23 09:26:30,593 ERROR scheduler Skipped queueing frappe.email.queue.flush because it was found in queue for site1
2025-06-23 09:26:30,688 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_calendar.google_calendar.sync because it was found in queue for site1
2025-06-23 09:26:30,778 ERROR scheduler Skipped queueing hrms.hr.doctype.interview.interview.send_interview_reminder because it was found in queue for site1
2025-06-23 09:26:30,813 ERROR scheduler Skipped queueing frappe.monitor.flush because it was found in queue for site1
2025-06-23 10:01:16,185 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for site1
2025-06-23 10:01:16,191 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for site1
2025-06-23 10:01:16,207 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for site1
2025-06-23 10:01:16,248 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for site1
2025-06-23 10:01:16,276 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_schedule_assignment.shift_schedule_assignment.process_auto_shift_creation because it was found in queue for site1
2025-06-23 10:01:16,284 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.process_auto_attendance_for_all_shifts because it was found in queue for site1
2025-06-23 10:01:16,300 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for site1
2025-06-23 10:01:16,333 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for site1
2025-06-23 10:01:16,350 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for site1
2025-06-23 10:01:16,371 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for site1
2025-06-23 10:01:16,375 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for site1
2025-06-23 10:01:16,407 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.update_last_sync_of_checkin because it was found in queue for site1
2025-06-23 10:01:16,418 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for site1
2025-06-23 10:02:17,885 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for site1
2025-06-23 10:02:17,913 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_schedule_assignment.shift_schedule_assignment.process_auto_shift_creation because it was found in queue for site1
2025-06-23 10:02:17,941 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.process_auto_attendance_for_all_shifts because it was found in queue for site1
2025-06-23 10:02:17,951 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for site1
2025-06-23 10:02:18,029 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for site1
2025-06-23 10:02:18,092 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.pull because it was found in queue for site1
2025-06-23 10:02:18,108 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for site1
2025-06-23 10:02:18,117 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.update_last_sync_of_checkin because it was found in queue for site1
2025-06-23 10:02:18,129 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for site1
2025-06-23 10:02:18,141 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for site1
2025-06-23 10:02:18,158 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for site1
2025-06-23 10:02:18,177 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for site1
2025-06-23 10:02:18,302 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for site1
2025-06-23 10:02:18,368 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for site1
2025-06-23 10:03:18,407 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for site1
2025-06-23 10:03:18,442 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for site1
2025-06-23 10:03:18,485 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for site1
2025-06-23 10:03:18,489 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for site1
2025-06-23 10:03:18,555 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.process_auto_attendance_for_all_shifts because it was found in queue for site1
2025-06-23 10:03:18,564 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for site1
2025-06-23 10:03:18,575 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.update_last_sync_of_checkin because it was found in queue for site1
2025-06-23 10:03:18,580 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for site1
2025-06-23 10:03:18,601 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for site1
2025-06-23 10:03:18,612 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for site1
2025-06-23 10:03:18,623 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for site1
2025-06-23 10:03:18,636 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_schedule_assignment.shift_schedule_assignment.process_auto_shift_creation because it was found in queue for site1
2025-06-23 10:03:18,641 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for site1
2025-06-23 10:04:19,675 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for site1
2025-06-23 10:04:19,679 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for site1
2025-06-23 10:04:19,688 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.update_last_sync_of_checkin because it was found in queue for site1
2025-06-23 10:04:19,711 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for site1
2025-06-23 10:04:19,725 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_schedule_assignment.shift_schedule_assignment.process_auto_shift_creation because it was found in queue for site1
2025-06-23 10:04:19,733 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for site1
2025-06-23 10:04:19,749 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for site1
2025-06-23 10:04:19,771 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for site1
2025-06-23 10:04:19,800 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for site1
2025-06-23 10:04:19,857 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for site1
2025-06-23 10:04:19,870 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for site1
2025-06-23 10:04:19,877 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for site1
2025-06-23 10:04:19,882 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.process_auto_attendance_for_all_shifts because it was found in queue for site1
2025-06-23 10:05:23,642 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for site1
2025-06-23 10:05:23,655 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for site1
2025-06-23 10:05:23,715 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for site1
2025-06-23 10:05:23,737 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for site1
2025-06-23 10:05:23,799 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.update_last_sync_of_checkin because it was found in queue for site1
2025-06-23 10:05:23,841 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.process_auto_attendance_for_all_shifts because it was found in queue for site1
2025-06-23 10:05:23,910 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for site1
2025-06-23 10:05:23,929 ERROR scheduler Skipped queueing frappe.monitor.flush because it was found in queue for site1
2025-06-23 10:05:24,078 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for site1
2025-06-23 10:05:24,117 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for site1
2025-06-23 10:05:24,139 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for site1
2025-06-23 10:05:24,180 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_calendar.google_calendar.sync because it was found in queue for site1
2025-06-23 10:05:24,190 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for site1
2025-06-23 10:05:24,221 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for site1
2025-06-23 10:05:24,225 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_schedule_assignment.shift_schedule_assignment.process_auto_shift_creation because it was found in queue for site1
2025-06-23 10:05:24,230 ERROR scheduler Skipped queueing frappe.email.queue.flush because it was found in queue for site1
2025-06-23 10:05:24,257 ERROR scheduler Skipped queueing hrms.hr.doctype.interview.interview.send_interview_reminder because it was found in queue for site1
2025-06-23 10:06:25,144 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for site1
2025-06-23 10:06:25,158 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for site1
2025-06-23 10:06:25,162 ERROR scheduler Skipped queueing frappe.email.queue.flush because it was found in queue for site1
2025-06-23 10:06:25,169 ERROR scheduler Skipped queueing frappe.monitor.flush because it was found in queue for site1
2025-06-23 10:06:25,172 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for site1
2025-06-23 10:06:25,202 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for site1
2025-06-23 10:06:25,209 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for site1
2025-06-23 10:06:25,216 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.process_auto_attendance_for_all_shifts because it was found in queue for site1
2025-06-23 10:06:25,235 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for site1
2025-06-23 10:06:25,245 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_schedule_assignment.shift_schedule_assignment.process_auto_shift_creation because it was found in queue for site1
2025-06-23 10:06:25,258 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for site1
2025-06-23 10:06:25,273 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.update_last_sync_of_checkin because it was found in queue for site1
2025-06-23 10:06:25,290 ERROR scheduler Skipped queueing hrms.hr.doctype.interview.interview.send_interview_reminder because it was found in queue for site1
2025-06-23 10:06:25,305 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for site1
2025-06-23 10:06:25,336 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for site1
2025-06-23 10:06:25,352 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_calendar.google_calendar.sync because it was found in queue for site1
2025-06-23 10:06:25,377 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for site1
2025-06-23 10:07:26,323 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for site1
2025-06-23 10:07:26,345 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for site1
2025-06-23 10:07:26,356 ERROR scheduler Skipped queueing hrms.hr.doctype.interview.interview.send_interview_reminder because it was found in queue for site1
2025-06-23 10:07:26,377 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for site1
2025-06-23 10:07:26,402 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for site1
2025-06-23 10:07:26,407 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for site1
2025-06-23 10:07:26,415 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_calendar.google_calendar.sync because it was found in queue for site1
2025-06-23 10:07:26,429 ERROR scheduler Skipped queueing frappe.monitor.flush because it was found in queue for site1
2025-06-23 10:07:26,454 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for site1
2025-06-23 10:07:26,483 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.update_last_sync_of_checkin because it was found in queue for site1
2025-06-23 10:07:26,487 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for site1
2025-06-23 10:07:26,511 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for site1
2025-06-23 10:07:26,520 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_schedule_assignment.shift_schedule_assignment.process_auto_shift_creation because it was found in queue for site1
2025-06-23 10:07:26,530 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for site1
2025-06-23 10:07:26,542 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.process_auto_attendance_for_all_shifts because it was found in queue for site1
2025-06-23 10:07:26,561 ERROR scheduler Skipped queueing frappe.email.queue.flush because it was found in queue for site1
2025-06-23 10:07:26,583 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for site1
2025-06-23 10:08:27,627 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for site1
2025-06-23 10:08:27,642 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for site1
2025-06-23 10:08:27,654 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_calendar.google_calendar.sync because it was found in queue for site1
2025-06-23 10:08:27,659 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for site1
2025-06-23 10:08:27,664 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for site1
2025-06-23 10:08:27,671 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for site1
2025-06-23 10:08:27,686 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for site1
2025-06-23 10:08:27,713 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for site1
2025-06-23 10:08:27,728 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.update_last_sync_of_checkin because it was found in queue for site1
2025-06-23 10:08:27,757 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for site1
2025-06-23 10:08:27,766 ERROR scheduler Skipped queueing frappe.email.queue.flush because it was found in queue for site1
2025-06-23 10:08:27,780 ERROR scheduler Skipped queueing hrms.hr.doctype.interview.interview.send_interview_reminder because it was found in queue for site1
2025-06-23 10:08:27,787 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.process_auto_attendance_for_all_shifts because it was found in queue for site1
2025-06-23 10:08:27,792 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_schedule_assignment.shift_schedule_assignment.process_auto_shift_creation because it was found in queue for site1
2025-06-23 10:08:27,844 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for site1
2025-06-23 10:08:27,848 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for site1
2025-06-23 10:08:27,854 ERROR scheduler Skipped queueing frappe.monitor.flush because it was found in queue for site1
2025-06-23 11:05:46,959 ERROR scheduler Skipped queueing frappe.monitor.flush because it was found in queue for site1
2025-06-23 11:05:46,979 ERROR scheduler Skipped queueing hrms.hr.doctype.interview.interview.send_interview_reminder because it was found in queue for site1
2025-06-23 11:05:47,065 ERROR scheduler Skipped queueing frappe.email.queue.flush because it was found in queue for site1
2025-06-23 11:05:47,150 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_calendar.google_calendar.sync because it was found in queue for site1
2025-06-23 12:01:11,909 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.process_auto_attendance_for_all_shifts because it was found in queue for site1
2025-06-23 12:01:11,915 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_schedule_assignment.shift_schedule_assignment.process_auto_shift_creation because it was found in queue for site1
2025-06-23 12:01:11,934 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for site1
2025-06-23 12:01:11,943 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for site1
2025-06-23 12:01:11,974 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for site1
2025-06-23 12:01:12,003 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for site1
2025-06-23 12:01:12,016 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for site1
2025-06-23 12:01:12,037 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for site1
2025-06-23 12:01:12,044 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for site1
2025-06-23 12:01:12,049 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.update_last_sync_of_checkin because it was found in queue for site1
2025-06-23 12:01:12,065 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for site1
2025-06-23 12:01:12,082 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for site1
2025-06-23 12:01:12,090 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for site1
2025-06-23 12:02:16,926 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for site1
2025-06-23 12:02:16,930 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for site1
2025-06-23 12:02:16,934 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for site1
2025-06-23 12:02:16,997 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.update_last_sync_of_checkin because it was found in queue for site1
2025-06-23 12:02:17,141 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for site1
2025-06-23 12:02:17,262 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for site1
2025-06-23 12:02:17,271 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for site1
2025-06-23 12:02:17,290 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for site1
2025-06-23 12:02:17,315 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for site1
2025-06-23 12:02:17,322 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.process_auto_attendance_for_all_shifts because it was found in queue for site1
2025-06-23 12:02:17,326 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for site1
2025-06-23 12:02:17,337 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_schedule_assignment.shift_schedule_assignment.process_auto_shift_creation because it was found in queue for site1
2025-06-23 12:02:17,351 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for site1
2025-06-23 12:03:17,772 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for site1
2025-06-23 12:03:17,827 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for site1
2025-06-23 12:03:17,872 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.update_last_sync_of_checkin because it was found in queue for site1
2025-06-23 12:03:17,890 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for site1
2025-06-23 12:03:17,897 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for site1
2025-06-23 12:03:17,909 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_schedule_assignment.shift_schedule_assignment.process_auto_shift_creation because it was found in queue for site1
2025-06-23 12:03:17,929 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for site1
2025-06-23 12:03:18,023 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for site1
2025-06-23 12:03:18,027 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for site1
2025-06-23 12:03:18,039 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for site1
2025-06-23 12:03:18,074 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.process_auto_attendance_for_all_shifts because it was found in queue for site1
2025-06-23 12:03:18,085 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for site1
2025-06-23 12:03:18,089 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for site1
2025-06-23 12:04:19,825 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for site1
2025-06-23 12:04:19,832 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for site1
2025-06-23 12:04:19,851 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for site1
2025-06-23 12:04:19,868 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for site1
2025-06-23 12:04:19,892 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_schedule_assignment.shift_schedule_assignment.process_auto_shift_creation because it was found in queue for site1
2025-06-23 12:04:19,900 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for site1
2025-06-23 12:04:19,915 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.process_auto_attendance_for_all_shifts because it was found in queue for site1
2025-06-23 12:04:19,955 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.update_last_sync_of_checkin because it was found in queue for site1
2025-06-23 12:04:19,994 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for site1
2025-06-23 12:04:20,018 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for site1
2025-06-23 12:04:20,024 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for site1
2025-06-23 12:04:20,062 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for site1
2025-06-23 12:04:20,084 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for site1
2025-06-23 12:09:28,152 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_calendar.google_calendar.sync because it was found in queue for site1
2025-06-23 12:09:28,161 ERROR scheduler Skipped queueing hrms.hr.doctype.interview.interview.send_interview_reminder because it was found in queue for site1
2025-06-23 12:09:28,180 ERROR scheduler Skipped queueing frappe.monitor.flush because it was found in queue for site1
2025-06-23 12:09:28,243 ERROR scheduler Skipped queueing frappe.email.queue.flush because it was found in queue for site1
2025-06-23 12:10:28,920 ERROR scheduler Skipped queueing hrms.hr.doctype.interview.interview.send_interview_reminder because it was found in queue for site1
2025-06-23 12:10:29,059 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_calendar.google_calendar.sync because it was found in queue for site1
2025-06-23 12:10:29,069 ERROR scheduler Skipped queueing frappe.email.queue.flush because it was found in queue for site1
2025-06-23 12:10:29,077 ERROR scheduler Skipped queueing frappe.monitor.flush because it was found in queue for site1
2025-06-23 13:39:15,179 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.process_auto_attendance_for_all_shifts because it was found in queue for site1
2025-06-23 13:39:15,194 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for site1
2025-06-23 13:39:15,209 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.update_last_sync_of_checkin because it was found in queue for site1
2025-06-23 13:39:15,213 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_schedule_assignment.shift_schedule_assignment.process_auto_shift_creation because it was found in queue for site1
2025-06-23 13:39:15,223 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for site1
2025-06-23 13:39:15,226 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for site1
2025-06-23 13:39:15,296 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for site1
2025-06-23 13:39:15,308 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for site1
2025-06-23 13:39:15,343 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for site1
2025-06-23 13:39:15,375 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for site1
2025-06-23 13:39:15,381 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for site1
2025-06-23 13:39:15,399 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for site1
2025-06-23 13:39:15,408 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for site1
2025-06-23 13:40:16,324 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for site1
2025-06-23 13:40:16,341 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for site1
2025-06-23 13:40:16,395 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for site1
2025-06-23 13:40:16,416 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for site1
2025-06-23 13:40:16,436 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for site1
2025-06-23 13:40:16,447 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for site1
2025-06-23 13:40:16,453 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_schedule_assignment.shift_schedule_assignment.process_auto_shift_creation because it was found in queue for site1
2025-06-23 13:40:16,463 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for site1
2025-06-23 13:40:16,478 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for site1
2025-06-23 13:40:16,493 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for site1
2025-06-23 13:40:16,498 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for site1
2025-06-23 13:40:16,538 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.update_last_sync_of_checkin because it was found in queue for site1
2025-06-23 13:40:16,545 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.process_auto_attendance_for_all_shifts because it was found in queue for site1
2025-06-23 13:41:17,026 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.process_auto_attendance_for_all_shifts because it was found in queue for site1
2025-06-23 13:41:17,039 ERROR scheduler Skipped queueing frappe.monitor.flush because it was found in queue for site1
2025-06-23 13:41:17,050 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for site1
2025-06-23 13:41:17,064 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.update_last_sync_of_checkin because it was found in queue for site1
2025-06-23 13:41:17,068 ERROR scheduler Skipped queueing hrms.hr.doctype.interview.interview.send_interview_reminder because it was found in queue for site1
2025-06-23 13:41:17,091 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for site1
2025-06-23 13:41:17,095 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for site1
2025-06-23 13:41:17,100 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for site1
2025-06-23 13:41:17,121 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for site1
2025-06-23 13:41:17,154 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for site1
2025-06-23 13:41:17,165 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_calendar.google_calendar.sync because it was found in queue for site1
2025-06-23 13:41:17,184 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_schedule_assignment.shift_schedule_assignment.process_auto_shift_creation because it was found in queue for site1
2025-06-23 13:41:17,203 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for site1
2025-06-23 13:41:17,209 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for site1
2025-06-23 13:41:17,224 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.pull because it was found in queue for site1
2025-06-23 13:41:17,235 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for site1
2025-06-23 13:41:17,241 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for site1
2025-06-23 13:41:17,247 ERROR scheduler Skipped queueing frappe.email.queue.flush because it was found in queue for site1
2025-06-23 13:42:18,347 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for site1
2025-06-23 13:42:18,364 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.pull because it was found in queue for site1
2025-06-23 13:42:18,405 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for site1
2025-06-23 13:42:18,453 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for site1
2025-06-23 13:42:18,460 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for site1
2025-06-23 13:42:18,465 ERROR scheduler Skipped queueing frappe.email.queue.flush because it was found in queue for site1
2025-06-23 13:42:18,474 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for site1
2025-06-23 13:42:18,485 ERROR scheduler Skipped queueing hrms.hr.doctype.interview.interview.send_interview_reminder because it was found in queue for site1
2025-06-23 13:42:18,492 ERROR scheduler Skipped queueing frappe.monitor.flush because it was found in queue for site1
2025-06-23 13:42:18,517 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for site1
2025-06-23 13:42:18,540 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_schedule_assignment.shift_schedule_assignment.process_auto_shift_creation because it was found in queue for site1
2025-06-23 13:42:18,551 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for site1
2025-06-23 13:42:18,555 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_calendar.google_calendar.sync because it was found in queue for site1
2025-06-23 13:42:18,558 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for site1
2025-06-23 13:42:18,569 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.process_auto_attendance_for_all_shifts because it was found in queue for site1
2025-06-23 13:42:18,585 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for site1
2025-06-23 13:42:18,609 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for site1
2025-06-23 13:42:18,617 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.update_last_sync_of_checkin because it was found in queue for site1
2025-06-23 13:43:18,946 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for site1
2025-06-23 13:43:18,979 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for site1
2025-06-23 13:43:19,006 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for site1
2025-06-23 13:43:19,039 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for site1
2025-06-23 13:43:19,044 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for site1
2025-06-23 13:43:19,061 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_schedule_assignment.shift_schedule_assignment.process_auto_shift_creation because it was found in queue for site1
2025-06-23 13:43:19,067 ERROR scheduler Skipped queueing frappe.email.queue.flush because it was found in queue for site1
2025-06-23 13:43:19,084 ERROR scheduler Skipped queueing frappe.monitor.flush because it was found in queue for site1
2025-06-23 13:43:19,089 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.update_last_sync_of_checkin because it was found in queue for site1
2025-06-23 13:43:19,111 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for site1
2025-06-23 13:43:19,116 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for site1
2025-06-23 13:43:19,126 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for site1
2025-06-23 13:43:19,131 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.process_auto_attendance_for_all_shifts because it was found in queue for site1
2025-06-23 13:43:19,138 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for site1
2025-06-23 13:43:19,162 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.pull because it was found in queue for site1
2025-06-23 13:43:19,166 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_calendar.google_calendar.sync because it was found in queue for site1
2025-06-23 13:43:19,180 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for site1
2025-06-23 13:43:19,186 ERROR scheduler Skipped queueing hrms.hr.doctype.interview.interview.send_interview_reminder because it was found in queue for site1
2025-06-23 14:01:41,533 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for site1
2025-06-23 14:01:41,536 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for site1
2025-06-23 14:01:41,543 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for site1
2025-06-23 14:01:41,583 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.update_last_sync_of_checkin because it was found in queue for site1
2025-06-23 14:01:41,589 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for site1
2025-06-23 14:01:41,606 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for site1
2025-06-23 14:01:41,618 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for site1
2025-06-23 14:01:41,621 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for site1
2025-06-23 14:01:41,662 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.process_auto_attendance_for_all_shifts because it was found in queue for site1
2025-06-23 14:01:41,680 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for site1
2025-06-23 14:01:41,685 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for site1
2025-06-23 14:01:41,694 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for site1
2025-06-23 14:01:41,728 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_schedule_assignment.shift_schedule_assignment.process_auto_shift_creation because it was found in queue for site1
2025-06-23 14:02:41,773 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_schedule_assignment.shift_schedule_assignment.process_auto_shift_creation because it was found in queue for site1
2025-06-23 14:02:41,824 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for site1
2025-06-23 14:02:41,840 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for site1
2025-06-23 14:02:41,843 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for site1
2025-06-23 14:02:41,847 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for site1
2025-06-23 14:02:41,875 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for site1
2025-06-23 14:02:41,882 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for site1
2025-06-23 14:02:41,905 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.update_last_sync_of_checkin because it was found in queue for site1
2025-06-23 14:02:41,909 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for site1
2025-06-23 14:02:41,916 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.process_auto_attendance_for_all_shifts because it was found in queue for site1
2025-06-23 14:02:41,949 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for site1
2025-06-23 14:02:41,963 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for site1
2025-06-23 14:02:41,998 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for site1
2025-06-23 14:03:42,829 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.update_last_sync_of_checkin because it was found in queue for site1
2025-06-23 14:03:42,842 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for site1
2025-06-23 14:03:42,862 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_schedule_assignment.shift_schedule_assignment.process_auto_shift_creation because it was found in queue for site1
2025-06-23 14:03:42,902 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for site1
2025-06-23 14:03:42,908 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.process_auto_attendance_for_all_shifts because it was found in queue for site1
2025-06-23 14:03:42,913 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for site1
2025-06-23 14:03:42,921 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for site1
2025-06-23 14:03:42,980 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for site1
2025-06-23 14:03:42,996 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for site1
2025-06-23 14:03:43,025 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for site1
2025-06-23 14:03:43,031 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for site1
2025-06-23 14:03:43,039 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for site1
2025-06-23 14:03:43,054 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for site1
2025-06-23 14:05:45,837 ERROR scheduler Skipped queueing frappe.email.queue.flush because it was found in queue for site1
2025-06-23 14:05:45,855 ERROR scheduler Skipped queueing frappe.monitor.flush because it was found in queue for site1
2025-06-23 14:05:45,949 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_calendar.google_calendar.sync because it was found in queue for site1
2025-06-23 14:05:45,997 ERROR scheduler Skipped queueing hrms.hr.doctype.interview.interview.send_interview_reminder because it was found in queue for site1
2025-06-23 14:06:46,826 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_calendar.google_calendar.sync because it was found in queue for site1
2025-06-23 14:06:46,864 ERROR scheduler Skipped queueing frappe.email.queue.flush because it was found in queue for site1
2025-06-23 14:06:46,934 ERROR scheduler Skipped queueing frappe.monitor.flush because it was found in queue for site1
2025-06-23 14:06:46,950 ERROR scheduler Skipped queueing hrms.hr.doctype.interview.interview.send_interview_reminder because it was found in queue for site1
2025-06-23 14:07:47,056 ERROR scheduler Skipped queueing frappe.monitor.flush because it was found in queue for site1
2025-06-23 14:07:47,110 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_calendar.google_calendar.sync because it was found in queue for site1
2025-06-23 14:07:47,215 ERROR scheduler Skipped queueing frappe.email.queue.flush because it was found in queue for site1
2025-06-23 14:07:47,227 ERROR scheduler Skipped queueing hrms.hr.doctype.interview.interview.send_interview_reminder because it was found in queue for site1
2025-06-23 14:08:48,915 ERROR scheduler Skipped queueing hrms.hr.doctype.interview.interview.send_interview_reminder because it was found in queue for site1
2025-06-23 14:08:48,945 ERROR scheduler Skipped queueing frappe.monitor.flush because it was found in queue for site1
2025-06-23 14:08:49,072 ERROR scheduler Skipped queueing frappe.email.queue.flush because it was found in queue for site1
2025-06-23 14:08:49,157 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_calendar.google_calendar.sync because it was found in queue for site1
2025-06-23 15:01:59,351 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for site1
2025-06-23 15:01:59,380 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for site1
2025-06-23 15:01:59,406 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.process_auto_attendance_for_all_shifts because it was found in queue for site1
2025-06-23 15:01:59,417 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for site1
2025-06-23 15:01:59,420 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for site1
2025-06-23 15:01:59,430 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for site1
2025-06-23 15:01:59,443 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for site1
2025-06-23 15:01:59,449 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for site1
2025-06-23 15:01:59,452 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.update_last_sync_of_checkin because it was found in queue for site1
2025-06-23 15:01:59,468 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for site1
2025-06-23 15:01:59,486 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for site1
2025-06-23 15:01:59,518 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_schedule_assignment.shift_schedule_assignment.process_auto_shift_creation because it was found in queue for site1
2025-06-23 15:01:59,541 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for site1
2025-06-23 15:03:01,436 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for site1
2025-06-23 15:03:01,441 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for site1
2025-06-23 15:03:01,466 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for site1
2025-06-23 15:03:01,490 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.process_auto_attendance_for_all_shifts because it was found in queue for site1
2025-06-23 15:03:01,496 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.update_last_sync_of_checkin because it was found in queue for site1
2025-06-23 15:03:01,499 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for site1
2025-06-23 15:03:01,511 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for site1
2025-06-23 15:03:01,543 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for site1
2025-06-23 15:03:01,585 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for site1
2025-06-23 15:03:01,594 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for site1
2025-06-23 15:03:01,604 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_schedule_assignment.shift_schedule_assignment.process_auto_shift_creation because it was found in queue for site1
2025-06-23 15:03:01,627 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for site1
2025-06-23 15:03:01,631 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for site1
2025-06-23 15:04:01,697 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for site1
2025-06-23 15:04:01,708 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for site1
2025-06-23 15:04:01,727 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.process_auto_attendance_for_all_shifts because it was found in queue for site1
2025-06-23 15:04:01,738 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for site1
2025-06-23 15:04:01,742 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_schedule_assignment.shift_schedule_assignment.process_auto_shift_creation because it was found in queue for site1
2025-06-23 15:04:01,763 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for site1
2025-06-23 15:04:01,772 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for site1
2025-06-23 15:04:01,778 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for site1
2025-06-23 15:04:01,827 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for site1
2025-06-23 15:04:01,874 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for site1
2025-06-23 15:04:01,881 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for site1
2025-06-23 15:04:01,927 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for site1
2025-06-23 15:04:01,932 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.update_last_sync_of_checkin because it was found in queue for site1
2025-06-23 15:05:03,434 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for site1
2025-06-23 15:05:03,455 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for site1
2025-06-23 15:05:03,460 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_schedule_assignment.shift_schedule_assignment.process_auto_shift_creation because it was found in queue for site1
2025-06-23 15:05:03,480 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.process_auto_attendance_for_all_shifts because it was found in queue for site1
2025-06-23 15:05:03,499 ERROR scheduler Skipped queueing frappe.email.queue.flush because it was found in queue for site1
2025-06-23 15:05:03,507 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for site1
2025-06-23 15:05:03,525 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for site1
2025-06-23 15:05:03,531 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for site1
2025-06-23 15:05:03,538 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.update_last_sync_of_checkin because it was found in queue for site1
2025-06-23 15:05:03,546 ERROR scheduler Skipped queueing frappe.monitor.flush because it was found in queue for site1
2025-06-23 15:05:03,565 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for site1
2025-06-23 15:05:03,573 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for site1
2025-06-23 15:05:03,581 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for site1
2025-06-23 15:05:03,583 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for site1
2025-06-23 15:05:03,595 ERROR scheduler Skipped queueing hrms.hr.doctype.interview.interview.send_interview_reminder because it was found in queue for site1
2025-06-23 15:05:03,608 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_calendar.google_calendar.sync because it was found in queue for site1
2025-06-23 15:05:03,634 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for site1
2025-06-23 15:06:04,595 ERROR scheduler Skipped queueing frappe.monitor.flush because it was found in queue for site1
2025-06-23 15:06:04,626 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for site1
2025-06-23 15:06:04,633 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_schedule_assignment.shift_schedule_assignment.process_auto_shift_creation because it was found in queue for site1
2025-06-23 15:06:04,640 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for site1
2025-06-23 15:06:04,655 ERROR scheduler Skipped queueing hrms.hr.doctype.interview.interview.send_interview_reminder because it was found in queue for site1
2025-06-23 15:06:04,659 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_calendar.google_calendar.sync because it was found in queue for site1
2025-06-23 15:06:04,664 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for site1
2025-06-23 15:06:04,669 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.update_last_sync_of_checkin because it was found in queue for site1
2025-06-23 15:06:04,691 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for site1
2025-06-23 15:06:04,700 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for site1
2025-06-23 15:06:04,728 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for site1
2025-06-23 15:06:04,763 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for site1
2025-06-23 15:06:04,768 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for site1
2025-06-23 15:06:04,771 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for site1
2025-06-23 15:06:04,775 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.process_auto_attendance_for_all_shifts because it was found in queue for site1
2025-06-23 15:06:04,788 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for site1
2025-06-23 15:06:04,804 ERROR scheduler Skipped queueing frappe.email.queue.flush because it was found in queue for site1
2025-06-23 16:01:21,008 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.update_last_sync_of_checkin because it was found in queue for site1
2025-06-23 16:01:21,069 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for site1
2025-06-23 16:01:21,107 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for site1
2025-06-23 16:01:21,121 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.process_auto_attendance_for_all_shifts because it was found in queue for site1
2025-06-23 16:01:21,126 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for site1
2025-06-23 16:01:21,147 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_schedule_assignment.shift_schedule_assignment.process_auto_shift_creation because it was found in queue for site1
2025-06-23 16:01:21,181 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for site1
2025-06-23 16:01:21,184 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for site1
2025-06-23 16:01:21,206 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for site1
2025-06-23 16:01:21,219 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for site1
2025-06-23 16:01:21,244 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for site1
2025-06-23 16:01:21,266 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for site1
2025-06-23 16:01:21,283 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for site1
2025-06-23 16:02:23,204 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.update_last_sync_of_checkin because it was found in queue for site1
2025-06-23 16:02:23,215 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_schedule_assignment.shift_schedule_assignment.process_auto_shift_creation because it was found in queue for site1
2025-06-23 16:02:23,223 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for site1
2025-06-23 16:02:23,250 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for site1
2025-06-23 16:02:23,269 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for site1
2025-06-23 16:02:23,280 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for site1
2025-06-23 16:02:23,292 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for site1
2025-06-23 16:02:23,306 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for site1
2025-06-23 16:02:23,315 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for site1
2025-06-23 16:02:23,338 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for site1
2025-06-23 16:02:23,349 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for site1
2025-06-23 16:02:23,378 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for site1
2025-06-23 16:02:23,399 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.process_auto_attendance_for_all_shifts because it was found in queue for site1
2025-06-23 16:02:23,410 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.pull because it was found in queue for site1
2025-06-23 16:05:25,977 ERROR scheduler Skipped queueing hrms.hr.doctype.interview.interview.send_interview_reminder because it was found in queue for site1
2025-06-23 16:05:26,002 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_calendar.google_calendar.sync because it was found in queue for site1
2025-06-23 16:05:26,070 ERROR scheduler Skipped queueing frappe.monitor.flush because it was found in queue for site1
2025-06-23 16:05:26,098 ERROR scheduler Skipped queueing frappe.email.queue.flush because it was found in queue for site1
2025-06-23 16:06:27,909 ERROR scheduler Skipped queueing hrms.hr.doctype.interview.interview.send_interview_reminder because it was found in queue for site1
2025-06-23 16:06:27,998 ERROR scheduler Skipped queueing frappe.email.queue.flush because it was found in queue for site1
2025-06-23 16:06:28,150 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_calendar.google_calendar.sync because it was found in queue for site1
2025-06-23 16:06:28,162 ERROR scheduler Skipped queueing frappe.monitor.flush because it was found in queue for site1
2025-06-23 16:07:28,559 ERROR scheduler Skipped queueing hrms.hr.doctype.interview.interview.send_interview_reminder because it was found in queue for site1
2025-06-23 16:07:28,606 ERROR scheduler Skipped queueing frappe.email.queue.flush because it was found in queue for site1
2025-06-23 16:07:28,701 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_calendar.google_calendar.sync because it was found in queue for site1
2025-06-23 16:07:28,762 ERROR scheduler Skipped queueing frappe.monitor.flush because it was found in queue for site1
2025-06-23 17:01:38,309 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for site1
2025-06-23 17:01:38,316 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_schedule_assignment.shift_schedule_assignment.process_auto_shift_creation because it was found in queue for site1
2025-06-23 17:01:38,347 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for site1
2025-06-23 17:01:38,368 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for site1
2025-06-23 17:01:38,421 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for site1
2025-06-23 17:01:38,424 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.update_last_sync_of_checkin because it was found in queue for site1
2025-06-23 17:01:38,436 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for site1
2025-06-23 17:01:38,461 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.process_auto_attendance_for_all_shifts because it was found in queue for site1
2025-06-23 17:01:38,471 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for site1
2025-06-23 17:01:38,504 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for site1
2025-06-23 17:01:38,507 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for site1
2025-06-23 17:01:38,512 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for site1
2025-06-23 17:01:38,520 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for site1
2025-06-23 17:02:39,229 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.update_last_sync_of_checkin because it was found in queue for site1
2025-06-23 17:02:39,240 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.process_auto_attendance_for_all_shifts because it was found in queue for site1
2025-06-23 17:02:39,258 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for site1
2025-06-23 17:02:39,281 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for site1
2025-06-23 17:02:39,324 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for site1
2025-06-23 17:02:39,335 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for site1
2025-06-23 17:02:39,360 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for site1
2025-06-23 17:02:39,369 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for site1
2025-06-23 17:02:39,372 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for site1
2025-06-23 17:02:39,384 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for site1
2025-06-23 17:02:39,423 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for site1
2025-06-23 17:02:39,443 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for site1
2025-06-23 17:02:39,487 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_schedule_assignment.shift_schedule_assignment.process_auto_shift_creation because it was found in queue for site1
2025-06-23 17:03:41,076 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for site1
2025-06-23 17:03:41,099 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for site1
2025-06-23 17:03:41,112 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for site1
2025-06-23 17:03:41,125 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for site1
2025-06-23 17:03:41,132 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for site1
2025-06-23 17:03:41,176 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for site1
2025-06-23 17:03:41,226 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.update_last_sync_of_checkin because it was found in queue for site1
2025-06-23 17:03:41,240 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_schedule_assignment.shift_schedule_assignment.process_auto_shift_creation because it was found in queue for site1
2025-06-23 17:03:41,243 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.process_auto_attendance_for_all_shifts because it was found in queue for site1
2025-06-23 17:03:41,253 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for site1
2025-06-23 17:03:41,273 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for site1
2025-06-23 17:03:41,306 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for site1
2025-06-23 17:03:41,311 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for site1
2025-06-23 17:04:42,398 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.update_last_sync_of_checkin because it was found in queue for site1
2025-06-23 17:04:42,459 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for site1
2025-06-23 17:04:42,508 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for site1
2025-06-23 17:04:42,519 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for site1
2025-06-23 17:04:42,537 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for site1
2025-06-23 17:04:42,551 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for site1
2025-06-23 17:04:42,563 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for site1
2025-06-23 17:04:42,567 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for site1
2025-06-23 17:04:42,580 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.process_auto_attendance_for_all_shifts because it was found in queue for site1
2025-06-23 17:04:42,585 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for site1
2025-06-23 17:04:42,594 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for site1
2025-06-23 17:04:42,603 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for site1
2025-06-23 17:04:42,641 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_schedule_assignment.shift_schedule_assignment.process_auto_shift_creation because it was found in queue for site1
2025-06-23 17:05:43,386 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for site1
2025-06-23 17:05:43,390 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_schedule_assignment.shift_schedule_assignment.process_auto_shift_creation because it was found in queue for site1
2025-06-23 17:05:43,397 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for site1
2025-06-23 17:05:43,409 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for site1
2025-06-23 17:05:43,431 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for site1
2025-06-23 17:05:43,459 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for site1
