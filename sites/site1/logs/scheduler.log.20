2025-06-10 16:03:31,469 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.update_last_sync_of_checkin because it was found in queue for site1
2025-06-10 16:03:31,481 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for site1
2025-06-10 16:03:31,490 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.process_auto_attendance_for_all_shifts because it was found in queue for site1
2025-06-10 16:03:31,494 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for site1
2025-06-10 16:03:31,516 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_schedule_assignment.shift_schedule_assignment.process_auto_shift_creation because it was found in queue for site1
2025-06-10 16:03:31,534 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for site1
2025-06-10 16:03:31,536 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for site1
2025-06-10 16:03:31,538 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for site1
2025-06-10 16:03:31,545 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for site1
2025-06-10 16:03:31,558 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for site1
2025-06-10 16:03:31,588 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for site1
2025-06-10 16:03:31,598 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for site1
2025-06-10 16:04:31,835 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for site1
2025-06-10 16:04:31,855 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for site1
2025-06-10 16:04:31,862 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_schedule_assignment.shift_schedule_assignment.process_auto_shift_creation because it was found in queue for site1
2025-06-10 16:04:31,867 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for site1
2025-06-10 16:04:31,872 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for site1
2025-06-10 16:04:31,879 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for site1
2025-06-10 16:04:31,880 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for site1
2025-06-10 16:04:31,883 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for site1
2025-06-10 16:04:31,884 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.update_last_sync_of_checkin because it was found in queue for site1
2025-06-10 16:04:31,903 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for site1
2025-06-10 16:04:31,905 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.process_auto_attendance_for_all_shifts because it was found in queue for site1
2025-06-10 16:04:31,930 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for site1
2025-06-10 16:04:31,945 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for site1
2025-06-10 18:01:40,752 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for site1
2025-06-10 18:01:40,754 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for site1
2025-06-10 18:01:40,772 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_schedule_assignment.shift_schedule_assignment.process_auto_shift_creation because it was found in queue for site1
2025-06-10 18:01:40,793 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for site1
2025-06-10 18:01:40,812 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for site1
2025-06-10 18:01:40,825 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for site1
2025-06-10 18:01:40,835 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.update_last_sync_of_checkin because it was found in queue for site1
2025-06-10 18:01:40,851 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for site1
2025-06-10 18:01:40,854 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.process_auto_attendance_for_all_shifts because it was found in queue for site1
2025-06-10 18:01:40,857 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for site1
2025-06-10 18:01:40,860 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for site1
2025-06-10 18:01:40,873 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for site1
2025-06-10 18:01:40,893 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for site1
2025-06-10 18:02:41,036 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_schedule_assignment.shift_schedule_assignment.process_auto_shift_creation because it was found in queue for site1
2025-06-10 18:02:41,041 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for site1
2025-06-10 18:02:41,042 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for site1
2025-06-10 18:02:41,046 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.update_last_sync_of_checkin because it was found in queue for site1
2025-06-10 18:02:41,064 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for site1
2025-06-10 18:02:41,066 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for site1
2025-06-10 18:02:41,070 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.process_auto_attendance_for_all_shifts because it was found in queue for site1
2025-06-10 18:02:41,074 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for site1
2025-06-10 18:02:41,082 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for site1
2025-06-10 18:02:41,104 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for site1
2025-06-10 18:02:41,108 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for site1
2025-06-10 18:02:41,112 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for site1
2025-06-10 18:02:41,117 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for site1
2025-06-11 13:50:45,846 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for site1
2025-06-11 13:50:45,848 ERROR scheduler Skipped queueing erpnext.accounts.doctype.process_subscription.process_subscription.create_subscription_process because it was found in queue for site1
2025-06-11 13:50:45,849 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.update_last_sync_of_checkin because it was found in queue for site1
2025-06-11 13:50:45,850 ERROR scheduler Skipped queueing erpnext.assets.doctype.asset.asset.make_post_gl_entry because it was found in queue for site1
2025-06-11 13:50:45,851 ERROR scheduler Skipped queueing frappe.integrations.doctype.s3_backup_settings.s3_backup_settings.take_backups_daily because it was found in queue for site1
2025-06-11 13:50:45,853 ERROR scheduler Skipped queueing csf_tz.stanbic.sftp.process_download_files because it was found in queue for site1
2025-06-11 13:50:45,854 ERROR scheduler Skipped queueing frappe.deferred_insert.save_to_db because it was found in queue for site1
2025-06-11 13:50:45,855 ERROR scheduler Skipped queueing frappe.email.queue.flush because it was found in queue for site1
2025-06-11 13:50:45,856 ERROR scheduler Skipped queueing frappe.monitor.flush because it was found in queue for site1
2025-06-11 13:50:45,857 ERROR scheduler Skipped queueing erpnext.manufacturing.doctype.bom_update_log.bom_update_log.resume_bom_cost_update_jobs because it was found in queue for site1
2025-06-11 13:50:45,858 ERROR scheduler Skipped queueing erpnext.projects.doctype.project.project.project_status_update_reminder because it was found in queue for site1
2025-06-11 13:50:45,859 ERROR scheduler Skipped queueing hrms.hr.utils.generate_leave_encashment because it was found in queue for site1
2025-06-11 13:50:45,860 ERROR scheduler Skipped queueing erpnext.selling.doctype.quotation.quotation.set_expired_status because it was found in queue for site1
2025-06-11 13:50:45,861 ERROR scheduler Skipped queueing csf_tz.csf_tz.doctype.visibility.visibility.trigger_daily_alerts because it was found in queue for site1
2025-06-11 13:50:45,862 ERROR scheduler Skipped queueing hrms.controllers.employee_reminders.send_work_anniversary_reminders because it was found in queue for site1
2025-06-11 13:50:45,863 ERROR scheduler Skipped queueing hrms.hr.doctype.daily_work_summary_group.daily_work_summary_group.trigger_emails because it was found in queue for site1
2025-06-11 13:50:45,864 ERROR scheduler Skipped queueing erpnext.buying.doctype.supplier_quotation.supplier_quotation.set_expired_status because it was found in queue for site1
2025-06-11 13:50:45,865 ERROR scheduler Skipped queueing erpnext.setup.doctype.email_digest.email_digest.send because it was found in queue for site1
2025-06-11 13:50:45,866 ERROR scheduler Skipped queueing frappe.integrations.doctype.dropbox_settings.dropbox_settings.take_backups_daily because it was found in queue for site1
2025-06-11 13:50:45,866 ERROR scheduler Skipped queueing frappe.email.doctype.auto_email_report.auto_email_report.send_daily because it was found in queue for site1
2025-06-11 13:50:45,867 ERROR scheduler Skipped queueing erpnext.assets.doctype.asset.depreciation.post_depreciation_entries because it was found in queue for site1
2025-06-11 13:50:45,869 ERROR scheduler Skipped queueing erpnext.projects.doctype.project.project.update_project_sales_billing because it was found in queue for site1
2025-06-11 13:50:45,871 ERROR scheduler Skipped queueing csf_tz.custom_api.auto_close_dn because it was found in queue for site1
2025-06-11 13:50:45,872 ERROR scheduler Skipped queueing erpnext.buying.doctype.supplier_scorecard.supplier_scorecard.refresh_scorecards because it was found in queue for site1
2025-06-11 13:50:45,873 ERROR scheduler Skipped queueing frappe.desk.doctype.event.event.send_event_digest because it was found in queue for site1
2025-06-11 13:50:45,874 ERROR scheduler Skipped queueing erpnext.projects.doctype.task.task.set_tasks_as_overdue because it was found in queue for site1
2025-06-11 13:50:45,875 ERROR scheduler Skipped queueing erpnext.crm.doctype.email_campaign.email_campaign.send_email_to_leads_or_contacts because it was found in queue for site1
2025-06-11 13:50:45,876 ERROR scheduler Skipped queueing erpnext.accounts.doctype.process_statement_of_accounts.process_statement_of_accounts.send_auto_email because it was found in queue for site1
2025-06-11 13:50:45,877 ERROR scheduler Skipped queueing erpnext.erpnext_integrations.doctype.plaid_settings.plaid_settings.automatic_synchronization because it was found in queue for site1
2025-06-11 13:50:45,878 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_daily_updates because it was found in queue for site1
2025-06-11 13:50:45,879 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.process_auto_attendance_for_all_shifts because it was found in queue for site1
2025-06-11 13:50:45,880 ERROR scheduler Skipped queueing frappe.social.doctype.energy_point_settings.energy_point_settings.allocate_review_points because it was found in queue for site1
2025-06-11 13:50:45,881 ERROR scheduler Skipped queueing erpnext.stock.reorder_item.reorder_item because it was found in queue for site1
2025-06-11 13:50:45,882 ERROR scheduler Skipped queueing csf_tz.csf_tz.doctype.vehicle_fine_record.vehicle_fine_record.check_fine_all_vehicles because it was found in queue for site1
2025-06-11 13:50:45,883 ERROR scheduler Skipped queueing erpnext.utilities.doctype.video.video.update_youtube_data because it was found in queue for site1
2025-06-11 13:50:45,885 ERROR scheduler Skipped queueing frappe.model.utils.link_count.update_link_count because it was found in queue for site1
2025-06-11 13:50:45,886 ERROR scheduler Skipped queueing erpnext.quality_management.doctype.quality_review.quality_review.review because it was found in queue for site1
2025-06-11 13:50:45,888 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.pull because it was found in queue for site1
2025-06-11 13:50:45,889 ERROR scheduler Skipped queueing erpnext.support.doctype.issue.issue.auto_close_tickets because it was found in queue for site1
2025-06-11 13:50:45,890 ERROR scheduler Skipped queueing frappe.utils.global_search.sync_global_search because it was found in queue for site1
2025-06-11 13:50:45,891 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_schedule_assignment.shift_schedule_assignment.process_auto_shift_creation because it was found in queue for site1
2025-06-11 13:50:45,892 ERROR scheduler Skipped queueing propms.auto_custom.statusChangeAfterLeaseExpire because it was found in queue for site1
2025-06-11 13:50:45,893 ERROR scheduler Skipped queueing propms.lease_invoice.leaseInvoiceAutoCreate because it was found in queue for site1
2025-06-11 13:50:45,893 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.remove_unverified_record because it was found in queue for site1
2025-06-11 13:50:45,894 ERROR scheduler Skipped queueing csf_tz.csftz_hooks.additional_salary.generate_additional_salary_records because it was found in queue for site1
2025-06-11 13:50:45,895 ERROR scheduler Skipped queueing csf_tz.stanbic.sftp.sync_all_stanbank_files because it was found in queue for site1
2025-06-11 13:50:45,896 ERROR scheduler Skipped queueing csf_tz.bank_api.reconciliation because it was found in queue for site1
2025-06-11 13:50:45,897 ERROR scheduler Skipped queueing erpnext.accounts.utils.auto_create_exchange_rate_revaluation_daily because it was found in queue for site1
2025-06-11 13:50:45,898 ERROR scheduler Skipped queueing erpnext.stock.doctype.serial_no.serial_no.update_maintenance_status because it was found in queue for site1
2025-06-11 13:50:45,899 ERROR scheduler Skipped queueing erpnext.manufacturing.doctype.bom_update_tool.bom_update_tool.auto_update_latest_price_in_all_boms because it was found in queue for site1
2025-06-11 13:50:45,899 ERROR scheduler Skipped queueing erpnext.crm.doctype.opportunity.opportunity.auto_close_opportunity because it was found in queue for site1
2025-06-11 13:50:45,900 ERROR scheduler Skipped queueing hrms.hr.utils.allocate_earned_leaves because it was found in queue for site1
2025-06-11 13:50:45,902 ERROR scheduler Skipped queueing propms.lease_invoice_schedule.make_lease_invoice_schedule because it was found in queue for site1
2025-06-11 13:50:45,903 ERROR scheduler Skipped queueing frappe.desk.notifications.clear_notifications because it was found in queue for site1
2025-06-11 13:50:45,904 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for site1
2025-06-11 13:50:45,906 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for site1
2025-06-11 13:50:45,907 ERROR scheduler Skipped queueing hrms.hr.doctype.interview.interview.send_daily_feedback_reminder because it was found in queue for site1
2025-06-11 13:50:45,907 ERROR scheduler Skipped queueing erpnext.crm.doctype.email_campaign.email_campaign.set_email_campaign_status because it was found in queue for site1
2025-06-11 13:50:45,908 ERROR scheduler Skipped queueing csf_tz.custom_api.create_delivery_note_for_all_pending_sales_invoice because it was found in queue for site1
2025-06-11 13:50:45,909 ERROR scheduler Skipped queueing erpnext.accounts.doctype.gl_entry.gl_entry.rename_gle_sle_docs because it was found in queue for site1
2025-06-11 13:50:45,910 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for site1
2025-06-11 13:50:45,911 ERROR scheduler Skipped queueing frappe.email.doctype.newsletter.newsletter.send_scheduled_email because it was found in queue for site1
2025-06-11 13:50:45,912 ERROR scheduler Skipped queueing hrms.controllers.employee_reminders.send_birthday_reminders because it was found in queue for site1
2025-06-11 13:50:45,913 ERROR scheduler Skipped queueing hrms.hr.doctype.leave_ledger_entry.leave_ledger_entry.process_expired_allocation because it was found in queue for site1
2025-06-11 13:50:45,913 ERROR scheduler Skipped queueing frappe.automation.doctype.reminder.reminder.send_reminders because it was found in queue for site1
2025-06-11 13:50:45,914 ERROR scheduler Skipped queueing erpnext.accounts.utils.run_ledger_health_checks because it was found in queue for site1
2025-06-11 13:50:45,915 ERROR scheduler Skipped queueing frappe.sessions.clear_expired_sessions because it was found in queue for site1
2025-06-11 13:50:45,916 ERROR scheduler Skipped queueing frappe.core.doctype.log_settings.log_settings.run_log_clean_up because it was found in queue for site1
2025-06-11 13:50:45,917 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for site1
2025-06-11 13:50:45,918 ERROR scheduler Skipped queueing csf_tz.csftz_hooks.material_request.auto_close_material_request because it was found in queue for site1
2025-06-11 13:50:45,920 ERROR scheduler Skipped queueing hrms.hr.doctype.job_opening.job_opening.close_expired_job_openings because it was found in queue for site1
2025-06-11 13:50:45,921 ERROR scheduler Skipped queueing erpnext.accounts.doctype.fiscal_year.fiscal_year.auto_create_fiscal_year because it was found in queue for site1
2025-06-11 13:50:45,922 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_calendar.google_calendar.sync because it was found in queue for site1
2025-06-11 13:50:45,923 ERROR scheduler Skipped queueing hrms.hr.doctype.daily_work_summary_group.daily_work_summary_group.send_summary because it was found in queue for site1
2025-06-11 13:50:45,924 ERROR scheduler Skipped queueing csf_tz.csftz_hooks.items_revaluation.process_incorrect_balance_qty because it was found in queue for site1
2025-06-11 13:50:45,925 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for site1
2025-06-11 13:50:45,926 ERROR scheduler Skipped queueing erpnext.projects.doctype.project.project.collect_project_status because it was found in queue for site1
2025-06-11 13:50:45,927 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for site1
2025-06-11 13:50:45,928 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.notify_unreplied because it was found in queue for site1
2025-06-11 13:50:45,930 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for site1
2025-06-11 13:50:45,930 ERROR scheduler Skipped queueing propms.auto_custom.statusChangeBeforeLeaseExpire because it was found in queue for site1
2025-06-11 13:50:45,931 ERROR scheduler Skipped queueing frappe.automation.doctype.auto_repeat.auto_repeat.make_auto_repeat_entry because it was found in queue for site1
2025-06-11 13:50:45,933 ERROR scheduler Skipped queueing erpnext.projects.doctype.project.project.hourly_reminder because it was found in queue for site1
2025-06-11 13:50:45,934 ERROR scheduler Skipped queueing erpnext.projects.doctype.project.project.send_project_status_email_to_users because it was found in queue for site1
2025-06-11 13:50:45,934 ERROR scheduler Skipped queueing erpnext.crm.utils.open_leads_opportunities_based_on_todays_event because it was found in queue for site1
2025-06-11 13:50:45,935 ERROR scheduler Skipped queueing erpnext.assets.doctype.asset_maintenance_log.asset_maintenance_log.update_asset_maintenance_log_status because it was found in queue for site1
2025-06-11 13:50:45,937 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_drive.google_drive.daily_backup because it was found in queue for site1
2025-06-11 13:50:45,939 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_contacts.google_contacts.sync because it was found in queue for site1
2025-06-11 13:50:45,939 ERROR scheduler Skipped queueing erpnext.controllers.accounts_controller.update_invoice_status because it was found in queue for site1
2025-06-11 13:50:45,940 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for site1
2025-06-11 13:50:45,942 ERROR scheduler Skipped queueing erpnext.crm.doctype.contract.contract.update_status_for_contracts because it was found in queue for site1
2025-06-11 13:50:45,943 ERROR scheduler Skipped queueing erpnext.assets.doctype.asset.asset.update_maintenance_status because it was found in queue for site1
2025-06-11 13:50:45,944 ERROR scheduler Skipped queueing erpnext.setup.doctype.company.company.cache_companies_monthly_sales_history because it was found in queue for site1
2025-06-11 13:50:45,945 ERROR scheduler Skipped queueing hrms.hr.doctype.interview.interview.send_interview_reminder because it was found in queue for site1
2025-06-11 13:50:45,946 ERROR scheduler Skipped queueing erpnext.support.doctype.service_level_agreement.service_level_agreement.check_agreement_status because it was found in queue for site1
2025-06-11 13:50:45,947 ERROR scheduler Skipped queueing csf_tz.csf_tz.doctype.parking_bill.parking_bill.check_bills_all_vehicles because it was found in queue for site1
2025-06-11 13:50:45,948 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for site1
2025-06-11 13:50:45,949 ERROR scheduler Skipped queueing frappe.email.doctype.notification.notification.trigger_daily_alerts because it was found in queue for site1
2025-06-11 16:14:16,780 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.process_auto_attendance_for_all_shifts because it was found in queue for site1
2025-06-11 16:14:16,782 ERROR scheduler Skipped queueing erpnext.selling.doctype.quotation.quotation.set_expired_status because it was found in queue for site1
2025-06-11 16:14:16,784 ERROR scheduler Skipped queueing csf_tz.bank_api.reconciliation because it was found in queue for site1
2025-06-11 16:14:16,785 ERROR scheduler Skipped queueing erpnext.support.doctype.service_level_agreement.service_level_agreement.check_agreement_status because it was found in queue for site1
2025-06-11 16:14:16,786 ERROR scheduler Skipped queueing erpnext.accounts.utils.run_ledger_health_checks because it was found in queue for site1
2025-06-11 16:14:16,787 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_schedule_assignment.shift_schedule_assignment.process_auto_shift_creation because it was found in queue for site1
2025-06-11 16:14:16,788 ERROR scheduler Skipped queueing frappe.email.doctype.newsletter.newsletter.send_scheduled_email because it was found in queue for site1
2025-06-11 16:14:16,789 ERROR scheduler Skipped queueing hrms.controllers.employee_reminders.send_work_anniversary_reminders because it was found in queue for site1
2025-06-11 16:14:16,790 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.update_last_sync_of_checkin because it was found in queue for site1
2025-06-11 16:14:16,790 ERROR scheduler Skipped queueing frappe.sessions.clear_expired_sessions because it was found in queue for site1
2025-06-11 16:14:16,792 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for site1
2025-06-11 16:14:16,793 ERROR scheduler Skipped queueing hrms.hr.doctype.daily_work_summary_group.daily_work_summary_group.send_summary because it was found in queue for site1
2025-06-11 16:14:16,793 ERROR scheduler Skipped queueing erpnext.projects.doctype.project.project.project_status_update_reminder because it was found in queue for site1
2025-06-11 16:14:16,795 ERROR scheduler Skipped queueing erpnext.crm.utils.open_leads_opportunities_based_on_todays_event because it was found in queue for site1
2025-06-11 16:14:16,796 ERROR scheduler Skipped queueing csf_tz.custom_api.create_delivery_note_for_all_pending_sales_invoice because it was found in queue for site1
2025-06-11 16:14:16,797 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for site1
2025-06-11 16:14:16,798 ERROR scheduler Skipped queueing frappe.desk.notifications.clear_notifications because it was found in queue for site1
2025-06-11 16:14:16,799 ERROR scheduler Skipped queueing erpnext.projects.doctype.project.project.send_project_status_email_to_users because it was found in queue for site1
2025-06-11 16:14:16,801 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for site1
2025-06-11 16:14:16,802 ERROR scheduler Skipped queueing erpnext.erpnext_integrations.doctype.plaid_settings.plaid_settings.automatic_synchronization because it was found in queue for site1
2025-06-11 16:14:16,803 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_contacts.google_contacts.sync because it was found in queue for site1
2025-06-11 16:14:16,804 ERROR scheduler Skipped queueing csf_tz.csftz_hooks.items_revaluation.process_incorrect_balance_qty because it was found in queue for site1
2025-06-11 16:14:16,805 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_calendar.google_calendar.sync because it was found in queue for site1
2025-06-11 16:14:16,806 ERROR scheduler Skipped queueing erpnext.buying.doctype.supplier_quotation.supplier_quotation.set_expired_status because it was found in queue for site1
2025-06-11 16:14:16,808 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.pull because it was found in queue for site1
2025-06-11 16:14:16,809 ERROR scheduler Skipped queueing erpnext.crm.doctype.opportunity.opportunity.auto_close_opportunity because it was found in queue for site1
2025-06-11 16:14:16,810 ERROR scheduler Skipped queueing erpnext.accounts.doctype.fiscal_year.fiscal_year.auto_create_fiscal_year because it was found in queue for site1
2025-06-11 16:14:16,811 ERROR scheduler Skipped queueing erpnext.setup.doctype.company.company.cache_companies_monthly_sales_history because it was found in queue for site1
2025-06-11 16:14:16,812 ERROR scheduler Skipped queueing frappe.core.doctype.log_settings.log_settings.run_log_clean_up because it was found in queue for site1
2025-06-11 16:14:16,812 ERROR scheduler Skipped queueing erpnext.accounts.doctype.process_subscription.process_subscription.create_subscription_process because it was found in queue for site1
2025-06-11 16:14:16,813 ERROR scheduler Skipped queueing erpnext.accounts.doctype.gl_entry.gl_entry.rename_gle_sle_docs because it was found in queue for site1
2025-06-11 16:14:16,815 ERROR scheduler Skipped queueing erpnext.accounts.doctype.process_statement_of_accounts.process_statement_of_accounts.send_auto_email because it was found in queue for site1
2025-06-11 16:14:16,817 ERROR scheduler Skipped queueing hrms.hr.doctype.interview.interview.send_interview_reminder because it was found in queue for site1
2025-06-11 16:14:16,818 ERROR scheduler Skipped queueing csf_tz.csf_tz.doctype.parking_bill.parking_bill.check_bills_all_vehicles because it was found in queue for site1
2025-06-11 16:14:16,820 ERROR scheduler Skipped queueing erpnext.stock.reorder_item.reorder_item because it was found in queue for site1
2025-06-11 16:14:16,821 ERROR scheduler Skipped queueing frappe.integrations.doctype.dropbox_settings.dropbox_settings.take_backups_daily because it was found in queue for site1
2025-06-11 16:14:16,822 ERROR scheduler Skipped queueing erpnext.projects.doctype.project.project.update_project_sales_billing because it was found in queue for site1
2025-06-11 16:14:16,823 ERROR scheduler Skipped queueing erpnext.projects.doctype.task.task.set_tasks_as_overdue because it was found in queue for site1
2025-06-11 16:14:16,824 ERROR scheduler Skipped queueing csf_tz.stanbic.sftp.sync_all_stanbank_files because it was found in queue for site1
2025-06-11 16:14:16,825 ERROR scheduler Skipped queueing erpnext.assets.doctype.asset.asset.update_maintenance_status because it was found in queue for site1
2025-06-11 16:14:16,826 ERROR scheduler Skipped queueing hrms.hr.doctype.job_opening.job_opening.close_expired_job_openings because it was found in queue for site1
2025-06-11 16:14:16,827 ERROR scheduler Skipped queueing frappe.monitor.flush because it was found in queue for site1
2025-06-11 16:14:16,828 ERROR scheduler Skipped queueing erpnext.assets.doctype.asset.asset.make_post_gl_entry because it was found in queue for site1
2025-06-11 16:14:16,829 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_daily_updates because it was found in queue for site1
2025-06-11 16:14:16,830 ERROR scheduler Skipped queueing erpnext.projects.doctype.project.project.collect_project_status because it was found in queue for site1
2025-06-11 16:14:16,831 ERROR scheduler Skipped queueing erpnext.setup.doctype.email_digest.email_digest.send because it was found in queue for site1
2025-06-11 16:14:16,833 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for site1
2025-06-11 16:14:16,834 ERROR scheduler Skipped queueing hrms.controllers.employee_reminders.send_birthday_reminders because it was found in queue for site1
2025-06-11 16:14:16,835 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.remove_unverified_record because it was found in queue for site1
2025-06-11 16:14:16,836 ERROR scheduler Skipped queueing csf_tz.csftz_hooks.additional_salary.generate_additional_salary_records because it was found in queue for site1
2025-06-11 16:14:16,837 ERROR scheduler Skipped queueing csf_tz.stanbic.sftp.process_download_files because it was found in queue for site1
2025-06-11 16:14:16,838 ERROR scheduler Skipped queueing frappe.social.doctype.energy_point_settings.energy_point_settings.allocate_review_points because it was found in queue for site1
2025-06-11 16:14:16,839 ERROR scheduler Skipped queueing erpnext.assets.doctype.asset_maintenance_log.asset_maintenance_log.update_asset_maintenance_log_status because it was found in queue for site1
2025-06-11 16:14:16,840 ERROR scheduler Skipped queueing erpnext.support.doctype.issue.issue.auto_close_tickets because it was found in queue for site1
2025-06-11 16:14:16,841 ERROR scheduler Skipped queueing frappe.automation.doctype.auto_repeat.auto_repeat.make_auto_repeat_entry because it was found in queue for site1
2025-06-11 16:14:16,841 ERROR scheduler Skipped queueing frappe.desk.doctype.event.event.send_event_digest because it was found in queue for site1
2025-06-11 16:14:16,842 ERROR scheduler Skipped queueing erpnext.crm.doctype.email_campaign.email_campaign.set_email_campaign_status because it was found in queue for site1
2025-06-11 16:14:16,843 ERROR scheduler Skipped queueing hrms.hr.doctype.daily_work_summary_group.daily_work_summary_group.trigger_emails because it was found in queue for site1
2025-06-11 16:14:16,844 ERROR scheduler Skipped queueing csf_tz.csf_tz.doctype.vehicle_fine_record.vehicle_fine_record.check_fine_all_vehicles because it was found in queue for site1
2025-06-11 16:14:16,845 ERROR scheduler Skipped queueing frappe.deferred_insert.save_to_db because it was found in queue for site1
2025-06-11 16:14:16,846 ERROR scheduler Skipped queueing propms.lease_invoice_schedule.make_lease_invoice_schedule because it was found in queue for site1
2025-06-11 16:14:16,846 ERROR scheduler Skipped queueing propms.auto_custom.statusChangeBeforeLeaseExpire because it was found in queue for site1
2025-06-11 16:14:16,847 ERROR scheduler Skipped queueing csf_tz.csftz_hooks.material_request.auto_close_material_request because it was found in queue for site1
2025-06-11 16:14:16,849 ERROR scheduler Skipped queueing erpnext.assets.doctype.asset.depreciation.post_depreciation_entries because it was found in queue for site1
2025-06-11 16:14:16,850 ERROR scheduler Skipped queueing frappe.email.doctype.notification.notification.trigger_daily_alerts because it was found in queue for site1
2025-06-11 16:14:16,851 ERROR scheduler Skipped queueing csf_tz.csf_tz.doctype.visibility.visibility.trigger_daily_alerts because it was found in queue for site1
2025-06-11 16:14:16,854 ERROR scheduler Skipped queueing erpnext.accounts.utils.auto_create_exchange_rate_revaluation_daily because it was found in queue for site1
2025-06-11 16:14:16,855 ERROR scheduler Skipped queueing frappe.model.utils.link_count.update_link_count because it was found in queue for site1
2025-06-11 16:14:16,855 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for site1
2025-06-11 16:14:16,856 ERROR scheduler Skipped queueing frappe.email.doctype.auto_email_report.auto_email_report.send_daily because it was found in queue for site1
2025-06-11 16:14:16,857 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for site1
2025-06-11 16:14:16,858 ERROR scheduler Skipped queueing erpnext.buying.doctype.supplier_scorecard.supplier_scorecard.refresh_scorecards because it was found in queue for site1
2025-06-11 16:14:16,859 ERROR scheduler Skipped queueing hrms.hr.utils.generate_leave_encashment because it was found in queue for site1
2025-06-11 16:14:16,860 ERROR scheduler Skipped queueing erpnext.controllers.accounts_controller.update_invoice_status because it was found in queue for site1
2025-06-11 16:14:16,860 ERROR scheduler Skipped queueing erpnext.quality_management.doctype.quality_review.quality_review.review because it was found in queue for site1
2025-06-11 16:14:16,861 ERROR scheduler Skipped queueing frappe.email.queue.flush because it was found in queue for site1
2025-06-11 16:14:16,862 ERROR scheduler Skipped queueing hrms.hr.utils.allocate_earned_leaves because it was found in queue for site1
2025-06-11 16:14:16,863 ERROR scheduler Skipped queueing hrms.hr.doctype.leave_ledger_entry.leave_ledger_entry.process_expired_allocation because it was found in queue for site1
2025-06-11 16:14:16,865 ERROR scheduler Skipped queueing frappe.automation.doctype.reminder.reminder.send_reminders because it was found in queue for site1
2025-06-11 16:14:16,866 ERROR scheduler Skipped queueing frappe.integrations.doctype.s3_backup_settings.s3_backup_settings.take_backups_daily because it was found in queue for site1
2025-06-11 16:14:16,868 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.notify_unreplied because it was found in queue for site1
2025-06-11 16:14:16,869 ERROR scheduler Skipped queueing erpnext.utilities.doctype.video.video.update_youtube_data because it was found in queue for site1
2025-06-11 16:14:16,870 ERROR scheduler Skipped queueing erpnext.crm.doctype.contract.contract.update_status_for_contracts because it was found in queue for site1
2025-06-11 16:14:16,871 ERROR scheduler Skipped queueing hrms.hr.doctype.interview.interview.send_daily_feedback_reminder because it was found in queue for site1
2025-06-11 16:14:16,872 ERROR scheduler Skipped queueing erpnext.manufacturing.doctype.bom_update_tool.bom_update_tool.auto_update_latest_price_in_all_boms because it was found in queue for site1
2025-06-11 16:14:16,873 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_drive.google_drive.daily_backup because it was found in queue for site1
2025-06-11 16:14:16,874 ERROR scheduler Skipped queueing erpnext.crm.doctype.email_campaign.email_campaign.send_email_to_leads_or_contacts because it was found in queue for site1
2025-06-11 16:14:16,875 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for site1
2025-06-11 16:14:16,876 ERROR scheduler Skipped queueing propms.lease_invoice.leaseInvoiceAutoCreate because it was found in queue for site1
2025-06-11 16:14:16,877 ERROR scheduler Skipped queueing erpnext.projects.doctype.project.project.hourly_reminder because it was found in queue for site1
2025-06-11 16:14:16,878 ERROR scheduler Skipped queueing frappe.utils.global_search.sync_global_search because it was found in queue for site1
2025-06-11 16:14:16,879 ERROR scheduler Skipped queueing csf_tz.custom_api.auto_close_dn because it was found in queue for site1
2025-06-11 16:14:16,880 ERROR scheduler Skipped queueing erpnext.manufacturing.doctype.bom_update_log.bom_update_log.resume_bom_cost_update_jobs because it was found in queue for site1
2025-06-11 16:14:16,881 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for site1
2025-06-11 16:14:16,882 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for site1
2025-06-11 16:14:16,883 ERROR scheduler Skipped queueing propms.auto_custom.statusChangeAfterLeaseExpire because it was found in queue for site1
2025-06-11 16:14:16,885 ERROR scheduler Skipped queueing erpnext.stock.doctype.serial_no.serial_no.update_maintenance_status because it was found in queue for site1
2025-06-11 16:14:16,886 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for site1
2025-06-11 16:15:17,021 ERROR scheduler Skipped queueing hrms.hr.utils.allocate_earned_leaves because it was found in queue for site1
2025-06-11 16:15:17,022 ERROR scheduler Skipped queueing csf_tz.csf_tz.doctype.visibility.visibility.trigger_daily_alerts because it was found in queue for site1
2025-06-11 16:15:17,023 ERROR scheduler Skipped queueing propms.lease_invoice_schedule.make_lease_invoice_schedule because it was found in queue for site1
2025-06-11 16:15:17,026 ERROR scheduler Skipped queueing erpnext.setup.doctype.company.company.cache_companies_monthly_sales_history because it was found in queue for site1
2025-06-11 16:15:17,028 ERROR scheduler Skipped queueing hrms.hr.doctype.interview.interview.send_daily_feedback_reminder because it was found in queue for site1
2025-06-11 16:15:17,029 ERROR scheduler Skipped queueing erpnext.stock.doctype.serial_no.serial_no.update_maintenance_status because it was found in queue for site1
2025-06-11 16:15:17,030 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.notify_unreplied because it was found in queue for site1
2025-06-11 16:15:17,031 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.update_last_sync_of_checkin because it was found in queue for site1
2025-06-11 16:15:17,032 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_calendar.google_calendar.sync because it was found in queue for site1
2025-06-11 16:15:17,034 ERROR scheduler Skipped queueing hrms.hr.doctype.job_opening.job_opening.close_expired_job_openings because it was found in queue for site1
2025-06-11 16:15:17,034 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for site1
2025-06-11 16:15:17,035 ERROR scheduler Skipped queueing erpnext.projects.doctype.project.project.send_project_status_email_to_users because it was found in queue for site1
2025-06-11 16:15:17,036 ERROR scheduler Skipped queueing frappe.monitor.flush because it was found in queue for site1
2025-06-11 16:15:17,037 ERROR scheduler Skipped queueing erpnext.accounts.utils.run_ledger_health_checks because it was found in queue for site1
2025-06-11 16:15:17,039 ERROR scheduler Skipped queueing csf_tz.csftz_hooks.additional_salary.generate_additional_salary_records because it was found in queue for site1
2025-06-11 16:15:17,040 ERROR scheduler Skipped queueing csf_tz.csf_tz.doctype.vehicle_fine_record.vehicle_fine_record.check_fine_all_vehicles because it was found in queue for site1
2025-06-11 16:15:17,041 ERROR scheduler Skipped queueing frappe.model.utils.link_count.update_link_count because it was found in queue for site1
2025-06-11 16:15:17,042 ERROR scheduler Skipped queueing frappe.integrations.doctype.s3_backup_settings.s3_backup_settings.take_backups_daily because it was found in queue for site1
2025-06-11 16:15:17,044 ERROR scheduler Skipped queueing erpnext.support.doctype.issue.issue.auto_close_tickets because it was found in queue for site1
2025-06-11 16:15:17,047 ERROR scheduler Skipped queueing propms.lease_invoice.leaseInvoiceAutoCreate because it was found in queue for site1
2025-06-11 16:15:17,048 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for site1
2025-06-11 16:15:17,049 ERROR scheduler Skipped queueing hrms.controllers.employee_reminders.send_work_anniversary_reminders because it was found in queue for site1
2025-06-11 16:15:17,050 ERROR scheduler Skipped queueing csf_tz.csftz_hooks.items_revaluation.process_incorrect_balance_qty because it was found in queue for site1
2025-06-11 16:15:17,051 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.process_auto_attendance_for_all_shifts because it was found in queue for site1
2025-06-11 16:15:17,052 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_contacts.google_contacts.sync because it was found in queue for site1
2025-06-11 16:15:17,052 ERROR scheduler Skipped queueing frappe.core.doctype.log_settings.log_settings.run_log_clean_up because it was found in queue for site1
2025-06-11 16:15:17,053 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for site1
2025-06-11 16:15:17,054 ERROR scheduler Skipped queueing erpnext.selling.doctype.quotation.quotation.set_expired_status because it was found in queue for site1
2025-06-11 16:15:17,055 ERROR scheduler Skipped queueing erpnext.manufacturing.doctype.bom_update_tool.bom_update_tool.auto_update_latest_price_in_all_boms because it was found in queue for site1
2025-06-11 16:15:17,056 ERROR scheduler Skipped queueing frappe.automation.doctype.auto_repeat.auto_repeat.make_auto_repeat_entry because it was found in queue for site1
2025-06-11 16:15:17,056 ERROR scheduler Skipped queueing csf_tz.custom_api.create_delivery_note_for_all_pending_sales_invoice because it was found in queue for site1
2025-06-11 16:15:17,057 ERROR scheduler Skipped queueing erpnext.controllers.accounts_controller.update_invoice_status because it was found in queue for site1
2025-06-11 16:15:17,058 ERROR scheduler Skipped queueing frappe.email.doctype.auto_email_report.auto_email_report.send_daily because it was found in queue for site1
2025-06-11 16:15:17,059 ERROR scheduler Skipped queueing frappe.desk.notifications.clear_notifications because it was found in queue for site1
2025-06-11 16:15:17,061 ERROR scheduler Skipped queueing frappe.email.doctype.newsletter.newsletter.send_scheduled_email because it was found in queue for site1
2025-06-11 16:15:17,062 ERROR scheduler Skipped queueing erpnext.accounts.doctype.gl_entry.gl_entry.rename_gle_sle_docs because it was found in queue for site1
2025-06-11 16:15:17,063 ERROR scheduler Skipped queueing erpnext.crm.doctype.contract.contract.update_status_for_contracts because it was found in queue for site1
2025-06-11 16:15:17,064 ERROR scheduler Skipped queueing hrms.hr.doctype.interview.interview.send_interview_reminder because it was found in queue for site1
2025-06-11 16:15:17,065 ERROR scheduler Skipped queueing csf_tz.custom_api.auto_close_dn because it was found in queue for site1
2025-06-11 16:15:17,066 ERROR scheduler Skipped queueing erpnext.projects.doctype.project.project.hourly_reminder because it was found in queue for site1
2025-06-11 16:15:17,067 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for site1
2025-06-11 16:15:17,068 ERROR scheduler Skipped queueing erpnext.projects.doctype.task.task.set_tasks_as_overdue because it was found in queue for site1
2025-06-11 16:15:17,069 ERROR scheduler Skipped queueing frappe.automation.doctype.reminder.reminder.send_reminders because it was found in queue for site1
2025-06-11 16:15:17,070 ERROR scheduler Skipped queueing frappe.sessions.clear_expired_sessions because it was found in queue for site1
2025-06-11 16:15:17,071 ERROR scheduler Skipped queueing csf_tz.bank_api.reconciliation because it was found in queue for site1
2025-06-11 16:15:17,071 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for site1
2025-06-11 16:15:17,072 ERROR scheduler Skipped queueing erpnext.crm.doctype.email_campaign.email_campaign.send_email_to_leads_or_contacts because it was found in queue for site1
2025-06-11 16:15:17,073 ERROR scheduler Skipped queueing hrms.hr.utils.generate_leave_encashment because it was found in queue for site1
2025-06-11 16:15:17,074 ERROR scheduler Skipped queueing hrms.controllers.employee_reminders.send_birthday_reminders because it was found in queue for site1
2025-06-11 16:15:17,074 ERROR scheduler Skipped queueing erpnext.utilities.doctype.video.video.update_youtube_data because it was found in queue for site1
2025-06-11 16:15:17,076 ERROR scheduler Skipped queueing frappe.email.doctype.notification.notification.trigger_daily_alerts because it was found in queue for site1
2025-06-11 16:15:17,078 ERROR scheduler Skipped queueing erpnext.assets.doctype.asset.asset.update_maintenance_status because it was found in queue for site1
2025-06-11 16:15:17,079 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for site1
2025-06-11 16:15:17,081 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.remove_unverified_record because it was found in queue for site1
2025-06-11 16:15:17,081 ERROR scheduler Skipped queueing erpnext.buying.doctype.supplier_quotation.supplier_quotation.set_expired_status because it was found in queue for site1
2025-06-11 16:15:17,082 ERROR scheduler Skipped queueing erpnext.accounts.doctype.fiscal_year.fiscal_year.auto_create_fiscal_year because it was found in queue for site1
2025-06-11 16:15:17,083 ERROR scheduler Skipped queueing erpnext.manufacturing.doctype.bom_update_log.bom_update_log.resume_bom_cost_update_jobs because it was found in queue for site1
2025-06-11 16:15:17,084 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.pull because it was found in queue for site1
2025-06-11 16:15:17,085 ERROR scheduler Skipped queueing csf_tz.csftz_hooks.material_request.auto_close_material_request because it was found in queue for site1
2025-06-11 16:15:17,086 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for site1
2025-06-11 16:15:17,087 ERROR scheduler Skipped queueing hrms.hr.doctype.daily_work_summary_group.daily_work_summary_group.trigger_emails because it was found in queue for site1
2025-06-11 16:15:17,088 ERROR scheduler Skipped queueing frappe.email.queue.flush because it was found in queue for site1
2025-06-11 16:15:17,089 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_daily_updates because it was found in queue for site1
2025-06-11 16:15:17,089 ERROR scheduler Skipped queueing frappe.deferred_insert.save_to_db because it was found in queue for site1
2025-06-11 16:15:17,091 ERROR scheduler Skipped queueing erpnext.erpnext_integrations.doctype.plaid_settings.plaid_settings.automatic_synchronization because it was found in queue for site1
2025-06-11 16:15:17,092 ERROR scheduler Skipped queueing erpnext.crm.doctype.opportunity.opportunity.auto_close_opportunity because it was found in queue for site1
2025-06-11 16:15:17,093 ERROR scheduler Skipped queueing erpnext.accounts.utils.auto_create_exchange_rate_revaluation_daily because it was found in queue for site1
2025-06-11 16:15:17,095 ERROR scheduler Skipped queueing erpnext.assets.doctype.asset.asset.make_post_gl_entry because it was found in queue for site1
2025-06-11 16:15:17,096 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for site1
2025-06-11 16:15:17,097 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for site1
2025-06-11 16:15:17,098 ERROR scheduler Skipped queueing erpnext.accounts.doctype.process_statement_of_accounts.process_statement_of_accounts.send_auto_email because it was found in queue for site1
2025-06-11 16:15:17,099 ERROR scheduler Skipped queueing frappe.desk.doctype.event.event.send_event_digest because it was found in queue for site1
2025-06-11 16:15:17,100 ERROR scheduler Skipped queueing frappe.social.doctype.energy_point_settings.energy_point_settings.allocate_review_points because it was found in queue for site1
2025-06-11 16:15:17,101 ERROR scheduler Skipped queueing csf_tz.stanbic.sftp.process_download_files because it was found in queue for site1
2025-06-11 16:15:17,102 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_schedule_assignment.shift_schedule_assignment.process_auto_shift_creation because it was found in queue for site1
2025-06-11 16:15:17,103 ERROR scheduler Skipped queueing erpnext.projects.doctype.project.project.project_status_update_reminder because it was found in queue for site1
2025-06-11 16:15:17,103 ERROR scheduler Skipped queueing csf_tz.csf_tz.doctype.parking_bill.parking_bill.check_bills_all_vehicles because it was found in queue for site1
2025-06-11 16:15:17,104 ERROR scheduler Skipped queueing erpnext.buying.doctype.supplier_scorecard.supplier_scorecard.refresh_scorecards because it was found in queue for site1
2025-06-11 16:15:17,105 ERROR scheduler Skipped queueing erpnext.support.doctype.service_level_agreement.service_level_agreement.check_agreement_status because it was found in queue for site1
2025-06-11 16:15:17,108 ERROR scheduler Skipped queueing erpnext.projects.doctype.project.project.update_project_sales_billing because it was found in queue for site1
2025-06-11 16:15:17,109 ERROR scheduler Skipped queueing propms.auto_custom.statusChangeBeforeLeaseExpire because it was found in queue for site1
2025-06-11 16:15:17,110 ERROR scheduler Skipped queueing hrms.hr.doctype.daily_work_summary_group.daily_work_summary_group.send_summary because it was found in queue for site1
2025-06-11 16:15:17,112 ERROR scheduler Skipped queueing erpnext.stock.reorder_item.reorder_item because it was found in queue for site1
2025-06-11 16:15:17,114 ERROR scheduler Skipped queueing erpnext.assets.doctype.asset.depreciation.post_depreciation_entries because it was found in queue for site1
2025-06-11 16:15:17,115 ERROR scheduler Skipped queueing erpnext.quality_management.doctype.quality_review.quality_review.review because it was found in queue for site1
2025-06-11 16:15:17,116 ERROR scheduler Skipped queueing erpnext.accounts.doctype.process_subscription.process_subscription.create_subscription_process because it was found in queue for site1
2025-06-11 16:15:17,117 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for site1
2025-06-11 16:15:17,117 ERROR scheduler Skipped queueing erpnext.assets.doctype.asset_maintenance_log.asset_maintenance_log.update_asset_maintenance_log_status because it was found in queue for site1
2025-06-11 16:15:17,119 ERROR scheduler Skipped queueing propms.auto_custom.statusChangeAfterLeaseExpire because it was found in queue for site1
2025-06-11 16:15:17,120 ERROR scheduler Skipped queueing csf_tz.stanbic.sftp.sync_all_stanbank_files because it was found in queue for site1
2025-06-11 16:15:17,120 ERROR scheduler Skipped queueing frappe.utils.global_search.sync_global_search because it was found in queue for site1
2025-06-11 16:15:17,121 ERROR scheduler Skipped queueing erpnext.projects.doctype.project.project.collect_project_status because it was found in queue for site1
2025-06-11 16:15:17,122 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_drive.google_drive.daily_backup because it was found in queue for site1
2025-06-11 16:15:17,123 ERROR scheduler Skipped queueing hrms.hr.doctype.leave_ledger_entry.leave_ledger_entry.process_expired_allocation because it was found in queue for site1
2025-06-11 16:15:17,124 ERROR scheduler Skipped queueing frappe.integrations.doctype.dropbox_settings.dropbox_settings.take_backups_daily because it was found in queue for site1
2025-06-11 16:15:17,125 ERROR scheduler Skipped queueing erpnext.setup.doctype.email_digest.email_digest.send because it was found in queue for site1
2025-06-11 16:15:17,127 ERROR scheduler Skipped queueing erpnext.crm.doctype.email_campaign.email_campaign.set_email_campaign_status because it was found in queue for site1
2025-06-11 16:15:17,128 ERROR scheduler Skipped queueing erpnext.crm.utils.open_leads_opportunities_based_on_todays_event because it was found in queue for site1
2025-06-11 16:16:17,164 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for site1
2025-06-11 16:16:17,166 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for site1
2025-06-11 16:16:17,171 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for site1
2025-06-11 16:16:17,182 ERROR scheduler Skipped queueing erpnext.crm.utils.open_leads_opportunities_based_on_todays_event because it was found in queue for site1
2025-06-11 16:16:17,183 ERROR scheduler Skipped queueing erpnext.manufacturing.doctype.bom_update_tool.bom_update_tool.auto_update_latest_price_in_all_boms because it was found in queue for site1
2025-06-11 16:16:17,188 ERROR scheduler Skipped queueing frappe.email.doctype.auto_email_report.auto_email_report.send_daily because it was found in queue for site1
2025-06-11 16:16:17,191 ERROR scheduler Skipped queueing frappe.social.doctype.energy_point_settings.energy_point_settings.allocate_review_points because it was found in queue for site1
2025-06-11 16:16:17,195 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for site1
2025-06-11 16:16:17,197 ERROR scheduler Skipped queueing hrms.hr.utils.generate_leave_encashment because it was found in queue for site1
2025-06-11 16:16:17,198 ERROR scheduler Skipped queueing hrms.hr.doctype.leave_ledger_entry.leave_ledger_entry.process_expired_allocation because it was found in queue for site1
2025-06-11 16:16:17,200 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for site1
2025-06-11 16:16:17,202 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_drive.google_drive.daily_backup because it was found in queue for site1
2025-06-11 16:16:17,203 ERROR scheduler Skipped queueing frappe.integrations.doctype.dropbox_settings.dropbox_settings.take_backups_daily because it was found in queue for site1
2025-06-11 16:16:17,204 ERROR scheduler Skipped queueing frappe.core.doctype.log_settings.log_settings.run_log_clean_up because it was found in queue for site1
2025-06-11 16:16:17,205 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.process_auto_attendance_for_all_shifts because it was found in queue for site1
2025-06-11 16:16:17,210 ERROR scheduler Skipped queueing erpnext.accounts.doctype.process_subscription.process_subscription.create_subscription_process because it was found in queue for site1
2025-06-11 16:16:17,213 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.remove_unverified_record because it was found in queue for site1
2025-06-11 16:16:17,214 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for site1
2025-06-11 16:16:17,215 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for site1
2025-06-11 16:16:17,218 ERROR scheduler Skipped queueing hrms.hr.utils.allocate_earned_leaves because it was found in queue for site1
2025-06-11 16:16:17,220 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.update_last_sync_of_checkin because it was found in queue for site1
2025-06-11 16:16:17,221 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_schedule_assignment.shift_schedule_assignment.process_auto_shift_creation because it was found in queue for site1
2025-06-11 16:16:17,222 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for site1
2025-06-11 16:16:17,224 ERROR scheduler Skipped queueing frappe.automation.doctype.auto_repeat.auto_repeat.make_auto_repeat_entry because it was found in queue for site1
2025-06-11 16:16:17,226 ERROR scheduler Skipped queueing frappe.sessions.clear_expired_sessions because it was found in queue for site1
2025-06-11 16:16:17,230 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for site1
2025-06-11 16:16:17,231 ERROR scheduler Skipped queueing frappe.desk.notifications.clear_notifications because it was found in queue for site1
2025-06-11 16:16:17,232 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for site1
2025-06-11 16:16:17,237 ERROR scheduler Skipped queueing frappe.integrations.doctype.s3_backup_settings.s3_backup_settings.take_backups_daily because it was found in queue for site1
2025-06-11 16:16:17,242 ERROR scheduler Skipped queueing erpnext.setup.doctype.email_digest.email_digest.send because it was found in queue for site1
2025-06-11 16:16:17,246 ERROR scheduler Skipped queueing erpnext.assets.doctype.asset.depreciation.post_depreciation_entries because it was found in queue for site1
2025-06-11 16:16:17,248 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_contacts.google_contacts.sync because it was found in queue for site1
2025-06-11 16:17:17,409 ERROR scheduler Skipped queueing frappe.social.doctype.energy_point_settings.energy_point_settings.allocate_review_points because it was found in queue for site1
2025-06-11 16:17:17,429 ERROR scheduler Skipped queueing erpnext.setup.doctype.email_digest.email_digest.send because it was found in queue for site1
2025-06-11 16:17:17,431 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.update_last_sync_of_checkin because it was found in queue for site1
2025-06-11 16:17:17,438 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for site1
2025-06-11 16:17:17,444 ERROR scheduler Skipped queueing frappe.core.doctype.log_settings.log_settings.run_log_clean_up because it was found in queue for site1
2025-06-11 16:17:17,446 ERROR scheduler Skipped queueing erpnext.assets.doctype.asset.depreciation.post_depreciation_entries because it was found in queue for site1
2025-06-11 16:17:17,454 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_contacts.google_contacts.sync because it was found in queue for site1
2025-06-11 16:17:17,456 ERROR scheduler Skipped queueing erpnext.crm.utils.open_leads_opportunities_based_on_todays_event because it was found in queue for site1
2025-06-11 16:17:17,458 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for site1
2025-06-11 16:17:17,459 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for site1
2025-06-11 16:17:17,463 ERROR scheduler Skipped queueing frappe.email.doctype.auto_email_report.auto_email_report.send_daily because it was found in queue for site1
2025-06-11 16:17:17,465 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for site1
2025-06-11 16:17:17,466 ERROR scheduler Skipped queueing frappe.automation.doctype.auto_repeat.auto_repeat.make_auto_repeat_entry because it was found in queue for site1
2025-06-11 16:17:17,471 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for site1
2025-06-11 16:17:17,473 ERROR scheduler Skipped queueing frappe.integrations.doctype.s3_backup_settings.s3_backup_settings.take_backups_daily because it was found in queue for site1
2025-06-11 16:17:17,482 ERROR scheduler Skipped queueing frappe.desk.notifications.clear_notifications because it was found in queue for site1
2025-06-11 16:17:17,483 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for site1
2025-06-11 16:17:17,486 ERROR scheduler Skipped queueing erpnext.accounts.doctype.process_subscription.process_subscription.create_subscription_process because it was found in queue for site1
2025-06-11 16:17:17,488 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for site1
2025-06-11 16:17:17,491 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for site1
2025-06-11 16:17:17,493 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for site1
2025-06-11 16:17:17,497 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_schedule_assignment.shift_schedule_assignment.process_auto_shift_creation because it was found in queue for site1
2025-06-11 16:17:17,498 ERROR scheduler Skipped queueing hrms.hr.utils.allocate_earned_leaves because it was found in queue for site1
2025-06-11 16:17:17,499 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.process_auto_attendance_for_all_shifts because it was found in queue for site1
2025-06-11 16:17:17,501 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.remove_unverified_record because it was found in queue for site1
2025-06-11 16:17:17,504 ERROR scheduler Skipped queueing hrms.hr.utils.generate_leave_encashment because it was found in queue for site1
2025-06-11 16:17:17,506 ERROR scheduler Skipped queueing frappe.sessions.clear_expired_sessions because it was found in queue for site1
2025-06-11 16:17:17,508 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for site1
2025-06-11 16:17:17,509 ERROR scheduler Skipped queueing frappe.integrations.doctype.dropbox_settings.dropbox_settings.take_backups_daily because it was found in queue for site1
2025-06-11 16:17:17,510 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_drive.google_drive.daily_backup because it was found in queue for site1
2025-06-11 16:17:17,511 ERROR scheduler Skipped queueing hrms.hr.doctype.leave_ledger_entry.leave_ledger_entry.process_expired_allocation because it was found in queue for site1
2025-06-11 16:17:17,513 ERROR scheduler Skipped queueing erpnext.manufacturing.doctype.bom_update_tool.bom_update_tool.auto_update_latest_price_in_all_boms because it was found in queue for site1
2025-06-11 16:18:17,649 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for site1
2025-06-11 16:18:17,652 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_drive.google_drive.daily_backup because it was found in queue for site1
2025-06-11 16:18:17,660 ERROR scheduler Skipped queueing erpnext.manufacturing.doctype.bom_update_tool.bom_update_tool.auto_update_latest_price_in_all_boms because it was found in queue for site1
2025-06-11 16:18:17,663 ERROR scheduler Skipped queueing erpnext.accounts.doctype.process_subscription.process_subscription.create_subscription_process because it was found in queue for site1
2025-06-11 16:18:17,666 ERROR scheduler Skipped queueing frappe.social.doctype.energy_point_settings.energy_point_settings.allocate_review_points because it was found in queue for site1
2025-06-11 16:18:17,670 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.process_auto_attendance_for_all_shifts because it was found in queue for site1
2025-06-11 16:18:17,671 ERROR scheduler Skipped queueing hrms.hr.doctype.leave_ledger_entry.leave_ledger_entry.process_expired_allocation because it was found in queue for site1
2025-06-11 16:18:17,673 ERROR scheduler Skipped queueing hrms.hr.utils.allocate_earned_leaves because it was found in queue for site1
2025-06-11 16:18:17,674 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for site1
2025-06-11 16:18:17,675 ERROR scheduler Skipped queueing frappe.desk.notifications.clear_notifications because it was found in queue for site1
2025-06-11 16:18:17,676 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for site1
2025-06-11 16:18:17,679 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for site1
2025-06-11 16:18:17,681 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for site1
2025-06-11 16:18:17,686 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for site1
2025-06-11 16:18:17,688 ERROR scheduler Skipped queueing frappe.integrations.doctype.s3_backup_settings.s3_backup_settings.take_backups_daily because it was found in queue for site1
2025-06-11 16:18:17,689 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.update_last_sync_of_checkin because it was found in queue for site1
2025-06-11 16:18:17,691 ERROR scheduler Skipped queueing erpnext.setup.doctype.email_digest.email_digest.send because it was found in queue for site1
2025-06-11 16:18:17,693 ERROR scheduler Skipped queueing erpnext.assets.doctype.asset.depreciation.post_depreciation_entries because it was found in queue for site1
2025-06-11 16:18:17,694 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for site1
2025-06-11 16:18:17,697 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for site1
2025-06-11 16:18:17,699 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for site1
2025-06-11 16:18:17,703 ERROR scheduler Skipped queueing hrms.hr.utils.generate_leave_encashment because it was found in queue for site1
2025-06-11 16:18:17,707 ERROR scheduler Skipped queueing frappe.automation.doctype.auto_repeat.auto_repeat.make_auto_repeat_entry because it was found in queue for site1
2025-06-11 16:18:17,708 ERROR scheduler Skipped queueing frappe.integrations.doctype.dropbox_settings.dropbox_settings.take_backups_daily because it was found in queue for site1
2025-06-11 16:18:17,711 ERROR scheduler Skipped queueing frappe.sessions.clear_expired_sessions because it was found in queue for site1
2025-06-11 16:18:17,715 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_schedule_assignment.shift_schedule_assignment.process_auto_shift_creation because it was found in queue for site1
2025-06-11 16:18:17,720 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_contacts.google_contacts.sync because it was found in queue for site1
2025-06-11 16:18:17,726 ERROR scheduler Skipped queueing frappe.email.doctype.auto_email_report.auto_email_report.send_daily because it was found in queue for site1
2025-06-11 16:18:17,727 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.remove_unverified_record because it was found in queue for site1
2025-06-11 16:18:17,731 ERROR scheduler Skipped queueing erpnext.crm.utils.open_leads_opportunities_based_on_todays_event because it was found in queue for site1
2025-06-11 16:18:17,734 ERROR scheduler Skipped queueing frappe.core.doctype.log_settings.log_settings.run_log_clean_up because it was found in queue for site1
2025-06-11 16:18:17,736 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for site1
2025-06-11 16:19:17,899 ERROR scheduler Skipped queueing erpnext.manufacturing.doctype.bom_update_tool.bom_update_tool.auto_update_latest_price_in_all_boms because it was found in queue for site1
2025-06-11 16:19:17,908 ERROR scheduler Skipped queueing frappe.sessions.clear_expired_sessions because it was found in queue for site1
2025-06-11 16:19:17,915 ERROR scheduler Skipped queueing frappe.core.doctype.log_settings.log_settings.run_log_clean_up because it was found in queue for site1
2025-06-11 16:19:17,917 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_schedule_assignment.shift_schedule_assignment.process_auto_shift_creation because it was found in queue for site1
2025-06-11 16:19:17,923 ERROR scheduler Skipped queueing frappe.email.doctype.auto_email_report.auto_email_report.send_daily because it was found in queue for site1
2025-06-11 16:19:17,927 ERROR scheduler Skipped queueing erpnext.crm.utils.open_leads_opportunities_based_on_todays_event because it was found in queue for site1
2025-06-11 16:19:17,929 ERROR scheduler Skipped queueing erpnext.assets.doctype.asset.depreciation.post_depreciation_entries because it was found in queue for site1
2025-06-11 16:19:17,930 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.update_last_sync_of_checkin because it was found in queue for site1
2025-06-11 16:19:17,932 ERROR scheduler Skipped queueing hrms.hr.utils.allocate_earned_leaves because it was found in queue for site1
2025-06-11 16:19:17,934 ERROR scheduler Skipped queueing frappe.automation.doctype.auto_repeat.auto_repeat.make_auto_repeat_entry because it was found in queue for site1
2025-06-11 16:19:17,935 ERROR scheduler Skipped queueing hrms.hr.utils.generate_leave_encashment because it was found in queue for site1
2025-06-11 16:19:17,937 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.process_auto_attendance_for_all_shifts because it was found in queue for site1
2025-06-11 16:19:17,940 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for site1
2025-06-11 16:19:17,941 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for site1
2025-06-11 16:19:17,943 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for site1
2025-06-11 16:19:17,944 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_drive.google_drive.daily_backup because it was found in queue for site1
2025-06-11 16:19:17,949 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for site1
2025-06-11 16:19:17,951 ERROR scheduler Skipped queueing frappe.integrations.doctype.s3_backup_settings.s3_backup_settings.take_backups_daily because it was found in queue for site1
2025-06-11 16:19:17,954 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for site1
2025-06-11 16:19:17,956 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for site1
2025-06-11 16:19:17,960 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.remove_unverified_record because it was found in queue for site1
2025-06-11 16:19:17,962 ERROR scheduler Skipped queueing frappe.desk.notifications.clear_notifications because it was found in queue for site1
2025-06-11 16:19:17,970 ERROR scheduler Skipped queueing erpnext.setup.doctype.email_digest.email_digest.send because it was found in queue for site1
2025-06-11 16:19:17,971 ERROR scheduler Skipped queueing frappe.integrations.doctype.dropbox_settings.dropbox_settings.take_backups_daily because it was found in queue for site1
2025-06-11 16:19:17,973 ERROR scheduler Skipped queueing hrms.hr.doctype.leave_ledger_entry.leave_ledger_entry.process_expired_allocation because it was found in queue for site1
2025-06-11 16:19:17,974 ERROR scheduler Skipped queueing frappe.social.doctype.energy_point_settings.energy_point_settings.allocate_review_points because it was found in queue for site1
2025-06-11 16:19:17,975 ERROR scheduler Skipped queueing erpnext.accounts.doctype.process_subscription.process_subscription.create_subscription_process because it was found in queue for site1
2025-06-11 16:19:17,978 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_contacts.google_contacts.sync because it was found in queue for site1
2025-06-11 16:19:17,980 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for site1
2025-06-11 16:19:17,984 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for site1
2025-06-11 16:19:17,985 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for site1
2025-06-11 16:19:17,987 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for site1
2025-06-11 16:20:18,218 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for site1
2025-06-11 16:20:18,221 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for site1
2025-06-11 16:20:18,224 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_contacts.google_contacts.sync because it was found in queue for site1
2025-06-11 16:20:18,225 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for site1
2025-06-11 16:20:18,228 ERROR scheduler Skipped queueing frappe.integrations.doctype.s3_backup_settings.s3_backup_settings.take_backups_daily because it was found in queue for site1
2025-06-11 16:20:18,231 ERROR scheduler Skipped queueing erpnext.crm.utils.open_leads_opportunities_based_on_todays_event because it was found in queue for site1
2025-06-11 16:20:18,237 ERROR scheduler Skipped queueing frappe.core.doctype.log_settings.log_settings.run_log_clean_up because it was found in queue for site1
2025-06-11 16:20:18,247 ERROR scheduler Skipped queueing hrms.hr.doctype.leave_ledger_entry.leave_ledger_entry.process_expired_allocation because it was found in queue for site1
2025-06-11 16:20:18,248 ERROR scheduler Skipped queueing frappe.social.doctype.energy_point_settings.energy_point_settings.allocate_review_points because it was found in queue for site1
2025-06-11 16:20:18,255 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for site1
2025-06-11 16:20:18,256 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.process_auto_attendance_for_all_shifts because it was found in queue for site1
2025-06-11 16:20:18,258 ERROR scheduler Skipped queueing frappe.integrations.doctype.dropbox_settings.dropbox_settings.take_backups_daily because it was found in queue for site1
2025-06-11 16:20:18,262 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for site1
2025-06-11 16:20:18,263 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for site1
2025-06-11 16:20:18,264 ERROR scheduler Skipped queueing erpnext.setup.doctype.email_digest.email_digest.send because it was found in queue for site1
2025-06-11 16:20:18,266 ERROR scheduler Skipped queueing hrms.hr.utils.allocate_earned_leaves because it was found in queue for site1
2025-06-11 16:20:18,269 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for site1
2025-06-11 16:20:18,271 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for site1
2025-06-11 16:20:18,272 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_schedule_assignment.shift_schedule_assignment.process_auto_shift_creation because it was found in queue for site1
2025-06-11 16:20:18,276 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for site1
2025-06-11 16:20:18,277 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for site1
2025-06-11 16:20:18,279 ERROR scheduler Skipped queueing frappe.email.doctype.auto_email_report.auto_email_report.send_daily because it was found in queue for site1
2025-06-11 16:20:18,282 ERROR scheduler Skipped queueing erpnext.assets.doctype.asset.depreciation.post_depreciation_entries because it was found in queue for site1
2025-06-11 16:20:18,287 ERROR scheduler Skipped queueing hrms.hr.utils.generate_leave_encashment because it was found in queue for site1
2025-06-11 16:20:18,293 ERROR scheduler Skipped queueing frappe.automation.doctype.auto_repeat.auto_repeat.make_auto_repeat_entry because it was found in queue for site1
2025-06-11 16:20:18,299 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.update_last_sync_of_checkin because it was found in queue for site1
2025-06-11 16:20:18,304 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.remove_unverified_record because it was found in queue for site1
2025-06-11 16:20:18,306 ERROR scheduler Skipped queueing erpnext.accounts.doctype.process_subscription.process_subscription.create_subscription_process because it was found in queue for site1
2025-06-11 16:20:18,307 ERROR scheduler Skipped queueing erpnext.manufacturing.doctype.bom_update_tool.bom_update_tool.auto_update_latest_price_in_all_boms because it was found in queue for site1
2025-06-11 16:20:18,309 ERROR scheduler Skipped queueing frappe.sessions.clear_expired_sessions because it was found in queue for site1
2025-06-11 16:20:18,310 ERROR scheduler Skipped queueing frappe.desk.notifications.clear_notifications because it was found in queue for site1
2025-06-11 16:20:18,313 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_drive.google_drive.daily_backup because it was found in queue for site1
2025-06-11 16:21:18,326 ERROR scheduler Skipped queueing hrms.hr.utils.generate_leave_encashment because it was found in queue for site1
2025-06-11 16:21:18,328 ERROR scheduler Skipped queueing hrms.hr.doctype.leave_ledger_entry.leave_ledger_entry.process_expired_allocation because it was found in queue for site1
2025-06-11 16:21:18,330 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_contacts.google_contacts.sync because it was found in queue for site1
2025-06-11 16:21:18,332 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for site1
2025-06-11 16:21:18,334 ERROR scheduler Skipped queueing erpnext.setup.doctype.email_digest.email_digest.send because it was found in queue for site1
2025-06-11 16:21:18,335 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for site1
2025-06-11 16:21:18,339 ERROR scheduler Skipped queueing frappe.integrations.doctype.dropbox_settings.dropbox_settings.take_backups_daily because it was found in queue for site1
2025-06-11 16:21:18,342 ERROR scheduler Skipped queueing erpnext.crm.utils.open_leads_opportunities_based_on_todays_event because it was found in queue for site1
2025-06-11 16:21:18,344 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.update_last_sync_of_checkin because it was found in queue for site1
2025-06-11 16:21:18,345 ERROR scheduler Skipped queueing frappe.integrations.doctype.s3_backup_settings.s3_backup_settings.take_backups_daily because it was found in queue for site1
2025-06-11 16:21:18,352 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for site1
2025-06-11 16:21:18,354 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.pull because it was found in queue for site1
2025-06-11 16:21:18,355 ERROR scheduler Skipped queueing frappe.automation.doctype.auto_repeat.auto_repeat.make_auto_repeat_entry because it was found in queue for site1
2025-06-11 16:21:18,357 ERROR scheduler Skipped queueing frappe.social.doctype.energy_point_settings.energy_point_settings.allocate_review_points because it was found in queue for site1
2025-06-11 16:21:18,361 ERROR scheduler Skipped queueing hrms.hr.utils.allocate_earned_leaves because it was found in queue for site1
2025-06-11 16:21:18,364 ERROR scheduler Skipped queueing erpnext.accounts.doctype.process_subscription.process_subscription.create_subscription_process because it was found in queue for site1
2025-06-11 16:21:18,367 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for site1
2025-06-11 16:21:18,370 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.remove_unverified_record because it was found in queue for site1
2025-06-11 16:21:18,375 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for site1
2025-06-11 16:21:18,376 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for site1
2025-06-11 16:21:18,377 ERROR scheduler Skipped queueing frappe.email.queue.flush because it was found in queue for site1
2025-06-11 16:21:18,380 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for site1
2025-06-11 16:21:18,381 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.process_auto_attendance_for_all_shifts because it was found in queue for site1
2025-06-11 16:21:18,384 ERROR scheduler Skipped queueing erpnext.assets.doctype.asset.depreciation.post_depreciation_entries because it was found in queue for site1
2025-06-11 16:21:18,387 ERROR scheduler Skipped queueing frappe.monitor.flush because it was found in queue for site1
2025-06-11 16:21:18,388 ERROR scheduler Skipped queueing frappe.sessions.clear_expired_sessions because it was found in queue for site1
2025-06-11 16:21:18,390 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for site1
2025-06-11 16:21:18,391 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_schedule_assignment.shift_schedule_assignment.process_auto_shift_creation because it was found in queue for site1
2025-06-11 16:21:18,392 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for site1
2025-06-11 16:21:18,398 ERROR scheduler Skipped queueing frappe.core.doctype.log_settings.log_settings.run_log_clean_up because it was found in queue for site1
2025-06-11 16:21:18,405 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for site1
2025-06-11 16:21:18,410 ERROR scheduler Skipped queueing hrms.hr.doctype.interview.interview.send_interview_reminder because it was found in queue for site1
2025-06-11 16:21:18,411 ERROR scheduler Skipped queueing frappe.desk.notifications.clear_notifications because it was found in queue for site1
2025-06-11 16:21:18,412 ERROR scheduler Skipped queueing erpnext.manufacturing.doctype.bom_update_tool.bom_update_tool.auto_update_latest_price_in_all_boms because it was found in queue for site1
2025-06-11 16:21:18,414 ERROR scheduler Skipped queueing frappe.email.doctype.auto_email_report.auto_email_report.send_daily because it was found in queue for site1
2025-06-11 16:21:18,418 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_calendar.google_calendar.sync because it was found in queue for site1
2025-06-11 16:21:18,420 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_drive.google_drive.daily_backup because it was found in queue for site1
2025-06-11 16:22:18,634 ERROR scheduler Skipped queueing erpnext.crm.utils.open_leads_opportunities_based_on_todays_event because it was found in queue for site1
2025-06-11 16:22:18,639 ERROR scheduler Skipped queueing frappe.automation.doctype.auto_repeat.auto_repeat.make_auto_repeat_entry because it was found in queue for site1
2025-06-11 16:22:18,647 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.remove_unverified_record because it was found in queue for site1
2025-06-11 16:22:18,652 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.update_last_sync_of_checkin because it was found in queue for site1
2025-06-11 16:22:18,654 ERROR scheduler Skipped queueing hrms.hr.utils.allocate_earned_leaves because it was found in queue for site1
2025-06-11 16:22:18,656 ERROR scheduler Skipped queueing frappe.sessions.clear_expired_sessions because it was found in queue for site1
2025-06-11 16:22:18,660 ERROR scheduler Skipped queueing erpnext.accounts.doctype.process_subscription.process_subscription.create_subscription_process because it was found in queue for site1
2025-06-11 16:22:18,661 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for site1
2025-06-11 16:22:18,662 ERROR scheduler Skipped queueing frappe.core.doctype.log_settings.log_settings.run_log_clean_up because it was found in queue for site1
2025-06-11 16:22:18,663 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.process_auto_attendance_for_all_shifts because it was found in queue for site1
2025-06-11 16:22:18,664 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for site1
2025-06-11 16:22:18,667 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for site1
2025-06-11 16:22:18,671 ERROR scheduler Skipped queueing erpnext.assets.doctype.asset.depreciation.post_depreciation_entries because it was found in queue for site1
2025-06-11 16:22:18,672 ERROR scheduler Skipped queueing erpnext.manufacturing.doctype.bom_update_tool.bom_update_tool.auto_update_latest_price_in_all_boms because it was found in queue for site1
2025-06-11 16:22:18,675 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_contacts.google_contacts.sync because it was found in queue for site1
2025-06-11 16:22:18,680 ERROR scheduler Skipped queueing frappe.email.doctype.auto_email_report.auto_email_report.send_daily because it was found in queue for site1
2025-06-11 16:22:18,685 ERROR scheduler Skipped queueing frappe.integrations.doctype.s3_backup_settings.s3_backup_settings.take_backups_daily because it was found in queue for site1
2025-06-11 16:22:18,690 ERROR scheduler Skipped queueing erpnext.setup.doctype.email_digest.email_digest.send because it was found in queue for site1
2025-06-11 16:22:18,691 ERROR scheduler Skipped queueing frappe.social.doctype.energy_point_settings.energy_point_settings.allocate_review_points because it was found in queue for site1
2025-06-11 16:22:18,694 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for site1
2025-06-11 16:22:18,695 ERROR scheduler Skipped queueing hrms.hr.doctype.leave_ledger_entry.leave_ledger_entry.process_expired_allocation because it was found in queue for site1
2025-06-11 16:22:18,697 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for site1
2025-06-11 16:22:18,698 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for site1
2025-06-11 16:22:18,699 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for site1
2025-06-11 16:22:18,702 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for site1
2025-06-11 16:22:18,705 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_schedule_assignment.shift_schedule_assignment.process_auto_shift_creation because it was found in queue for site1
2025-06-11 16:22:18,706 ERROR scheduler Skipped queueing frappe.desk.notifications.clear_notifications because it was found in queue for site1
2025-06-11 16:22:18,707 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_drive.google_drive.daily_backup because it was found in queue for site1
2025-06-11 16:22:18,708 ERROR scheduler Skipped queueing frappe.integrations.doctype.dropbox_settings.dropbox_settings.take_backups_daily because it was found in queue for site1
2025-06-11 16:22:18,709 ERROR scheduler Skipped queueing hrms.hr.utils.generate_leave_encashment because it was found in queue for site1
2025-06-11 16:22:18,715 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for site1
2025-06-11 16:22:18,716 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for site1
2025-06-11 16:23:18,731 ERROR scheduler Skipped queueing frappe.social.doctype.energy_point_settings.energy_point_settings.allocate_review_points because it was found in queue for site1
2025-06-11 16:23:18,735 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.remove_unverified_record because it was found in queue for site1
2025-06-11 16:23:18,738 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for site1
2025-06-11 16:23:18,746 ERROR scheduler Skipped queueing hrms.hr.doctype.leave_ledger_entry.leave_ledger_entry.process_expired_allocation because it was found in queue for site1
2025-06-11 16:23:18,748 ERROR scheduler Skipped queueing erpnext.accounts.doctype.process_subscription.process_subscription.create_subscription_process because it was found in queue for site1
2025-06-11 16:23:18,750 ERROR scheduler Skipped queueing frappe.integrations.doctype.s3_backup_settings.s3_backup_settings.take_backups_daily because it was found in queue for site1
2025-06-11 16:23:18,753 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for site1
2025-06-11 16:23:18,754 ERROR scheduler Skipped queueing erpnext.assets.doctype.asset.depreciation.post_depreciation_entries because it was found in queue for site1
2025-06-11 16:23:18,759 ERROR scheduler Skipped queueing frappe.email.doctype.auto_email_report.auto_email_report.send_daily because it was found in queue for site1
2025-06-11 16:23:18,761 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for site1
2025-06-11 16:23:18,762 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for site1
2025-06-11 16:23:18,763 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for site1
2025-06-11 16:23:18,764 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for site1
2025-06-11 16:23:18,768 ERROR scheduler Skipped queueing frappe.core.doctype.log_settings.log_settings.run_log_clean_up because it was found in queue for site1
2025-06-11 16:23:18,769 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for site1
2025-06-11 16:23:18,772 ERROR scheduler Skipped queueing hrms.hr.utils.generate_leave_encashment because it was found in queue for site1
2025-06-11 16:23:18,773 ERROR scheduler Skipped queueing frappe.desk.notifications.clear_notifications because it was found in queue for site1
2025-06-11 16:23:18,778 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for site1
2025-06-11 16:23:18,779 ERROR scheduler Skipped queueing frappe.automation.doctype.auto_repeat.auto_repeat.make_auto_repeat_entry because it was found in queue for site1
2025-06-11 16:23:18,785 ERROR scheduler Skipped queueing erpnext.manufacturing.doctype.bom_update_tool.bom_update_tool.auto_update_latest_price_in_all_boms because it was found in queue for site1
2025-06-11 16:23:18,787 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for site1
2025-06-11 16:23:18,789 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.process_auto_attendance_for_all_shifts because it was found in queue for site1
2025-06-11 16:23:18,790 ERROR scheduler Skipped queueing frappe.integrations.doctype.dropbox_settings.dropbox_settings.take_backups_daily because it was found in queue for site1
2025-06-11 16:23:18,791 ERROR scheduler Skipped queueing hrms.hr.utils.allocate_earned_leaves because it was found in queue for site1
2025-06-11 16:23:18,792 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.update_last_sync_of_checkin because it was found in queue for site1
2025-06-11 16:23:18,793 ERROR scheduler Skipped queueing frappe.sessions.clear_expired_sessions because it was found in queue for site1
2025-06-11 16:23:18,795 ERROR scheduler Skipped queueing erpnext.crm.utils.open_leads_opportunities_based_on_todays_event because it was found in queue for site1
2025-06-11 16:23:18,796 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_contacts.google_contacts.sync because it was found in queue for site1
2025-06-11 16:23:18,797 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_schedule_assignment.shift_schedule_assignment.process_auto_shift_creation because it was found in queue for site1
2025-06-11 16:23:18,798 ERROR scheduler Skipped queueing erpnext.setup.doctype.email_digest.email_digest.send because it was found in queue for site1
2025-06-11 16:23:18,803 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for site1
2025-06-11 16:23:18,807 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_drive.google_drive.daily_backup because it was found in queue for site1
2025-06-11 17:01:25,485 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for site1
2025-06-11 17:01:25,507 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for site1
2025-06-11 17:01:25,511 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for site1
2025-06-11 17:01:25,522 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for site1
2025-06-11 17:01:25,527 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_schedule_assignment.shift_schedule_assignment.process_auto_shift_creation because it was found in queue for site1
2025-06-11 17:01:25,528 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.update_last_sync_of_checkin because it was found in queue for site1
