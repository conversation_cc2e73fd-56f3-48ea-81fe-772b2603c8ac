2025-06-20 17:08:26,779 ERROR scheduler Exception in Enqueue Events for Site viva
Traceback (most recent call last):
  File "/home/<USER>/dev/frappe-bench/apps/frappe/frappe/utils/scheduler.py", line 99, in enqueue_events_for_site
    if is_scheduler_inactive():
       ^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/frappe-bench/apps/frappe/frappe/utils/scheduler.py", line 143, in is_scheduler_inactive
    if is_scheduler_disabled(verbose=verbose):
       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/frappe-bench/apps/frappe/frappe/utils/scheduler.py", line 156, in is_scheduler_disabled
    frappe.db.get_single_value("System Settings", "enable_scheduler")
  File "/home/<USER>/dev/frappe-bench/apps/frappe/frappe/database/database.py", line 837, in get_single_value
    ).run()
      ^^^^^
  File "/home/<USER>/dev/frappe-bench/apps/frappe/frappe/query_builder/utils.py", line 87, in execute_query
    result = frappe.db.sql(query, params, *args, **kwargs)  # nosemgrep
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/frappe-bench/apps/frappe/frappe/database/database.py", line 230, in sql
    self._cursor.execute(query, values)
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/pymysql/cursors.py", line 153, in execute
    result = self._query(query)
             ^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/pymysql/cursors.py", line 322, in _query
    conn.query(q)
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/pymysql/connections.py", line 563, in query
    self._affected_rows = self._read_query_result(unbuffered=unbuffered)
                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/pymysql/connections.py", line 825, in _read_query_result
    result.read()
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/pymysql/connections.py", line 1199, in read
    first_packet = self.connection._read_packet()
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/pymysql/connections.py", line 775, in _read_packet
    packet.raise_for_error()
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/pymysql/protocol.py", line 219, in raise_for_error
    err.raise_mysql_exception(self._data)
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/pymysql/err.py", line 150, in raise_mysql_exception
    raise errorclass(errno, errval)
pymysql.err.ProgrammingError: (1146, "Table '_9c5dc86bed914016.tabSingles' doesn't exist")
2025-06-20 17:09:27,240 ERROR scheduler Exception in Enqueue Events for Site viva
Traceback (most recent call last):
  File "/home/<USER>/dev/frappe-bench/apps/frappe/frappe/utils/scheduler.py", line 99, in enqueue_events_for_site
    if is_scheduler_inactive():
       ^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/frappe-bench/apps/frappe/frappe/utils/scheduler.py", line 143, in is_scheduler_inactive
    if is_scheduler_disabled(verbose=verbose):
       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/frappe-bench/apps/frappe/frappe/utils/scheduler.py", line 156, in is_scheduler_disabled
    frappe.db.get_single_value("System Settings", "enable_scheduler")
  File "/home/<USER>/dev/frappe-bench/apps/frappe/frappe/database/database.py", line 837, in get_single_value
    ).run()
      ^^^^^
  File "/home/<USER>/dev/frappe-bench/apps/frappe/frappe/query_builder/utils.py", line 87, in execute_query
    result = frappe.db.sql(query, params, *args, **kwargs)  # nosemgrep
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/frappe-bench/apps/frappe/frappe/database/database.py", line 230, in sql
    self._cursor.execute(query, values)
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/pymysql/cursors.py", line 153, in execute
    result = self._query(query)
             ^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/pymysql/cursors.py", line 322, in _query
    conn.query(q)
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/pymysql/connections.py", line 563, in query
    self._affected_rows = self._read_query_result(unbuffered=unbuffered)
                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/pymysql/connections.py", line 825, in _read_query_result
    result.read()
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/pymysql/connections.py", line 1199, in read
    first_packet = self.connection._read_packet()
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/pymysql/connections.py", line 775, in _read_packet
    packet.raise_for_error()
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/pymysql/protocol.py", line 219, in raise_for_error
    err.raise_mysql_exception(self._data)
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/pymysql/err.py", line 150, in raise_mysql_exception
    raise errorclass(errno, errval)
pymysql.err.ProgrammingError: (1146, "Table '_9c5dc86bed914016.tabSingles' doesn't exist")
2025-06-20 17:10:27,600 ERROR scheduler Exception in Enqueue Events for Site viva
Traceback (most recent call last):
  File "/home/<USER>/dev/frappe-bench/apps/frappe/frappe/utils/scheduler.py", line 99, in enqueue_events_for_site
    if is_scheduler_inactive():
       ^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/frappe-bench/apps/frappe/frappe/utils/scheduler.py", line 143, in is_scheduler_inactive
    if is_scheduler_disabled(verbose=verbose):
       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/frappe-bench/apps/frappe/frappe/utils/scheduler.py", line 156, in is_scheduler_disabled
    frappe.db.get_single_value("System Settings", "enable_scheduler")
  File "/home/<USER>/dev/frappe-bench/apps/frappe/frappe/database/database.py", line 837, in get_single_value
    ).run()
      ^^^^^
  File "/home/<USER>/dev/frappe-bench/apps/frappe/frappe/query_builder/utils.py", line 87, in execute_query
    result = frappe.db.sql(query, params, *args, **kwargs)  # nosemgrep
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/frappe-bench/apps/frappe/frappe/database/database.py", line 230, in sql
    self._cursor.execute(query, values)
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/pymysql/cursors.py", line 153, in execute
    result = self._query(query)
             ^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/pymysql/cursors.py", line 322, in _query
    conn.query(q)
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/pymysql/connections.py", line 563, in query
    self._affected_rows = self._read_query_result(unbuffered=unbuffered)
                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/pymysql/connections.py", line 825, in _read_query_result
    result.read()
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/pymysql/connections.py", line 1199, in read
    first_packet = self.connection._read_packet()
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/pymysql/connections.py", line 775, in _read_packet
    packet.raise_for_error()
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/pymysql/protocol.py", line 219, in raise_for_error
    err.raise_mysql_exception(self._data)
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/pymysql/err.py", line 150, in raise_mysql_exception
    raise errorclass(errno, errval)
pymysql.err.ProgrammingError: (1146, "Table '_9c5dc86bed914016.tabSingles' doesn't exist")
2025-06-20 17:11:27,606 ERROR scheduler Exception in Enqueue Events for Site viva
Traceback (most recent call last):
  File "/home/<USER>/dev/frappe-bench/apps/frappe/frappe/utils/scheduler.py", line 99, in enqueue_events_for_site
    if is_scheduler_inactive():
       ^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/frappe-bench/apps/frappe/frappe/utils/scheduler.py", line 143, in is_scheduler_inactive
    if is_scheduler_disabled(verbose=verbose):
       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/frappe-bench/apps/frappe/frappe/utils/scheduler.py", line 156, in is_scheduler_disabled
    frappe.db.get_single_value("System Settings", "enable_scheduler")
  File "/home/<USER>/dev/frappe-bench/apps/frappe/frappe/database/database.py", line 837, in get_single_value
    ).run()
      ^^^^^
  File "/home/<USER>/dev/frappe-bench/apps/frappe/frappe/query_builder/utils.py", line 87, in execute_query
    result = frappe.db.sql(query, params, *args, **kwargs)  # nosemgrep
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/frappe-bench/apps/frappe/frappe/database/database.py", line 230, in sql
    self._cursor.execute(query, values)
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/pymysql/cursors.py", line 153, in execute
    result = self._query(query)
             ^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/pymysql/cursors.py", line 322, in _query
    conn.query(q)
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/pymysql/connections.py", line 563, in query
    self._affected_rows = self._read_query_result(unbuffered=unbuffered)
                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/pymysql/connections.py", line 825, in _read_query_result
    result.read()
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/pymysql/connections.py", line 1199, in read
    first_packet = self.connection._read_packet()
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/pymysql/connections.py", line 775, in _read_packet
    packet.raise_for_error()
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/pymysql/protocol.py", line 219, in raise_for_error
    err.raise_mysql_exception(self._data)
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/pymysql/err.py", line 150, in raise_mysql_exception
    raise errorclass(errno, errval)
pymysql.err.ProgrammingError: (1146, "Table '_9c5dc86bed914016.tabSingles' doesn't exist")
2025-06-20 17:12:28,344 ERROR scheduler Exception in Enqueue Events for Site viva
Traceback (most recent call last):
  File "/home/<USER>/dev/frappe-bench/apps/frappe/frappe/utils/scheduler.py", line 99, in enqueue_events_for_site
    if is_scheduler_inactive():
       ^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/frappe-bench/apps/frappe/frappe/utils/scheduler.py", line 143, in is_scheduler_inactive
    if is_scheduler_disabled(verbose=verbose):
       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/frappe-bench/apps/frappe/frappe/utils/scheduler.py", line 156, in is_scheduler_disabled
    frappe.db.get_single_value("System Settings", "enable_scheduler")
  File "/home/<USER>/dev/frappe-bench/apps/frappe/frappe/database/database.py", line 837, in get_single_value
    ).run()
      ^^^^^
  File "/home/<USER>/dev/frappe-bench/apps/frappe/frappe/query_builder/utils.py", line 87, in execute_query
    result = frappe.db.sql(query, params, *args, **kwargs)  # nosemgrep
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/frappe-bench/apps/frappe/frappe/database/database.py", line 230, in sql
    self._cursor.execute(query, values)
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/pymysql/cursors.py", line 153, in execute
    result = self._query(query)
             ^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/pymysql/cursors.py", line 322, in _query
    conn.query(q)
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/pymysql/connections.py", line 563, in query
    self._affected_rows = self._read_query_result(unbuffered=unbuffered)
                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/pymysql/connections.py", line 825, in _read_query_result
    result.read()
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/pymysql/connections.py", line 1199, in read
    first_packet = self.connection._read_packet()
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/pymysql/connections.py", line 775, in _read_packet
    packet.raise_for_error()
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/pymysql/protocol.py", line 219, in raise_for_error
    err.raise_mysql_exception(self._data)
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/pymysql/err.py", line 150, in raise_mysql_exception
    raise errorclass(errno, errval)
pymysql.err.ProgrammingError: (1146, "Table '_9c5dc86bed914016.tabSingles' doesn't exist")
2025-06-20 17:13:28,350 ERROR scheduler Exception in Enqueue Events for Site viva
Traceback (most recent call last):
  File "/home/<USER>/dev/frappe-bench/apps/frappe/frappe/utils/scheduler.py", line 99, in enqueue_events_for_site
    if is_scheduler_inactive():
       ^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/frappe-bench/apps/frappe/frappe/utils/scheduler.py", line 143, in is_scheduler_inactive
    if is_scheduler_disabled(verbose=verbose):
       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/frappe-bench/apps/frappe/frappe/utils/scheduler.py", line 156, in is_scheduler_disabled
    frappe.db.get_single_value("System Settings", "enable_scheduler")
  File "/home/<USER>/dev/frappe-bench/apps/frappe/frappe/database/database.py", line 837, in get_single_value
    ).run()
      ^^^^^
  File "/home/<USER>/dev/frappe-bench/apps/frappe/frappe/query_builder/utils.py", line 87, in execute_query
    result = frappe.db.sql(query, params, *args, **kwargs)  # nosemgrep
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/frappe-bench/apps/frappe/frappe/database/database.py", line 230, in sql
    self._cursor.execute(query, values)
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/pymysql/cursors.py", line 153, in execute
    result = self._query(query)
             ^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/pymysql/cursors.py", line 322, in _query
    conn.query(q)
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/pymysql/connections.py", line 563, in query
    self._affected_rows = self._read_query_result(unbuffered=unbuffered)
                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/pymysql/connections.py", line 825, in _read_query_result
    result.read()
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/pymysql/connections.py", line 1199, in read
    first_packet = self.connection._read_packet()
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/pymysql/connections.py", line 775, in _read_packet
    packet.raise_for_error()
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/pymysql/protocol.py", line 219, in raise_for_error
    err.raise_mysql_exception(self._data)
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/pymysql/err.py", line 150, in raise_mysql_exception
    raise errorclass(errno, errval)
pymysql.err.ProgrammingError: (1146, "Table '_9c5dc86bed914016.tabSingles' doesn't exist")
2025-06-20 17:14:28,705 ERROR scheduler Exception in Enqueue Events for Site viva
Traceback (most recent call last):
  File "/home/<USER>/dev/frappe-bench/apps/frappe/frappe/utils/scheduler.py", line 99, in enqueue_events_for_site
    if is_scheduler_inactive():
       ^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/frappe-bench/apps/frappe/frappe/utils/scheduler.py", line 143, in is_scheduler_inactive
    if is_scheduler_disabled(verbose=verbose):
       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/frappe-bench/apps/frappe/frappe/utils/scheduler.py", line 156, in is_scheduler_disabled
    frappe.db.get_single_value("System Settings", "enable_scheduler")
  File "/home/<USER>/dev/frappe-bench/apps/frappe/frappe/database/database.py", line 837, in get_single_value
    ).run()
      ^^^^^
  File "/home/<USER>/dev/frappe-bench/apps/frappe/frappe/query_builder/utils.py", line 87, in execute_query
    result = frappe.db.sql(query, params, *args, **kwargs)  # nosemgrep
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/frappe-bench/apps/frappe/frappe/database/database.py", line 230, in sql
    self._cursor.execute(query, values)
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/pymysql/cursors.py", line 153, in execute
    result = self._query(query)
             ^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/pymysql/cursors.py", line 322, in _query
    conn.query(q)
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/pymysql/connections.py", line 563, in query
    self._affected_rows = self._read_query_result(unbuffered=unbuffered)
                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/pymysql/connections.py", line 825, in _read_query_result
    result.read()
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/pymysql/connections.py", line 1199, in read
    first_packet = self.connection._read_packet()
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/pymysql/connections.py", line 775, in _read_packet
    packet.raise_for_error()
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/pymysql/protocol.py", line 219, in raise_for_error
    err.raise_mysql_exception(self._data)
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/pymysql/err.py", line 150, in raise_mysql_exception
    raise errorclass(errno, errval)
pymysql.err.ProgrammingError: (1146, "Table '_9c5dc86bed914016.tabSingles' doesn't exist")
2025-06-20 17:15:29,458 ERROR scheduler Exception in Enqueue Events for Site viva
Traceback (most recent call last):
  File "/home/<USER>/dev/frappe-bench/apps/frappe/frappe/utils/scheduler.py", line 99, in enqueue_events_for_site
    if is_scheduler_inactive():
       ^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/frappe-bench/apps/frappe/frappe/utils/scheduler.py", line 143, in is_scheduler_inactive
    if is_scheduler_disabled(verbose=verbose):
       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/frappe-bench/apps/frappe/frappe/utils/scheduler.py", line 156, in is_scheduler_disabled
    frappe.db.get_single_value("System Settings", "enable_scheduler")
  File "/home/<USER>/dev/frappe-bench/apps/frappe/frappe/database/database.py", line 837, in get_single_value
    ).run()
      ^^^^^
  File "/home/<USER>/dev/frappe-bench/apps/frappe/frappe/query_builder/utils.py", line 87, in execute_query
    result = frappe.db.sql(query, params, *args, **kwargs)  # nosemgrep
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/frappe-bench/apps/frappe/frappe/database/database.py", line 230, in sql
    self._cursor.execute(query, values)
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/pymysql/cursors.py", line 153, in execute
    result = self._query(query)
             ^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/pymysql/cursors.py", line 322, in _query
    conn.query(q)
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/pymysql/connections.py", line 563, in query
    self._affected_rows = self._read_query_result(unbuffered=unbuffered)
                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/pymysql/connections.py", line 825, in _read_query_result
    result.read()
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/pymysql/connections.py", line 1199, in read
    first_packet = self.connection._read_packet()
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/pymysql/connections.py", line 775, in _read_packet
    packet.raise_for_error()
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/pymysql/protocol.py", line 219, in raise_for_error
    err.raise_mysql_exception(self._data)
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/pymysql/err.py", line 150, in raise_mysql_exception
    raise errorclass(errno, errval)
pymysql.err.ProgrammingError: (1146, "Table '_9c5dc86bed914016.tabSingles' doesn't exist")
2025-06-20 17:16:29,461 ERROR scheduler Exception in Enqueue Events for Site viva
Traceback (most recent call last):
  File "/home/<USER>/dev/frappe-bench/apps/frappe/frappe/utils/scheduler.py", line 99, in enqueue_events_for_site
    if is_scheduler_inactive():
       ^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/frappe-bench/apps/frappe/frappe/utils/scheduler.py", line 143, in is_scheduler_inactive
    if is_scheduler_disabled(verbose=verbose):
       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/frappe-bench/apps/frappe/frappe/utils/scheduler.py", line 156, in is_scheduler_disabled
    frappe.db.get_single_value("System Settings", "enable_scheduler")
  File "/home/<USER>/dev/frappe-bench/apps/frappe/frappe/database/database.py", line 837, in get_single_value
    ).run()
      ^^^^^
  File "/home/<USER>/dev/frappe-bench/apps/frappe/frappe/query_builder/utils.py", line 87, in execute_query
    result = frappe.db.sql(query, params, *args, **kwargs)  # nosemgrep
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/frappe-bench/apps/frappe/frappe/database/database.py", line 230, in sql
    self._cursor.execute(query, values)
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/pymysql/cursors.py", line 153, in execute
    result = self._query(query)
             ^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/pymysql/cursors.py", line 322, in _query
    conn.query(q)
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/pymysql/connections.py", line 563, in query
    self._affected_rows = self._read_query_result(unbuffered=unbuffered)
                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/pymysql/connections.py", line 825, in _read_query_result
    result.read()
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/pymysql/connections.py", line 1199, in read
    first_packet = self.connection._read_packet()
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/pymysql/connections.py", line 775, in _read_packet
    packet.raise_for_error()
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/pymysql/protocol.py", line 219, in raise_for_error
    err.raise_mysql_exception(self._data)
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/pymysql/err.py", line 150, in raise_mysql_exception
    raise errorclass(errno, errval)
pymysql.err.ProgrammingError: (1146, "Table '_9c5dc86bed914016.tabSingles' doesn't exist")
2025-06-20 17:17:29,983 ERROR scheduler Exception in Enqueue Events for Site viva
Traceback (most recent call last):
  File "/home/<USER>/dev/frappe-bench/apps/frappe/frappe/utils/scheduler.py", line 99, in enqueue_events_for_site
    if is_scheduler_inactive():
       ^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/frappe-bench/apps/frappe/frappe/utils/scheduler.py", line 143, in is_scheduler_inactive
    if is_scheduler_disabled(verbose=verbose):
       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/frappe-bench/apps/frappe/frappe/utils/scheduler.py", line 156, in is_scheduler_disabled
    frappe.db.get_single_value("System Settings", "enable_scheduler")
  File "/home/<USER>/dev/frappe-bench/apps/frappe/frappe/database/database.py", line 837, in get_single_value
    ).run()
      ^^^^^
  File "/home/<USER>/dev/frappe-bench/apps/frappe/frappe/query_builder/utils.py", line 87, in execute_query
    result = frappe.db.sql(query, params, *args, **kwargs)  # nosemgrep
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/frappe-bench/apps/frappe/frappe/database/database.py", line 230, in sql
    self._cursor.execute(query, values)
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/pymysql/cursors.py", line 153, in execute
    result = self._query(query)
             ^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/pymysql/cursors.py", line 322, in _query
    conn.query(q)
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/pymysql/connections.py", line 563, in query
    self._affected_rows = self._read_query_result(unbuffered=unbuffered)
                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/pymysql/connections.py", line 825, in _read_query_result
    result.read()
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/pymysql/connections.py", line 1199, in read
    first_packet = self.connection._read_packet()
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/pymysql/connections.py", line 775, in _read_packet
    packet.raise_for_error()
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/pymysql/protocol.py", line 219, in raise_for_error
    err.raise_mysql_exception(self._data)
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/pymysql/err.py", line 150, in raise_mysql_exception
    raise errorclass(errno, errval)
pymysql.err.ProgrammingError: (1146, "Table '_9c5dc86bed914016.tabSingles' doesn't exist")
2025-06-20 17:18:30,188 ERROR scheduler Exception in Enqueue Events for Site viva
Traceback (most recent call last):
  File "/home/<USER>/dev/frappe-bench/apps/frappe/frappe/utils/scheduler.py", line 99, in enqueue_events_for_site
    if is_scheduler_inactive():
       ^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/frappe-bench/apps/frappe/frappe/utils/scheduler.py", line 143, in is_scheduler_inactive
    if is_scheduler_disabled(verbose=verbose):
       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/frappe-bench/apps/frappe/frappe/utils/scheduler.py", line 156, in is_scheduler_disabled
    frappe.db.get_single_value("System Settings", "enable_scheduler")
  File "/home/<USER>/dev/frappe-bench/apps/frappe/frappe/database/database.py", line 837, in get_single_value
    ).run()
      ^^^^^
  File "/home/<USER>/dev/frappe-bench/apps/frappe/frappe/query_builder/utils.py", line 87, in execute_query
    result = frappe.db.sql(query, params, *args, **kwargs)  # nosemgrep
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/frappe-bench/apps/frappe/frappe/database/database.py", line 230, in sql
    self._cursor.execute(query, values)
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/pymysql/cursors.py", line 153, in execute
    result = self._query(query)
             ^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/pymysql/cursors.py", line 322, in _query
    conn.query(q)
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/pymysql/connections.py", line 563, in query
    self._affected_rows = self._read_query_result(unbuffered=unbuffered)
                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/pymysql/connections.py", line 825, in _read_query_result
    result.read()
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/pymysql/connections.py", line 1199, in read
    first_packet = self.connection._read_packet()
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/pymysql/connections.py", line 775, in _read_packet
    packet.raise_for_error()
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/pymysql/protocol.py", line 219, in raise_for_error
    err.raise_mysql_exception(self._data)
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/pymysql/err.py", line 150, in raise_mysql_exception
    raise errorclass(errno, errval)
pymysql.err.ProgrammingError: (1146, "Table '_9c5dc86bed914016.tabSingles' doesn't exist")
2025-06-20 17:19:30,916 ERROR scheduler Exception in Enqueue Events for Site viva
Traceback (most recent call last):
  File "/home/<USER>/dev/frappe-bench/apps/frappe/frappe/utils/scheduler.py", line 99, in enqueue_events_for_site
    if is_scheduler_inactive():
       ^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/frappe-bench/apps/frappe/frappe/utils/scheduler.py", line 143, in is_scheduler_inactive
    if is_scheduler_disabled(verbose=verbose):
       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/frappe-bench/apps/frappe/frappe/utils/scheduler.py", line 156, in is_scheduler_disabled
    frappe.db.get_single_value("System Settings", "enable_scheduler")
  File "/home/<USER>/dev/frappe-bench/apps/frappe/frappe/database/database.py", line 837, in get_single_value
    ).run()
      ^^^^^
  File "/home/<USER>/dev/frappe-bench/apps/frappe/frappe/query_builder/utils.py", line 87, in execute_query
    result = frappe.db.sql(query, params, *args, **kwargs)  # nosemgrep
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/frappe-bench/apps/frappe/frappe/database/database.py", line 230, in sql
    self._cursor.execute(query, values)
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/pymysql/cursors.py", line 153, in execute
    result = self._query(query)
             ^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/pymysql/cursors.py", line 322, in _query
    conn.query(q)
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/pymysql/connections.py", line 563, in query
    self._affected_rows = self._read_query_result(unbuffered=unbuffered)
                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/pymysql/connections.py", line 825, in _read_query_result
    result.read()
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/pymysql/connections.py", line 1199, in read
    first_packet = self.connection._read_packet()
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/pymysql/connections.py", line 775, in _read_packet
    packet.raise_for_error()
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/pymysql/protocol.py", line 219, in raise_for_error
    err.raise_mysql_exception(self._data)
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/pymysql/err.py", line 150, in raise_mysql_exception
    raise errorclass(errno, errval)
pymysql.err.ProgrammingError: (1146, "Table '_9c5dc86bed914016.tabSingles' doesn't exist")
2025-06-20 17:20:31,144 ERROR scheduler Exception in Enqueue Events for Site viva
Traceback (most recent call last):
  File "/home/<USER>/dev/frappe-bench/apps/frappe/frappe/utils/scheduler.py", line 99, in enqueue_events_for_site
    if is_scheduler_inactive():
       ^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/frappe-bench/apps/frappe/frappe/utils/scheduler.py", line 143, in is_scheduler_inactive
    if is_scheduler_disabled(verbose=verbose):
       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/frappe-bench/apps/frappe/frappe/utils/scheduler.py", line 156, in is_scheduler_disabled
    frappe.db.get_single_value("System Settings", "enable_scheduler")
  File "/home/<USER>/dev/frappe-bench/apps/frappe/frappe/database/database.py", line 837, in get_single_value
    ).run()
      ^^^^^
  File "/home/<USER>/dev/frappe-bench/apps/frappe/frappe/query_builder/utils.py", line 87, in execute_query
    result = frappe.db.sql(query, params, *args, **kwargs)  # nosemgrep
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/frappe-bench/apps/frappe/frappe/database/database.py", line 230, in sql
    self._cursor.execute(query, values)
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/pymysql/cursors.py", line 153, in execute
    result = self._query(query)
             ^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/pymysql/cursors.py", line 322, in _query
    conn.query(q)
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/pymysql/connections.py", line 563, in query
    self._affected_rows = self._read_query_result(unbuffered=unbuffered)
                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/pymysql/connections.py", line 825, in _read_query_result
    result.read()
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/pymysql/connections.py", line 1199, in read
    first_packet = self.connection._read_packet()
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/pymysql/connections.py", line 775, in _read_packet
    packet.raise_for_error()
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/pymysql/protocol.py", line 219, in raise_for_error
    err.raise_mysql_exception(self._data)
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/pymysql/err.py", line 150, in raise_mysql_exception
    raise errorclass(errno, errval)
pymysql.err.ProgrammingError: (1146, "Table '_9c5dc86bed914016.tabSingles' doesn't exist")
2025-06-20 17:21:31,315 ERROR scheduler Exception in Enqueue Events for Site viva
Traceback (most recent call last):
  File "/home/<USER>/dev/frappe-bench/apps/frappe/frappe/utils/scheduler.py", line 99, in enqueue_events_for_site
    if is_scheduler_inactive():
       ^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/frappe-bench/apps/frappe/frappe/utils/scheduler.py", line 143, in is_scheduler_inactive
    if is_scheduler_disabled(verbose=verbose):
       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/frappe-bench/apps/frappe/frappe/utils/scheduler.py", line 156, in is_scheduler_disabled
    frappe.db.get_single_value("System Settings", "enable_scheduler")
  File "/home/<USER>/dev/frappe-bench/apps/frappe/frappe/database/database.py", line 837, in get_single_value
    ).run()
      ^^^^^
  File "/home/<USER>/dev/frappe-bench/apps/frappe/frappe/query_builder/utils.py", line 87, in execute_query
    result = frappe.db.sql(query, params, *args, **kwargs)  # nosemgrep
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/frappe-bench/apps/frappe/frappe/database/database.py", line 230, in sql
    self._cursor.execute(query, values)
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/pymysql/cursors.py", line 153, in execute
    result = self._query(query)
             ^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/pymysql/cursors.py", line 322, in _query
    conn.query(q)
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/pymysql/connections.py", line 563, in query
    self._affected_rows = self._read_query_result(unbuffered=unbuffered)
                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/pymysql/connections.py", line 825, in _read_query_result
    result.read()
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/pymysql/connections.py", line 1199, in read
    first_packet = self.connection._read_packet()
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/pymysql/connections.py", line 775, in _read_packet
    packet.raise_for_error()
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/pymysql/protocol.py", line 219, in raise_for_error
    err.raise_mysql_exception(self._data)
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/pymysql/err.py", line 150, in raise_mysql_exception
    raise errorclass(errno, errval)
pymysql.err.ProgrammingError: (1146, "Table '_9c5dc86bed914016.tabSingles' doesn't exist")
2025-06-20 17:22:31,764 ERROR scheduler Exception in Enqueue Events for Site viva
Traceback (most recent call last):
  File "/home/<USER>/dev/frappe-bench/apps/frappe/frappe/utils/scheduler.py", line 99, in enqueue_events_for_site
    if is_scheduler_inactive():
       ^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/frappe-bench/apps/frappe/frappe/utils/scheduler.py", line 143, in is_scheduler_inactive
    if is_scheduler_disabled(verbose=verbose):
       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/frappe-bench/apps/frappe/frappe/utils/scheduler.py", line 156, in is_scheduler_disabled
    frappe.db.get_single_value("System Settings", "enable_scheduler")
  File "/home/<USER>/dev/frappe-bench/apps/frappe/frappe/database/database.py", line 837, in get_single_value
    ).run()
      ^^^^^
  File "/home/<USER>/dev/frappe-bench/apps/frappe/frappe/query_builder/utils.py", line 87, in execute_query
    result = frappe.db.sql(query, params, *args, **kwargs)  # nosemgrep
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/frappe-bench/apps/frappe/frappe/database/database.py", line 230, in sql
    self._cursor.execute(query, values)
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/pymysql/cursors.py", line 153, in execute
    result = self._query(query)
             ^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/pymysql/cursors.py", line 322, in _query
    conn.query(q)
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/pymysql/connections.py", line 563, in query
    self._affected_rows = self._read_query_result(unbuffered=unbuffered)
                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/pymysql/connections.py", line 825, in _read_query_result
    result.read()
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/pymysql/connections.py", line 1199, in read
    first_packet = self.connection._read_packet()
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/pymysql/connections.py", line 775, in _read_packet
    packet.raise_for_error()
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/pymysql/protocol.py", line 219, in raise_for_error
    err.raise_mysql_exception(self._data)
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/pymysql/err.py", line 150, in raise_mysql_exception
    raise errorclass(errno, errval)
pymysql.err.ProgrammingError: (1146, "Table '_9c5dc86bed914016.tabSingles' doesn't exist")
2025-06-20 17:23:32,404 ERROR scheduler Exception in Enqueue Events for Site viva
Traceback (most recent call last):
  File "/home/<USER>/dev/frappe-bench/apps/frappe/frappe/utils/scheduler.py", line 99, in enqueue_events_for_site
    if is_scheduler_inactive():
       ^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/frappe-bench/apps/frappe/frappe/utils/scheduler.py", line 143, in is_scheduler_inactive
    if is_scheduler_disabled(verbose=verbose):
       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/frappe-bench/apps/frappe/frappe/utils/scheduler.py", line 156, in is_scheduler_disabled
    frappe.db.get_single_value("System Settings", "enable_scheduler")
  File "/home/<USER>/dev/frappe-bench/apps/frappe/frappe/database/database.py", line 837, in get_single_value
    ).run()
      ^^^^^
  File "/home/<USER>/dev/frappe-bench/apps/frappe/frappe/query_builder/utils.py", line 87, in execute_query
    result = frappe.db.sql(query, params, *args, **kwargs)  # nosemgrep
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/frappe-bench/apps/frappe/frappe/database/database.py", line 230, in sql
    self._cursor.execute(query, values)
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/pymysql/cursors.py", line 153, in execute
    result = self._query(query)
             ^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/pymysql/cursors.py", line 322, in _query
    conn.query(q)
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/pymysql/connections.py", line 563, in query
    self._affected_rows = self._read_query_result(unbuffered=unbuffered)
                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/pymysql/connections.py", line 825, in _read_query_result
    result.read()
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/pymysql/connections.py", line 1199, in read
    first_packet = self.connection._read_packet()
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/pymysql/connections.py", line 775, in _read_packet
    packet.raise_for_error()
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/pymysql/protocol.py", line 219, in raise_for_error
    err.raise_mysql_exception(self._data)
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/pymysql/err.py", line 150, in raise_mysql_exception
    raise errorclass(errno, errval)
pymysql.err.ProgrammingError: (1146, "Table '_9c5dc86bed914016.tabSingles' doesn't exist")
2025-06-20 17:24:32,594 ERROR scheduler Exception in Enqueue Events for Site viva
Traceback (most recent call last):
  File "/home/<USER>/dev/frappe-bench/apps/frappe/frappe/utils/scheduler.py", line 99, in enqueue_events_for_site
    if is_scheduler_inactive():
       ^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/frappe-bench/apps/frappe/frappe/utils/scheduler.py", line 143, in is_scheduler_inactive
    if is_scheduler_disabled(verbose=verbose):
       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/frappe-bench/apps/frappe/frappe/utils/scheduler.py", line 156, in is_scheduler_disabled
    frappe.db.get_single_value("System Settings", "enable_scheduler")
  File "/home/<USER>/dev/frappe-bench/apps/frappe/frappe/database/database.py", line 837, in get_single_value
    ).run()
      ^^^^^
  File "/home/<USER>/dev/frappe-bench/apps/frappe/frappe/query_builder/utils.py", line 87, in execute_query
    result = frappe.db.sql(query, params, *args, **kwargs)  # nosemgrep
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/frappe-bench/apps/frappe/frappe/database/database.py", line 230, in sql
    self._cursor.execute(query, values)
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/pymysql/cursors.py", line 153, in execute
    result = self._query(query)
             ^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/pymysql/cursors.py", line 322, in _query
    conn.query(q)
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/pymysql/connections.py", line 563, in query
    self._affected_rows = self._read_query_result(unbuffered=unbuffered)
                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/pymysql/connections.py", line 825, in _read_query_result
    result.read()
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/pymysql/connections.py", line 1199, in read
    first_packet = self.connection._read_packet()
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/pymysql/connections.py", line 775, in _read_packet
    packet.raise_for_error()
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/pymysql/protocol.py", line 219, in raise_for_error
    err.raise_mysql_exception(self._data)
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/pymysql/err.py", line 150, in raise_mysql_exception
    raise errorclass(errno, errval)
pymysql.err.ProgrammingError: (1146, "Table '_9c5dc86bed914016.tabSingles' doesn't exist")
2025-06-20 17:25:32,945 ERROR scheduler Exception in Enqueue Events for Site viva
Traceback (most recent call last):
  File "/home/<USER>/dev/frappe-bench/apps/frappe/frappe/utils/scheduler.py", line 99, in enqueue_events_for_site
    if is_scheduler_inactive():
       ^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/frappe-bench/apps/frappe/frappe/utils/scheduler.py", line 143, in is_scheduler_inactive
    if is_scheduler_disabled(verbose=verbose):
       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/frappe-bench/apps/frappe/frappe/utils/scheduler.py", line 156, in is_scheduler_disabled
    frappe.db.get_single_value("System Settings", "enable_scheduler")
  File "/home/<USER>/dev/frappe-bench/apps/frappe/frappe/database/database.py", line 837, in get_single_value
    ).run()
      ^^^^^
  File "/home/<USER>/dev/frappe-bench/apps/frappe/frappe/query_builder/utils.py", line 87, in execute_query
    result = frappe.db.sql(query, params, *args, **kwargs)  # nosemgrep
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/frappe-bench/apps/frappe/frappe/database/database.py", line 230, in sql
    self._cursor.execute(query, values)
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/pymysql/cursors.py", line 153, in execute
    result = self._query(query)
             ^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/pymysql/cursors.py", line 322, in _query
    conn.query(q)
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/pymysql/connections.py", line 563, in query
    self._affected_rows = self._read_query_result(unbuffered=unbuffered)
                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/pymysql/connections.py", line 825, in _read_query_result
    result.read()
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/pymysql/connections.py", line 1199, in read
    first_packet = self.connection._read_packet()
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/pymysql/connections.py", line 775, in _read_packet
    packet.raise_for_error()
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/pymysql/protocol.py", line 219, in raise_for_error
    err.raise_mysql_exception(self._data)
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/pymysql/err.py", line 150, in raise_mysql_exception
    raise errorclass(errno, errval)
pymysql.err.ProgrammingError: (1146, "Table '_9c5dc86bed914016.tabSingles' doesn't exist")
2025-06-20 17:26:33,159 ERROR scheduler Exception in Enqueue Events for Site viva
Traceback (most recent call last):
  File "/home/<USER>/dev/frappe-bench/apps/frappe/frappe/utils/scheduler.py", line 99, in enqueue_events_for_site
    if is_scheduler_inactive():
       ^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/frappe-bench/apps/frappe/frappe/utils/scheduler.py", line 143, in is_scheduler_inactive
    if is_scheduler_disabled(verbose=verbose):
       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/frappe-bench/apps/frappe/frappe/utils/scheduler.py", line 156, in is_scheduler_disabled
    frappe.db.get_single_value("System Settings", "enable_scheduler")
  File "/home/<USER>/dev/frappe-bench/apps/frappe/frappe/database/database.py", line 837, in get_single_value
    ).run()
      ^^^^^
  File "/home/<USER>/dev/frappe-bench/apps/frappe/frappe/query_builder/utils.py", line 87, in execute_query
    result = frappe.db.sql(query, params, *args, **kwargs)  # nosemgrep
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/frappe-bench/apps/frappe/frappe/database/database.py", line 230, in sql
    self._cursor.execute(query, values)
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/pymysql/cursors.py", line 153, in execute
    result = self._query(query)
             ^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/pymysql/cursors.py", line 322, in _query
    conn.query(q)
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/pymysql/connections.py", line 563, in query
    self._affected_rows = self._read_query_result(unbuffered=unbuffered)
                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/pymysql/connections.py", line 825, in _read_query_result
    result.read()
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/pymysql/connections.py", line 1199, in read
    first_packet = self.connection._read_packet()
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/pymysql/connections.py", line 775, in _read_packet
    packet.raise_for_error()
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/pymysql/protocol.py", line 219, in raise_for_error
    err.raise_mysql_exception(self._data)
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/pymysql/err.py", line 150, in raise_mysql_exception
    raise errorclass(errno, errval)
pymysql.err.ProgrammingError: (1146, "Table '_9c5dc86bed914016.tabSingles' doesn't exist")
2025-06-20 17:27:33,512 ERROR scheduler Exception in Enqueue Events for Site viva
Traceback (most recent call last):
  File "/home/<USER>/dev/frappe-bench/apps/frappe/frappe/utils/scheduler.py", line 99, in enqueue_events_for_site
    if is_scheduler_inactive():
       ^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/frappe-bench/apps/frappe/frappe/utils/scheduler.py", line 143, in is_scheduler_inactive
    if is_scheduler_disabled(verbose=verbose):
       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/frappe-bench/apps/frappe/frappe/utils/scheduler.py", line 156, in is_scheduler_disabled
    frappe.db.get_single_value("System Settings", "enable_scheduler")
  File "/home/<USER>/dev/frappe-bench/apps/frappe/frappe/database/database.py", line 837, in get_single_value
    ).run()
      ^^^^^
  File "/home/<USER>/dev/frappe-bench/apps/frappe/frappe/query_builder/utils.py", line 87, in execute_query
    result = frappe.db.sql(query, params, *args, **kwargs)  # nosemgrep
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/frappe-bench/apps/frappe/frappe/database/database.py", line 230, in sql
    self._cursor.execute(query, values)
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/pymysql/cursors.py", line 153, in execute
    result = self._query(query)
             ^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/pymysql/cursors.py", line 322, in _query
    conn.query(q)
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/pymysql/connections.py", line 563, in query
    self._affected_rows = self._read_query_result(unbuffered=unbuffered)
                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/pymysql/connections.py", line 825, in _read_query_result
    result.read()
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/pymysql/connections.py", line 1199, in read
    first_packet = self.connection._read_packet()
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/pymysql/connections.py", line 775, in _read_packet
    packet.raise_for_error()
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/pymysql/protocol.py", line 219, in raise_for_error
    err.raise_mysql_exception(self._data)
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/pymysql/err.py", line 150, in raise_mysql_exception
    raise errorclass(errno, errval)
pymysql.err.ProgrammingError: (1146, "Table '_9c5dc86bed914016.tabSingles' doesn't exist")
2025-06-20 17:28:34,160 ERROR scheduler Exception in Enqueue Events for Site viva
Traceback (most recent call last):
  File "/home/<USER>/dev/frappe-bench/apps/frappe/frappe/utils/scheduler.py", line 99, in enqueue_events_for_site
    if is_scheduler_inactive():
       ^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/frappe-bench/apps/frappe/frappe/utils/scheduler.py", line 143, in is_scheduler_inactive
    if is_scheduler_disabled(verbose=verbose):
       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/frappe-bench/apps/frappe/frappe/utils/scheduler.py", line 156, in is_scheduler_disabled
    frappe.db.get_single_value("System Settings", "enable_scheduler")
  File "/home/<USER>/dev/frappe-bench/apps/frappe/frappe/database/database.py", line 837, in get_single_value
    ).run()
      ^^^^^
  File "/home/<USER>/dev/frappe-bench/apps/frappe/frappe/query_builder/utils.py", line 87, in execute_query
    result = frappe.db.sql(query, params, *args, **kwargs)  # nosemgrep
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/frappe-bench/apps/frappe/frappe/database/database.py", line 230, in sql
    self._cursor.execute(query, values)
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/pymysql/cursors.py", line 153, in execute
    result = self._query(query)
             ^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/pymysql/cursors.py", line 322, in _query
    conn.query(q)
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/pymysql/connections.py", line 563, in query
    self._affected_rows = self._read_query_result(unbuffered=unbuffered)
                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/pymysql/connections.py", line 825, in _read_query_result
    result.read()
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/pymysql/connections.py", line 1199, in read
    first_packet = self.connection._read_packet()
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/pymysql/connections.py", line 775, in _read_packet
    packet.raise_for_error()
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/pymysql/protocol.py", line 219, in raise_for_error
    err.raise_mysql_exception(self._data)
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/pymysql/err.py", line 150, in raise_mysql_exception
    raise errorclass(errno, errval)
pymysql.err.ProgrammingError: (1146, "Table '_9c5dc86bed914016.tabSingles' doesn't exist")
2025-06-20 17:29:34,713 ERROR scheduler Exception in Enqueue Events for Site viva
Traceback (most recent call last):
  File "/home/<USER>/dev/frappe-bench/apps/frappe/frappe/utils/scheduler.py", line 99, in enqueue_events_for_site
    if is_scheduler_inactive():
       ^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/frappe-bench/apps/frappe/frappe/utils/scheduler.py", line 143, in is_scheduler_inactive
    if is_scheduler_disabled(verbose=verbose):
       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/frappe-bench/apps/frappe/frappe/utils/scheduler.py", line 156, in is_scheduler_disabled
    frappe.db.get_single_value("System Settings", "enable_scheduler")
  File "/home/<USER>/dev/frappe-bench/apps/frappe/frappe/database/database.py", line 837, in get_single_value
    ).run()
      ^^^^^
  File "/home/<USER>/dev/frappe-bench/apps/frappe/frappe/query_builder/utils.py", line 87, in execute_query
    result = frappe.db.sql(query, params, *args, **kwargs)  # nosemgrep
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/frappe-bench/apps/frappe/frappe/database/database.py", line 230, in sql
    self._cursor.execute(query, values)
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/pymysql/cursors.py", line 153, in execute
    result = self._query(query)
             ^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/pymysql/cursors.py", line 322, in _query
    conn.query(q)
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/pymysql/connections.py", line 563, in query
    self._affected_rows = self._read_query_result(unbuffered=unbuffered)
                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/pymysql/connections.py", line 825, in _read_query_result
    result.read()
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/pymysql/connections.py", line 1199, in read
    first_packet = self.connection._read_packet()
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/pymysql/connections.py", line 775, in _read_packet
    packet.raise_for_error()
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/pymysql/protocol.py", line 219, in raise_for_error
    err.raise_mysql_exception(self._data)
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/pymysql/err.py", line 150, in raise_mysql_exception
    raise errorclass(errno, errval)
pymysql.err.ProgrammingError: (1146, "Table '_9c5dc86bed914016.tabSingles' doesn't exist")
2025-06-20 17:30:34,826 ERROR scheduler Exception in Enqueue Events for Site viva
Traceback (most recent call last):
  File "/home/<USER>/dev/frappe-bench/apps/frappe/frappe/utils/scheduler.py", line 99, in enqueue_events_for_site
    if is_scheduler_inactive():
       ^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/frappe-bench/apps/frappe/frappe/utils/scheduler.py", line 143, in is_scheduler_inactive
    if is_scheduler_disabled(verbose=verbose):
       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/frappe-bench/apps/frappe/frappe/utils/scheduler.py", line 156, in is_scheduler_disabled
    frappe.db.get_single_value("System Settings", "enable_scheduler")
  File "/home/<USER>/dev/frappe-bench/apps/frappe/frappe/database/database.py", line 837, in get_single_value
    ).run()
      ^^^^^
  File "/home/<USER>/dev/frappe-bench/apps/frappe/frappe/query_builder/utils.py", line 87, in execute_query
    result = frappe.db.sql(query, params, *args, **kwargs)  # nosemgrep
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/frappe-bench/apps/frappe/frappe/database/database.py", line 230, in sql
    self._cursor.execute(query, values)
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/pymysql/cursors.py", line 153, in execute
    result = self._query(query)
             ^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/pymysql/cursors.py", line 322, in _query
    conn.query(q)
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/pymysql/connections.py", line 563, in query
    self._affected_rows = self._read_query_result(unbuffered=unbuffered)
                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/pymysql/connections.py", line 825, in _read_query_result
    result.read()
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/pymysql/connections.py", line 1199, in read
    first_packet = self.connection._read_packet()
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/pymysql/connections.py", line 775, in _read_packet
    packet.raise_for_error()
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/pymysql/protocol.py", line 219, in raise_for_error
    err.raise_mysql_exception(self._data)
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/pymysql/err.py", line 150, in raise_mysql_exception
    raise errorclass(errno, errval)
pymysql.err.ProgrammingError: (1146, "Table '_9c5dc86bed914016.tabSingles' doesn't exist")
2025-06-20 17:31:35,226 ERROR scheduler Exception in Enqueue Events for Site viva
Traceback (most recent call last):
  File "/home/<USER>/dev/frappe-bench/apps/frappe/frappe/utils/scheduler.py", line 99, in enqueue_events_for_site
    if is_scheduler_inactive():
       ^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/frappe-bench/apps/frappe/frappe/utils/scheduler.py", line 143, in is_scheduler_inactive
    if is_scheduler_disabled(verbose=verbose):
       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/frappe-bench/apps/frappe/frappe/utils/scheduler.py", line 156, in is_scheduler_disabled
    frappe.db.get_single_value("System Settings", "enable_scheduler")
  File "/home/<USER>/dev/frappe-bench/apps/frappe/frappe/database/database.py", line 837, in get_single_value
    ).run()
      ^^^^^
  File "/home/<USER>/dev/frappe-bench/apps/frappe/frappe/query_builder/utils.py", line 87, in execute_query
    result = frappe.db.sql(query, params, *args, **kwargs)  # nosemgrep
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/frappe-bench/apps/frappe/frappe/database/database.py", line 230, in sql
    self._cursor.execute(query, values)
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/pymysql/cursors.py", line 153, in execute
    result = self._query(query)
             ^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/pymysql/cursors.py", line 322, in _query
    conn.query(q)
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/pymysql/connections.py", line 563, in query
    self._affected_rows = self._read_query_result(unbuffered=unbuffered)
                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/pymysql/connections.py", line 825, in _read_query_result
    result.read()
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/pymysql/connections.py", line 1199, in read
    first_packet = self.connection._read_packet()
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/pymysql/connections.py", line 775, in _read_packet
    packet.raise_for_error()
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/pymysql/protocol.py", line 219, in raise_for_error
    err.raise_mysql_exception(self._data)
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/pymysql/err.py", line 150, in raise_mysql_exception
    raise errorclass(errno, errval)
pymysql.err.ProgrammingError: (1146, "Table '_9c5dc86bed914016.tabSingles' doesn't exist")
2025-06-20 17:32:35,886 ERROR scheduler Exception in Enqueue Events for Site viva
Traceback (most recent call last):
  File "/home/<USER>/dev/frappe-bench/apps/frappe/frappe/utils/scheduler.py", line 99, in enqueue_events_for_site
    if is_scheduler_inactive():
       ^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/frappe-bench/apps/frappe/frappe/utils/scheduler.py", line 143, in is_scheduler_inactive
    if is_scheduler_disabled(verbose=verbose):
       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/frappe-bench/apps/frappe/frappe/utils/scheduler.py", line 156, in is_scheduler_disabled
    frappe.db.get_single_value("System Settings", "enable_scheduler")
  File "/home/<USER>/dev/frappe-bench/apps/frappe/frappe/database/database.py", line 837, in get_single_value
    ).run()
      ^^^^^
  File "/home/<USER>/dev/frappe-bench/apps/frappe/frappe/query_builder/utils.py", line 87, in execute_query
    result = frappe.db.sql(query, params, *args, **kwargs)  # nosemgrep
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/frappe-bench/apps/frappe/frappe/database/database.py", line 230, in sql
    self._cursor.execute(query, values)
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/pymysql/cursors.py", line 153, in execute
    result = self._query(query)
             ^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/pymysql/cursors.py", line 322, in _query
    conn.query(q)
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/pymysql/connections.py", line 563, in query
    self._affected_rows = self._read_query_result(unbuffered=unbuffered)
                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/pymysql/connections.py", line 825, in _read_query_result
    result.read()
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/pymysql/connections.py", line 1199, in read
    first_packet = self.connection._read_packet()
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/pymysql/connections.py", line 775, in _read_packet
    packet.raise_for_error()
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/pymysql/protocol.py", line 219, in raise_for_error
    err.raise_mysql_exception(self._data)
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/pymysql/err.py", line 150, in raise_mysql_exception
    raise errorclass(errno, errval)
pymysql.err.ProgrammingError: (1146, "Table '_9c5dc86bed914016.tabSingles' doesn't exist")
2025-06-20 17:33:36,326 ERROR scheduler Exception in Enqueue Events for Site viva
Traceback (most recent call last):
  File "/home/<USER>/dev/frappe-bench/apps/frappe/frappe/utils/scheduler.py", line 99, in enqueue_events_for_site
    if is_scheduler_inactive():
       ^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/frappe-bench/apps/frappe/frappe/utils/scheduler.py", line 143, in is_scheduler_inactive
    if is_scheduler_disabled(verbose=verbose):
       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/frappe-bench/apps/frappe/frappe/utils/scheduler.py", line 156, in is_scheduler_disabled
    frappe.db.get_single_value("System Settings", "enable_scheduler")
  File "/home/<USER>/dev/frappe-bench/apps/frappe/frappe/database/database.py", line 837, in get_single_value
    ).run()
      ^^^^^
  File "/home/<USER>/dev/frappe-bench/apps/frappe/frappe/query_builder/utils.py", line 87, in execute_query
    result = frappe.db.sql(query, params, *args, **kwargs)  # nosemgrep
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/frappe-bench/apps/frappe/frappe/database/database.py", line 230, in sql
    self._cursor.execute(query, values)
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/pymysql/cursors.py", line 153, in execute
    result = self._query(query)
             ^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/pymysql/cursors.py", line 322, in _query
    conn.query(q)
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/pymysql/connections.py", line 563, in query
    self._affected_rows = self._read_query_result(unbuffered=unbuffered)
                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/pymysql/connections.py", line 825, in _read_query_result
    result.read()
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/pymysql/connections.py", line 1199, in read
    first_packet = self.connection._read_packet()
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/pymysql/connections.py", line 775, in _read_packet
    packet.raise_for_error()
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/pymysql/protocol.py", line 219, in raise_for_error
    err.raise_mysql_exception(self._data)
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/pymysql/err.py", line 150, in raise_mysql_exception
    raise errorclass(errno, errval)
pymysql.err.ProgrammingError: (1146, "Table '_9c5dc86bed914016.tabSingles' doesn't exist")
2025-06-20 17:34:36,747 ERROR scheduler Exception in Enqueue Events for Site viva
Traceback (most recent call last):
  File "/home/<USER>/dev/frappe-bench/apps/frappe/frappe/utils/scheduler.py", line 99, in enqueue_events_for_site
    if is_scheduler_inactive():
       ^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/frappe-bench/apps/frappe/frappe/utils/scheduler.py", line 143, in is_scheduler_inactive
    if is_scheduler_disabled(verbose=verbose):
       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/frappe-bench/apps/frappe/frappe/utils/scheduler.py", line 156, in is_scheduler_disabled
    frappe.db.get_single_value("System Settings", "enable_scheduler")
  File "/home/<USER>/dev/frappe-bench/apps/frappe/frappe/database/database.py", line 837, in get_single_value
    ).run()
      ^^^^^
  File "/home/<USER>/dev/frappe-bench/apps/frappe/frappe/query_builder/utils.py", line 87, in execute_query
    result = frappe.db.sql(query, params, *args, **kwargs)  # nosemgrep
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/frappe-bench/apps/frappe/frappe/database/database.py", line 230, in sql
    self._cursor.execute(query, values)
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/pymysql/cursors.py", line 153, in execute
    result = self._query(query)
             ^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/pymysql/cursors.py", line 322, in _query
    conn.query(q)
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/pymysql/connections.py", line 563, in query
    self._affected_rows = self._read_query_result(unbuffered=unbuffered)
                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/pymysql/connections.py", line 825, in _read_query_result
    result.read()
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/pymysql/connections.py", line 1199, in read
    first_packet = self.connection._read_packet()
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/pymysql/connections.py", line 775, in _read_packet
    packet.raise_for_error()
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/pymysql/protocol.py", line 219, in raise_for_error
    err.raise_mysql_exception(self._data)
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/pymysql/err.py", line 150, in raise_mysql_exception
    raise errorclass(errno, errval)
pymysql.err.ProgrammingError: (1146, "Table '_9c5dc86bed914016.tabSingles' doesn't exist")
2025-06-20 17:35:36,934 ERROR scheduler Exception in Enqueue Events for Site viva
Traceback (most recent call last):
  File "/home/<USER>/dev/frappe-bench/apps/frappe/frappe/utils/scheduler.py", line 99, in enqueue_events_for_site
    if is_scheduler_inactive():
       ^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/frappe-bench/apps/frappe/frappe/utils/scheduler.py", line 143, in is_scheduler_inactive
    if is_scheduler_disabled(verbose=verbose):
       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/frappe-bench/apps/frappe/frappe/utils/scheduler.py", line 156, in is_scheduler_disabled
    frappe.db.get_single_value("System Settings", "enable_scheduler")
  File "/home/<USER>/dev/frappe-bench/apps/frappe/frappe/database/database.py", line 837, in get_single_value
    ).run()
      ^^^^^
  File "/home/<USER>/dev/frappe-bench/apps/frappe/frappe/query_builder/utils.py", line 87, in execute_query
    result = frappe.db.sql(query, params, *args, **kwargs)  # nosemgrep
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/frappe-bench/apps/frappe/frappe/database/database.py", line 230, in sql
    self._cursor.execute(query, values)
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/pymysql/cursors.py", line 153, in execute
    result = self._query(query)
             ^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/pymysql/cursors.py", line 322, in _query
    conn.query(q)
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/pymysql/connections.py", line 563, in query
    self._affected_rows = self._read_query_result(unbuffered=unbuffered)
                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/pymysql/connections.py", line 825, in _read_query_result
    result.read()
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/pymysql/connections.py", line 1199, in read
    first_packet = self.connection._read_packet()
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/pymysql/connections.py", line 775, in _read_packet
    packet.raise_for_error()
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/pymysql/protocol.py", line 219, in raise_for_error
    err.raise_mysql_exception(self._data)
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/pymysql/err.py", line 150, in raise_mysql_exception
    raise errorclass(errno, errval)
pymysql.err.ProgrammingError: (1146, "Table '_9c5dc86bed914016.tabSingles' doesn't exist")
2025-06-20 17:36:37,768 ERROR scheduler Exception in Enqueue Events for Site viva
Traceback (most recent call last):
  File "/home/<USER>/dev/frappe-bench/apps/frappe/frappe/utils/scheduler.py", line 99, in enqueue_events_for_site
    if is_scheduler_inactive():
       ^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/frappe-bench/apps/frappe/frappe/utils/scheduler.py", line 143, in is_scheduler_inactive
    if is_scheduler_disabled(verbose=verbose):
       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/frappe-bench/apps/frappe/frappe/utils/scheduler.py", line 156, in is_scheduler_disabled
    frappe.db.get_single_value("System Settings", "enable_scheduler")
  File "/home/<USER>/dev/frappe-bench/apps/frappe/frappe/database/database.py", line 837, in get_single_value
    ).run()
      ^^^^^
  File "/home/<USER>/dev/frappe-bench/apps/frappe/frappe/query_builder/utils.py", line 87, in execute_query
    result = frappe.db.sql(query, params, *args, **kwargs)  # nosemgrep
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/frappe-bench/apps/frappe/frappe/database/database.py", line 230, in sql
    self._cursor.execute(query, values)
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/pymysql/cursors.py", line 153, in execute
    result = self._query(query)
             ^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/pymysql/cursors.py", line 322, in _query
    conn.query(q)
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/pymysql/connections.py", line 563, in query
    self._affected_rows = self._read_query_result(unbuffered=unbuffered)
                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/pymysql/connections.py", line 825, in _read_query_result
    result.read()
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/pymysql/connections.py", line 1199, in read
    first_packet = self.connection._read_packet()
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/pymysql/connections.py", line 775, in _read_packet
    packet.raise_for_error()
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/pymysql/protocol.py", line 219, in raise_for_error
    err.raise_mysql_exception(self._data)
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/pymysql/err.py", line 150, in raise_mysql_exception
    raise errorclass(errno, errval)
pymysql.err.ProgrammingError: (1146, "Table '_9c5dc86bed914016.tabSingles' doesn't exist")
2025-06-20 17:37:37,972 ERROR scheduler Exception in Enqueue Events for Site viva
Traceback (most recent call last):
  File "/home/<USER>/dev/frappe-bench/apps/frappe/frappe/utils/scheduler.py", line 99, in enqueue_events_for_site
    if is_scheduler_inactive():
       ^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/frappe-bench/apps/frappe/frappe/utils/scheduler.py", line 143, in is_scheduler_inactive
    if is_scheduler_disabled(verbose=verbose):
       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/frappe-bench/apps/frappe/frappe/utils/scheduler.py", line 156, in is_scheduler_disabled
    frappe.db.get_single_value("System Settings", "enable_scheduler")
  File "/home/<USER>/dev/frappe-bench/apps/frappe/frappe/database/database.py", line 837, in get_single_value
    ).run()
      ^^^^^
  File "/home/<USER>/dev/frappe-bench/apps/frappe/frappe/query_builder/utils.py", line 87, in execute_query
    result = frappe.db.sql(query, params, *args, **kwargs)  # nosemgrep
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/frappe-bench/apps/frappe/frappe/database/database.py", line 230, in sql
    self._cursor.execute(query, values)
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/pymysql/cursors.py", line 153, in execute
    result = self._query(query)
             ^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/pymysql/cursors.py", line 322, in _query
    conn.query(q)
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/pymysql/connections.py", line 563, in query
    self._affected_rows = self._read_query_result(unbuffered=unbuffered)
                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/pymysql/connections.py", line 825, in _read_query_result
    result.read()
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/pymysql/connections.py", line 1199, in read
    first_packet = self.connection._read_packet()
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/pymysql/connections.py", line 775, in _read_packet
    packet.raise_for_error()
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/pymysql/protocol.py", line 219, in raise_for_error
    err.raise_mysql_exception(self._data)
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/pymysql/err.py", line 150, in raise_mysql_exception
    raise errorclass(errno, errval)
pymysql.err.ProgrammingError: (1146, "Table '_9c5dc86bed914016.tabSingles' doesn't exist")
2025-06-20 17:38:38,363 ERROR scheduler Exception in Enqueue Events for Site viva
Traceback (most recent call last):
  File "/home/<USER>/dev/frappe-bench/apps/frappe/frappe/utils/scheduler.py", line 99, in enqueue_events_for_site
    if is_scheduler_inactive():
       ^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/frappe-bench/apps/frappe/frappe/utils/scheduler.py", line 143, in is_scheduler_inactive
    if is_scheduler_disabled(verbose=verbose):
       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/frappe-bench/apps/frappe/frappe/utils/scheduler.py", line 156, in is_scheduler_disabled
    frappe.db.get_single_value("System Settings", "enable_scheduler")
  File "/home/<USER>/dev/frappe-bench/apps/frappe/frappe/database/database.py", line 837, in get_single_value
    ).run()
      ^^^^^
  File "/home/<USER>/dev/frappe-bench/apps/frappe/frappe/query_builder/utils.py", line 87, in execute_query
    result = frappe.db.sql(query, params, *args, **kwargs)  # nosemgrep
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/frappe-bench/apps/frappe/frappe/database/database.py", line 230, in sql
    self._cursor.execute(query, values)
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/pymysql/cursors.py", line 153, in execute
    result = self._query(query)
             ^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/pymysql/cursors.py", line 322, in _query
    conn.query(q)
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/pymysql/connections.py", line 563, in query
    self._affected_rows = self._read_query_result(unbuffered=unbuffered)
                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/pymysql/connections.py", line 825, in _read_query_result
    result.read()
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/pymysql/connections.py", line 1199, in read
    first_packet = self.connection._read_packet()
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/pymysql/connections.py", line 775, in _read_packet
    packet.raise_for_error()
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/pymysql/protocol.py", line 219, in raise_for_error
    err.raise_mysql_exception(self._data)
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/pymysql/err.py", line 150, in raise_mysql_exception
    raise errorclass(errno, errval)
pymysql.err.ProgrammingError: (1146, "Table '_9c5dc86bed914016.tabSingles' doesn't exist")
2025-06-20 17:39:38,786 ERROR scheduler Exception in Enqueue Events for Site viva
Traceback (most recent call last):
  File "/home/<USER>/dev/frappe-bench/apps/frappe/frappe/utils/scheduler.py", line 99, in enqueue_events_for_site
    if is_scheduler_inactive():
       ^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/frappe-bench/apps/frappe/frappe/utils/scheduler.py", line 143, in is_scheduler_inactive
    if is_scheduler_disabled(verbose=verbose):
       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/frappe-bench/apps/frappe/frappe/utils/scheduler.py", line 156, in is_scheduler_disabled
    frappe.db.get_single_value("System Settings", "enable_scheduler")
  File "/home/<USER>/dev/frappe-bench/apps/frappe/frappe/database/database.py", line 837, in get_single_value
    ).run()
      ^^^^^
  File "/home/<USER>/dev/frappe-bench/apps/frappe/frappe/query_builder/utils.py", line 87, in execute_query
    result = frappe.db.sql(query, params, *args, **kwargs)  # nosemgrep
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/frappe-bench/apps/frappe/frappe/database/database.py", line 230, in sql
    self._cursor.execute(query, values)
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/pymysql/cursors.py", line 153, in execute
    result = self._query(query)
             ^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/pymysql/cursors.py", line 322, in _query
    conn.query(q)
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/pymysql/connections.py", line 563, in query
    self._affected_rows = self._read_query_result(unbuffered=unbuffered)
                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/pymysql/connections.py", line 825, in _read_query_result
    result.read()
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/pymysql/connections.py", line 1199, in read
    first_packet = self.connection._read_packet()
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/pymysql/connections.py", line 775, in _read_packet
    packet.raise_for_error()
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/pymysql/protocol.py", line 219, in raise_for_error
    err.raise_mysql_exception(self._data)
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/pymysql/err.py", line 150, in raise_mysql_exception
    raise errorclass(errno, errval)
pymysql.err.ProgrammingError: (1146, "Table '_9c5dc86bed914016.tabSingles' doesn't exist")
2025-06-20 17:40:39,342 ERROR scheduler Exception in Enqueue Events for Site viva
Traceback (most recent call last):
  File "/home/<USER>/dev/frappe-bench/apps/frappe/frappe/utils/scheduler.py", line 99, in enqueue_events_for_site
    if is_scheduler_inactive():
       ^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/frappe-bench/apps/frappe/frappe/utils/scheduler.py", line 143, in is_scheduler_inactive
    if is_scheduler_disabled(verbose=verbose):
       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/frappe-bench/apps/frappe/frappe/utils/scheduler.py", line 156, in is_scheduler_disabled
    frappe.db.get_single_value("System Settings", "enable_scheduler")
  File "/home/<USER>/dev/frappe-bench/apps/frappe/frappe/database/database.py", line 837, in get_single_value
    ).run()
      ^^^^^
  File "/home/<USER>/dev/frappe-bench/apps/frappe/frappe/query_builder/utils.py", line 87, in execute_query
    result = frappe.db.sql(query, params, *args, **kwargs)  # nosemgrep
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/frappe-bench/apps/frappe/frappe/database/database.py", line 230, in sql
    self._cursor.execute(query, values)
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/pymysql/cursors.py", line 153, in execute
    result = self._query(query)
             ^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/pymysql/cursors.py", line 322, in _query
    conn.query(q)
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/pymysql/connections.py", line 563, in query
    self._affected_rows = self._read_query_result(unbuffered=unbuffered)
                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/pymysql/connections.py", line 825, in _read_query_result
    result.read()
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/pymysql/connections.py", line 1199, in read
    first_packet = self.connection._read_packet()
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/pymysql/connections.py", line 775, in _read_packet
    packet.raise_for_error()
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/pymysql/protocol.py", line 219, in raise_for_error
    err.raise_mysql_exception(self._data)
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/pymysql/err.py", line 150, in raise_mysql_exception
    raise errorclass(errno, errval)
pymysql.err.ProgrammingError: (1146, "Table '_9c5dc86bed914016.tabSingles' doesn't exist")
2025-06-20 17:41:39,513 ERROR scheduler Exception in Enqueue Events for Site viva
Traceback (most recent call last):
  File "/home/<USER>/dev/frappe-bench/apps/frappe/frappe/utils/scheduler.py", line 99, in enqueue_events_for_site
    if is_scheduler_inactive():
       ^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/frappe-bench/apps/frappe/frappe/utils/scheduler.py", line 143, in is_scheduler_inactive
    if is_scheduler_disabled(verbose=verbose):
       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/frappe-bench/apps/frappe/frappe/utils/scheduler.py", line 156, in is_scheduler_disabled
    frappe.db.get_single_value("System Settings", "enable_scheduler")
  File "/home/<USER>/dev/frappe-bench/apps/frappe/frappe/database/database.py", line 837, in get_single_value
    ).run()
      ^^^^^
  File "/home/<USER>/dev/frappe-bench/apps/frappe/frappe/query_builder/utils.py", line 87, in execute_query
    result = frappe.db.sql(query, params, *args, **kwargs)  # nosemgrep
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/frappe-bench/apps/frappe/frappe/database/database.py", line 230, in sql
    self._cursor.execute(query, values)
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/pymysql/cursors.py", line 153, in execute
    result = self._query(query)
             ^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/pymysql/cursors.py", line 322, in _query
    conn.query(q)
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/pymysql/connections.py", line 563, in query
    self._affected_rows = self._read_query_result(unbuffered=unbuffered)
                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/pymysql/connections.py", line 825, in _read_query_result
    result.read()
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/pymysql/connections.py", line 1199, in read
    first_packet = self.connection._read_packet()
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/pymysql/connections.py", line 775, in _read_packet
    packet.raise_for_error()
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/pymysql/protocol.py", line 219, in raise_for_error
    err.raise_mysql_exception(self._data)
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/pymysql/err.py", line 150, in raise_mysql_exception
    raise errorclass(errno, errval)
pymysql.err.ProgrammingError: (1146, "Table '_9c5dc86bed914016.tabSingles' doesn't exist")
2025-06-20 17:42:39,786 ERROR scheduler Exception in Enqueue Events for Site viva
Traceback (most recent call last):
  File "/home/<USER>/dev/frappe-bench/apps/frappe/frappe/utils/scheduler.py", line 99, in enqueue_events_for_site
    if is_scheduler_inactive():
       ^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/frappe-bench/apps/frappe/frappe/utils/scheduler.py", line 143, in is_scheduler_inactive
    if is_scheduler_disabled(verbose=verbose):
       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/frappe-bench/apps/frappe/frappe/utils/scheduler.py", line 156, in is_scheduler_disabled
    frappe.db.get_single_value("System Settings", "enable_scheduler")
  File "/home/<USER>/dev/frappe-bench/apps/frappe/frappe/database/database.py", line 837, in get_single_value
    ).run()
      ^^^^^
  File "/home/<USER>/dev/frappe-bench/apps/frappe/frappe/query_builder/utils.py", line 87, in execute_query
    result = frappe.db.sql(query, params, *args, **kwargs)  # nosemgrep
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/frappe-bench/apps/frappe/frappe/database/database.py", line 230, in sql
    self._cursor.execute(query, values)
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/pymysql/cursors.py", line 153, in execute
    result = self._query(query)
             ^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/pymysql/cursors.py", line 322, in _query
    conn.query(q)
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/pymysql/connections.py", line 563, in query
    self._affected_rows = self._read_query_result(unbuffered=unbuffered)
                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/pymysql/connections.py", line 825, in _read_query_result
    result.read()
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/pymysql/connections.py", line 1199, in read
    first_packet = self.connection._read_packet()
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/pymysql/connections.py", line 775, in _read_packet
    packet.raise_for_error()
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/pymysql/protocol.py", line 219, in raise_for_error
    err.raise_mysql_exception(self._data)
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/pymysql/err.py", line 150, in raise_mysql_exception
    raise errorclass(errno, errval)
pymysql.err.ProgrammingError: (1146, "Table '_9c5dc86bed914016.tabSingles' doesn't exist")
2025-06-20 17:43:40,232 ERROR scheduler Exception in Enqueue Events for Site viva
Traceback (most recent call last):
  File "/home/<USER>/dev/frappe-bench/apps/frappe/frappe/utils/scheduler.py", line 99, in enqueue_events_for_site
    if is_scheduler_inactive():
       ^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/frappe-bench/apps/frappe/frappe/utils/scheduler.py", line 143, in is_scheduler_inactive
    if is_scheduler_disabled(verbose=verbose):
       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/frappe-bench/apps/frappe/frappe/utils/scheduler.py", line 156, in is_scheduler_disabled
    frappe.db.get_single_value("System Settings", "enable_scheduler")
  File "/home/<USER>/dev/frappe-bench/apps/frappe/frappe/database/database.py", line 837, in get_single_value
    ).run()
      ^^^^^
  File "/home/<USER>/dev/frappe-bench/apps/frappe/frappe/query_builder/utils.py", line 87, in execute_query
    result = frappe.db.sql(query, params, *args, **kwargs)  # nosemgrep
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/frappe-bench/apps/frappe/frappe/database/database.py", line 230, in sql
    self._cursor.execute(query, values)
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/pymysql/cursors.py", line 153, in execute
    result = self._query(query)
             ^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/pymysql/cursors.py", line 322, in _query
    conn.query(q)
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/pymysql/connections.py", line 563, in query
    self._affected_rows = self._read_query_result(unbuffered=unbuffered)
                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/pymysql/connections.py", line 825, in _read_query_result
    result.read()
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/pymysql/connections.py", line 1199, in read
    first_packet = self.connection._read_packet()
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/pymysql/connections.py", line 775, in _read_packet
    packet.raise_for_error()
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/pymysql/protocol.py", line 219, in raise_for_error
    err.raise_mysql_exception(self._data)
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/pymysql/err.py", line 150, in raise_mysql_exception
    raise errorclass(errno, errval)
pymysql.err.ProgrammingError: (1146, "Table '_9c5dc86bed914016.tabSingles' doesn't exist")
