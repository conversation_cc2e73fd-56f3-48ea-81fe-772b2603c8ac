2025-06-20 14:02:55,702 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for health
2025-06-20 14:02:55,717 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for health
2025-06-20 14:02:55,743 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for health
2025-06-20 14:02:55,775 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for health
2025-06-20 14:02:55,798 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for health
2025-06-20 14:02:55,803 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for health
2025-06-20 14:02:55,816 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.update_last_sync_of_checkin because it was found in queue for health
2025-06-20 14:02:55,852 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for health
2025-06-20 14:02:55,855 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for health
2025-06-20 14:02:55,911 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.process_auto_attendance_for_all_shifts because it was found in queue for health
2025-06-20 14:02:55,939 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_schedule_assignment.shift_schedule_assignment.process_auto_shift_creation because it was found in queue for health
2025-06-20 14:02:55,953 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for health
2025-06-20 14:03:57,211 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for health
2025-06-20 14:03:57,275 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for health
2025-06-20 14:03:57,287 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.process_auto_attendance_for_all_shifts because it was found in queue for health
2025-06-20 14:03:57,324 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for health
2025-06-20 14:03:57,415 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for health
2025-06-20 14:03:57,432 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_schedule_assignment.shift_schedule_assignment.process_auto_shift_creation because it was found in queue for health
2025-06-20 14:03:57,447 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for health
2025-06-20 14:03:57,451 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for health
2025-06-20 14:03:57,472 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for health
2025-06-20 14:03:57,532 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.update_last_sync_of_checkin because it was found in queue for health
2025-06-20 14:03:57,548 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for health
2025-06-20 14:03:57,603 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for health
2025-06-20 14:03:57,627 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for health
2025-06-20 14:04:59,783 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for health
2025-06-20 14:04:59,827 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for health
2025-06-20 14:04:59,838 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for health
2025-06-20 14:04:59,843 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.update_last_sync_of_checkin because it was found in queue for health
2025-06-20 14:04:59,875 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for health
2025-06-20 14:04:59,918 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for health
2025-06-20 14:04:59,999 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for health
2025-06-20 14:05:00,014 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for health
2025-06-20 14:05:00,074 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for health
2025-06-20 14:05:00,122 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for health
2025-06-20 14:05:00,158 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_schedule_assignment.shift_schedule_assignment.process_auto_shift_creation because it was found in queue for health
2025-06-20 14:05:00,163 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for health
2025-06-20 14:06:00,217 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for health
2025-06-20 14:06:00,238 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_calendar.google_calendar.sync because it was found in queue for health
2025-06-20 14:06:00,292 ERROR scheduler Skipped queueing frappe.monitor.flush because it was found in queue for health
2025-06-20 14:06:00,325 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for health
2025-06-20 14:06:00,341 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for health
2025-06-20 14:06:00,345 ERROR scheduler Skipped queueing healthcare.healthcare.doctype.patient_appointment.patient_appointment.send_appointment_reminder because it was found in queue for health
2025-06-20 14:06:00,362 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.update_last_sync_of_checkin because it was found in queue for health
2025-06-20 14:06:00,393 ERROR scheduler Skipped queueing hrms.hr.doctype.interview.interview.send_interview_reminder because it was found in queue for health
2025-06-20 14:06:00,401 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for health
2025-06-20 14:06:00,467 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for health
2025-06-20 14:06:00,500 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for health
2025-06-20 14:06:00,513 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for health
2025-06-20 14:06:00,526 ERROR scheduler Skipped queueing payments.payment_gateways.doctype.razorpay_settings.razorpay_settings.capture_payment because it was found in queue for health
2025-06-20 14:06:00,557 ERROR scheduler Skipped queueing frappe.email.queue.flush because it was found in queue for health
2025-06-20 14:06:00,594 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for health
2025-06-20 14:06:00,620 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for health
2025-06-20 14:06:00,660 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for health
2025-06-20 14:06:00,665 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_schedule_assignment.shift_schedule_assignment.process_auto_shift_creation because it was found in queue for health
2025-06-20 14:07:01,672 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for health
2025-06-20 14:07:01,680 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for health
2025-06-20 14:07:01,699 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for health
2025-06-20 14:07:01,720 ERROR scheduler Skipped queueing frappe.email.queue.flush because it was found in queue for health
2025-06-20 14:07:01,738 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for health
2025-06-20 14:07:01,745 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for health
2025-06-20 14:07:01,773 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for health
2025-06-20 14:07:01,785 ERROR scheduler Skipped queueing hrms.hr.doctype.interview.interview.send_interview_reminder because it was found in queue for health
2025-06-20 14:07:01,815 ERROR scheduler Skipped queueing payments.payment_gateways.doctype.razorpay_settings.razorpay_settings.capture_payment because it was found in queue for health
2025-06-20 14:07:01,846 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_calendar.google_calendar.sync because it was found in queue for health
2025-06-20 14:07:01,864 ERROR scheduler Skipped queueing healthcare.healthcare.doctype.patient_appointment.patient_appointment.send_appointment_reminder because it was found in queue for health
2025-06-20 14:07:01,905 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_schedule_assignment.shift_schedule_assignment.process_auto_shift_creation because it was found in queue for health
2025-06-20 14:07:01,937 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.update_last_sync_of_checkin because it was found in queue for health
2025-06-20 14:07:01,949 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for health
2025-06-20 14:07:01,992 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for health
2025-06-20 14:07:02,001 ERROR scheduler Skipped queueing frappe.monitor.flush because it was found in queue for health
2025-06-20 14:07:02,005 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for health
2025-06-20 14:07:02,012 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for health
2025-06-20 14:08:03,419 ERROR scheduler Skipped queueing frappe.email.queue.flush because it was found in queue for health
2025-06-20 14:08:03,424 ERROR scheduler Skipped queueing payments.payment_gateways.doctype.razorpay_settings.razorpay_settings.capture_payment because it was found in queue for health
2025-06-20 14:08:03,495 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for health
2025-06-20 14:08:03,505 ERROR scheduler Skipped queueing hrms.hr.doctype.interview.interview.send_interview_reminder because it was found in queue for health
2025-06-20 14:08:03,542 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for health
2025-06-20 14:08:03,548 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for health
2025-06-20 14:08:03,572 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.update_last_sync_of_checkin because it was found in queue for health
2025-06-20 14:08:03,594 ERROR scheduler Skipped queueing frappe.monitor.flush because it was found in queue for health
2025-06-20 14:08:03,604 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for health
2025-06-20 14:08:03,616 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_schedule_assignment.shift_schedule_assignment.process_auto_shift_creation because it was found in queue for health
2025-06-20 14:08:03,674 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for health
2025-06-20 14:08:03,687 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_calendar.google_calendar.sync because it was found in queue for health
2025-06-20 14:08:03,698 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for health
2025-06-20 14:08:03,703 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for health
2025-06-20 14:08:03,711 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for health
2025-06-20 14:08:03,724 ERROR scheduler Skipped queueing healthcare.healthcare.doctype.patient_appointment.patient_appointment.send_appointment_reminder because it was found in queue for health
2025-06-20 14:08:03,752 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for health
2025-06-20 14:08:03,781 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for health
2025-06-20 14:09:05,363 ERROR scheduler Skipped queueing frappe.email.queue.flush because it was found in queue for health
2025-06-20 14:09:05,368 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.update_last_sync_of_checkin because it was found in queue for health
2025-06-20 14:09:05,398 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for health
2025-06-20 14:09:05,489 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for health
2025-06-20 14:09:05,530 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for health
2025-06-20 14:09:05,545 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for health
2025-06-20 14:09:05,549 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for health
2025-06-20 14:09:05,602 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for health
2025-06-20 14:09:05,619 ERROR scheduler Skipped queueing hrms.hr.doctype.interview.interview.send_interview_reminder because it was found in queue for health
2025-06-20 14:09:05,623 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_calendar.google_calendar.sync because it was found in queue for health
2025-06-20 14:09:05,638 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for health
2025-06-20 14:09:05,685 ERROR scheduler Skipped queueing frappe.monitor.flush because it was found in queue for health
2025-06-20 14:09:05,712 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_schedule_assignment.shift_schedule_assignment.process_auto_shift_creation because it was found in queue for health
2025-06-20 14:09:05,715 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for health
2025-06-20 14:09:05,720 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for health
2025-06-20 14:09:05,728 ERROR scheduler Skipped queueing healthcare.healthcare.doctype.patient_appointment.patient_appointment.send_appointment_reminder because it was found in queue for health
2025-06-20 14:09:05,736 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for health
2025-06-20 14:09:05,757 ERROR scheduler Skipped queueing payments.payment_gateways.doctype.razorpay_settings.razorpay_settings.capture_payment because it was found in queue for health
2025-06-20 15:01:11,301 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for health
2025-06-20 15:01:11,334 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for health
2025-06-20 15:01:11,348 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for health
2025-06-20 15:01:11,380 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for health
2025-06-20 15:01:11,407 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_schedule_assignment.shift_schedule_assignment.process_auto_shift_creation because it was found in queue for health
2025-06-20 15:01:11,429 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for health
2025-06-20 15:01:11,474 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for health
2025-06-20 15:01:11,553 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for health
2025-06-20 15:01:11,561 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for health
2025-06-20 15:02:12,779 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for health
2025-06-20 15:02:12,810 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for health
2025-06-20 15:02:12,829 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for health
2025-06-20 15:02:12,856 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for health
2025-06-20 15:02:12,926 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_schedule_assignment.shift_schedule_assignment.process_auto_shift_creation because it was found in queue for health
2025-06-20 15:02:12,945 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for health
2025-06-20 15:02:12,974 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for health
2025-06-20 15:02:12,977 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for health
2025-06-20 15:02:13,005 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for health
2025-06-20 15:03:14,300 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_schedule_assignment.shift_schedule_assignment.process_auto_shift_creation because it was found in queue for health
2025-06-20 15:03:14,317 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for health
2025-06-20 15:03:14,325 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for health
2025-06-20 15:03:14,374 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for health
2025-06-20 15:03:14,438 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for health
2025-06-20 15:03:14,460 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for health
2025-06-20 15:03:14,522 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for health
2025-06-20 15:03:14,528 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for health
2025-06-20 15:03:14,551 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for health
2025-06-20 15:04:15,511 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for health
2025-06-20 15:04:15,576 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for health
2025-06-20 15:04:15,611 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_schedule_assignment.shift_schedule_assignment.process_auto_shift_creation because it was found in queue for health
2025-06-20 15:04:15,632 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for health
2025-06-20 15:04:15,658 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for health
2025-06-20 15:04:15,672 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for health
2025-06-20 15:04:15,687 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for health
2025-06-20 15:04:15,734 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for health
2025-06-20 15:04:15,761 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for health
2025-06-20 15:05:16,126 ERROR scheduler Skipped queueing healthcare.healthcare.doctype.patient_appointment.patient_appointment.send_appointment_reminder because it was found in queue for health
2025-06-20 15:05:16,150 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for health
2025-06-20 15:05:16,157 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for health
2025-06-20 15:05:16,189 ERROR scheduler Skipped queueing frappe.monitor.flush because it was found in queue for health
2025-06-20 15:05:16,223 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for health
2025-06-20 15:05:16,238 ERROR scheduler Skipped queueing frappe.email.queue.flush because it was found in queue for health
2025-06-20 15:05:16,312 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_schedule_assignment.shift_schedule_assignment.process_auto_shift_creation because it was found in queue for health
2025-06-20 15:05:16,348 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for health
2025-06-20 15:05:16,354 ERROR scheduler Skipped queueing payments.payment_gateways.doctype.razorpay_settings.razorpay_settings.capture_payment because it was found in queue for health
2025-06-20 15:05:16,410 ERROR scheduler Skipped queueing hrms.hr.doctype.interview.interview.send_interview_reminder because it was found in queue for health
2025-06-20 15:05:16,472 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for health
2025-06-20 15:05:16,582 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for health
2025-06-20 15:05:16,587 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for health
2025-06-20 15:05:16,635 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for health
2025-06-20 15:05:16,662 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_calendar.google_calendar.sync because it was found in queue for health
2025-06-20 16:01:00,689 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for health
2025-06-20 16:01:00,691 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.update_last_sync_of_checkin because it was found in queue for health
2025-06-20 16:01:00,707 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for health
2025-06-20 16:01:00,710 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_schedule_assignment.shift_schedule_assignment.process_auto_shift_creation because it was found in queue for health
2025-06-20 16:01:00,712 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for health
2025-06-20 16:01:00,715 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for health
2025-06-20 16:01:00,718 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.process_auto_attendance_for_all_shifts because it was found in queue for health
2025-06-20 16:01:00,724 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for health
2025-06-20 16:01:00,735 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for health
2025-06-20 16:01:00,739 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for health
2025-06-20 16:01:00,744 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for health
2025-06-20 16:01:00,752 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for health
2025-06-20 16:01:00,758 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for health
2025-06-20 16:02:01,186 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for health
2025-06-20 16:02:01,193 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for health
2025-06-20 16:02:01,202 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for health
2025-06-20 16:02:01,207 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for health
2025-06-20 16:02:01,212 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for health
2025-06-20 16:02:01,219 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_schedule_assignment.shift_schedule_assignment.process_auto_shift_creation because it was found in queue for health
2025-06-20 16:02:01,223 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.process_auto_attendance_for_all_shifts because it was found in queue for health
2025-06-20 16:02:01,238 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.update_last_sync_of_checkin because it was found in queue for health
2025-06-20 16:02:01,240 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for health
2025-06-20 16:02:01,250 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for health
2025-06-20 16:02:01,251 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for health
2025-06-20 16:02:01,257 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for health
2025-06-20 16:02:01,262 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for health
2025-06-20 16:03:01,667 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for health
2025-06-20 16:03:01,673 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for health
2025-06-20 16:03:01,684 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.update_last_sync_of_checkin because it was found in queue for health
2025-06-20 16:03:01,691 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for health
2025-06-20 16:03:01,711 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for health
2025-06-20 16:03:01,715 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for health
2025-06-20 16:03:01,722 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_schedule_assignment.shift_schedule_assignment.process_auto_shift_creation because it was found in queue for health
2025-06-20 16:03:01,761 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for health
2025-06-20 16:03:01,763 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for health
2025-06-20 16:03:01,774 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for health
2025-06-20 16:03:01,781 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for health
2025-06-20 16:04:01,957 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for health
2025-06-20 16:04:01,973 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for health
2025-06-20 16:04:01,983 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for health
2025-06-20 16:04:01,995 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_schedule_assignment.shift_schedule_assignment.process_auto_shift_creation because it was found in queue for health
2025-06-20 16:04:02,013 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for health
2025-06-20 16:04:02,018 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for health
2025-06-20 16:04:02,027 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for health
2025-06-20 16:04:02,031 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.update_last_sync_of_checkin because it was found in queue for health
2025-06-20 16:04:02,047 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for health
2025-06-20 16:04:02,083 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for health
2025-06-20 16:04:02,087 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for health
2025-06-20 17:01:23,842 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for health
2025-06-20 17:01:23,850 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for health
2025-06-20 17:01:23,859 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for health
2025-06-20 17:01:23,881 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for health
2025-06-20 17:01:23,885 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_schedule_assignment.shift_schedule_assignment.process_auto_shift_creation because it was found in queue for health
2025-06-20 17:01:23,906 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for health
2025-06-20 17:01:23,907 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for health
2025-06-20 17:01:23,916 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for health
2025-06-20 17:01:23,920 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for health
2025-06-20 17:01:23,947 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.update_last_sync_of_checkin because it was found in queue for health
2025-06-20 17:02:24,578 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for health
2025-06-21 22:07:53,557 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.process_auto_attendance_for_all_shifts because it was found in queue for health
2025-06-21 22:07:53,565 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_drive.google_drive.daily_backup because it was found in queue for health
2025-06-21 22:07:53,583 ERROR scheduler Skipped queueing frappe.social.doctype.energy_point_settings.energy_point_settings.allocate_review_points because it was found in queue for health
2025-06-21 22:07:53,587 ERROR scheduler Skipped queueing frappe.deferred_insert.save_to_db because it was found in queue for health
2025-06-21 22:07:53,590 ERROR scheduler Skipped queueing erpnext.projects.doctype.project.project.collect_project_status because it was found in queue for health
2025-06-21 22:07:53,595 ERROR scheduler Skipped queueing frappe.core.doctype.log_settings.log_settings.run_log_clean_up because it was found in queue for health
2025-06-21 22:07:53,602 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for health
2025-06-21 22:07:53,607 ERROR scheduler Skipped queueing hrms.hr.doctype.job_opening.job_opening.close_expired_job_openings because it was found in queue for health
2025-06-21 22:07:53,610 ERROR scheduler Skipped queueing hms_tz.nhif.api.inpatient_record.daily_update_inpatient_occupancies because it was found in queue for health
2025-06-21 22:07:53,613 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_contacts.google_contacts.sync because it was found in queue for health
2025-06-21 22:07:53,617 ERROR scheduler Skipped queueing hms_tz.nhif.nhif_api.verification.get_visit_types because it was found in queue for health
2025-06-21 22:07:53,620 ERROR scheduler Skipped queueing csf_tz.csftz_hooks.material_request.auto_close_material_request because it was found in queue for health
2025-06-21 22:07:53,624 ERROR scheduler Skipped queueing hms_tz.nhif.api.healthcare_utils.set_uninvoiced_so_closed because it was found in queue for health
2025-06-21 22:07:53,628 ERROR scheduler Skipped queueing erpnext.assets.doctype.asset.asset.make_post_gl_entry because it was found in queue for health
2025-06-21 22:07:53,632 ERROR scheduler Skipped queueing hms_tz.nhif.api.healthcare_utils.create_invoiced_items_if_not_created because it was found in queue for health
2025-06-21 22:07:53,635 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for health
2025-06-21 22:07:53,649 ERROR scheduler Skipped queueing erpnext.setup.doctype.company.company.cache_companies_monthly_sales_history because it was found in queue for health
2025-06-21 22:07:53,653 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_schedule_assignment.shift_schedule_assignment.process_auto_shift_creation because it was found in queue for health
2025-06-21 22:07:53,657 ERROR scheduler Skipped queueing erpnext.accounts.doctype.gl_entry.gl_entry.rename_gle_sle_docs because it was found in queue for health
2025-06-21 22:07:53,660 ERROR scheduler Skipped queueing hms_tz.nhif.nhif_api.price_package.get_item_types because it was found in queue for health
2025-06-21 22:07:53,665 ERROR scheduler Skipped queueing hms_tz.nhif.api.healthcare_utils.auto_submit_nhif_patient_claim because it was found in queue for health
2025-06-21 22:07:53,673 ERROR scheduler Skipped queueing erpnext.manufacturing.doctype.bom_update_tool.bom_update_tool.auto_update_latest_price_in_all_boms because it was found in queue for health
2025-06-21 22:07:53,677 ERROR scheduler Skipped queueing hms_tz.nhif.api.healthcare_utils.enqueue_auto_create_nhif_patient_claims because it was found in queue for health
2025-06-21 22:07:53,689 ERROR scheduler Skipped queueing erpnext.selling.doctype.quotation.quotation.set_expired_status because it was found in queue for health
2025-06-21 22:07:53,692 ERROR scheduler Skipped queueing hms_tz.nhif.nhif_api.admission.get_admission_types because it was found in queue for health
2025-06-21 22:07:53,696 ERROR scheduler Skipped queueing hms_tz.nhif.nhif_api.admission.get_room_types because it was found in queue for health
2025-06-21 22:07:53,704 ERROR scheduler Skipped queueing erpnext.accounts.doctype.process_subscription.process_subscription.create_subscription_process because it was found in queue for health
2025-06-21 22:07:53,708 ERROR scheduler Skipped queueing hrms.hr.utils.allocate_earned_leaves because it was found in queue for health
2025-06-21 22:07:53,711 ERROR scheduler Skipped queueing hrms.hr.doctype.daily_work_summary_group.daily_work_summary_group.trigger_emails because it was found in queue for health
2025-06-21 22:07:53,722 ERROR scheduler Skipped queueing erpnext.assets.doctype.asset.depreciation.post_depreciation_entries because it was found in queue for health
2025-06-21 22:07:53,727 ERROR scheduler Skipped queueing hrms.hr.doctype.leave_ledger_entry.leave_ledger_entry.process_expired_allocation because it was found in queue for health
2025-06-21 22:07:53,733 ERROR scheduler Skipped queueing hrms.hr.utils.generate_leave_encashment because it was found in queue for health
2025-06-21 22:07:53,736 ERROR scheduler Skipped queueing erpnext.crm.utils.open_leads_opportunities_based_on_todays_event because it was found in queue for health
2025-06-21 22:07:53,740 ERROR scheduler Skipped queueing frappe.automation.doctype.auto_repeat.auto_repeat.make_auto_repeat_entry because it was found in queue for health
2025-06-21 22:07:53,744 ERROR scheduler Skipped queueing hms_tz.nhif.nhif_api.verification.get_card_verifier because it was found in queue for health
2025-06-21 22:07:53,754 ERROR scheduler Skipped queueing csf_tz.csftz_hooks.additional_salary.generate_additional_salary_records because it was found in queue for health
2025-06-21 22:07:53,758 ERROR scheduler Skipped queueing csf_tz.custom_api.create_delivery_note_for_all_pending_sales_invoice because it was found in queue for health
2025-06-21 22:07:53,761 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for health
2025-06-21 22:07:53,765 ERROR scheduler Skipped queueing frappe.desk.doctype.event.event.send_event_digest because it was found in queue for health
2025-06-21 22:07:53,771 ERROR scheduler Skipped queueing erpnext.setup.doctype.email_digest.email_digest.send because it was found in queue for health
2025-06-21 22:07:53,775 ERROR scheduler Skipped queueing hms_tz.nhif.nhif_api.admission.get_discharge_types because it was found in queue for health
2025-06-21 22:07:53,785 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for health
2025-06-21 22:07:53,797 ERROR scheduler Skipped queueing erpnext.controllers.accounts_controller.update_invoice_status because it was found in queue for health
2025-06-21 22:07:53,801 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for health
2025-06-21 22:07:53,804 ERROR scheduler Skipped queueing propms.auto_custom.statusChangeBeforeLeaseExpire because it was found in queue for health
2025-06-21 22:07:53,810 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for health
2025-06-21 22:07:53,820 ERROR scheduler Skipped queueing erpnext.utilities.doctype.video.video.update_youtube_data because it was found in queue for health
2025-06-21 22:07:53,823 ERROR scheduler Skipped queueing frappe.desk.notifications.clear_notifications because it was found in queue for health
2025-06-21 22:07:53,827 ERROR scheduler Skipped queueing hms_tz.nhif.nhif_api.admission.send_overstay_nofication because it was found in queue for health
2025-06-21 22:07:53,829 ERROR scheduler Skipped queueing hms_tz.nhif.nhif_api.facility.get_facilities because it was found in queue for health
2025-06-21 22:07:53,832 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.update_last_sync_of_checkin because it was found in queue for health
2025-06-21 22:07:53,835 ERROR scheduler Skipped queueing frappe.email.doctype.auto_email_report.auto_email_report.send_daily because it was found in queue for health
2025-06-21 22:07:53,845 ERROR scheduler Skipped queueing erpnext.buying.doctype.supplier_quotation.supplier_quotation.set_expired_status because it was found in queue for health
2025-06-21 22:07:53,847 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.remove_unverified_record because it was found in queue for health
2025-06-21 22:07:53,849 ERROR scheduler Skipped queueing erpnext.crm.doctype.email_campaign.email_campaign.send_email_to_leads_or_contacts because it was found in queue for health
2025-06-21 22:07:53,853 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for health
2025-06-21 22:07:53,855 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for health
2025-06-21 22:07:53,859 ERROR scheduler Skipped queueing hms_tz.nhif.nhif_api.reference.get_points_of_care because it was found in queue for health
2025-06-21 22:07:53,866 ERROR scheduler Skipped queueing frappe.integrations.doctype.dropbox_settings.dropbox_settings.take_backups_daily because it was found in queue for health
2025-06-21 22:07:53,870 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.notify_unreplied because it was found in queue for health
2025-06-21 22:07:53,875 ERROR scheduler Skipped queueing frappe.model.utils.link_count.update_link_count because it was found in queue for health
2025-06-21 22:07:53,880 ERROR scheduler Skipped queueing hms_tz.nhif.api.healthcare_utils.auto_finalize_patient_encounters because it was found in queue for health
2025-06-21 22:07:53,885 ERROR scheduler Skipped queueing erpnext.projects.doctype.project.project.send_project_status_email_to_users because it was found in queue for health
2025-06-21 22:07:53,887 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for health
2025-06-21 22:07:53,889 ERROR scheduler Skipped queueing frappe.integrations.doctype.s3_backup_settings.s3_backup_settings.take_backups_daily because it was found in queue for health
2025-06-21 22:07:53,892 ERROR scheduler Skipped queueing hrms.hr.doctype.daily_work_summary_group.daily_work_summary_group.send_summary because it was found in queue for health
2025-06-21 22:07:53,895 ERROR scheduler Skipped queueing frappe.sessions.clear_expired_sessions because it was found in queue for health
2025-06-21 22:07:53,896 ERROR scheduler Skipped queueing csf_tz.csf_tz.doctype.visibility.visibility.trigger_daily_alerts because it was found in queue for health
2025-06-21 22:07:53,898 ERROR scheduler Skipped queueing erpnext.crm.doctype.opportunity.opportunity.auto_close_opportunity because it was found in queue for health
2025-06-21 22:07:53,900 ERROR scheduler Skipped queueing erpnext.stock.doctype.serial_no.serial_no.update_maintenance_status because it was found in queue for health
2025-06-21 22:08:54,606 ERROR scheduler Skipped queueing frappe.desk.notifications.clear_notifications because it was found in queue for health
2025-06-21 22:08:54,613 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for health
2025-06-21 22:08:54,616 ERROR scheduler Skipped queueing hrms.hr.doctype.daily_work_summary_group.daily_work_summary_group.trigger_emails because it was found in queue for health
2025-06-21 22:08:54,619 ERROR scheduler Skipped queueing erpnext.setup.doctype.email_digest.email_digest.send because it was found in queue for health
2025-06-21 22:08:54,626 ERROR scheduler Skipped queueing erpnext.assets.doctype.asset.asset.make_post_gl_entry because it was found in queue for health
2025-06-21 22:08:54,630 ERROR scheduler Skipped queueing hrms.hr.utils.generate_leave_encashment because it was found in queue for health
2025-06-21 22:08:54,634 ERROR scheduler Skipped queueing hms_tz.nhif.nhif_api.admission.send_overstay_nofication because it was found in queue for health
2025-06-21 22:08:54,637 ERROR scheduler Skipped queueing frappe.desk.doctype.event.event.send_event_digest because it was found in queue for health
2025-06-21 22:08:54,647 ERROR scheduler Skipped queueing frappe.sessions.clear_expired_sessions because it was found in queue for health
2025-06-21 22:08:54,650 ERROR scheduler Skipped queueing hrms.hr.doctype.daily_work_summary_group.daily_work_summary_group.send_summary because it was found in queue for health
2025-06-21 22:08:54,655 ERROR scheduler Skipped queueing frappe.integrations.doctype.dropbox_settings.dropbox_settings.take_backups_daily because it was found in queue for health
2025-06-21 22:08:54,659 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_contacts.google_contacts.sync because it was found in queue for health
2025-06-21 22:08:54,662 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for health
2025-06-21 22:08:54,665 ERROR scheduler Skipped queueing frappe.email.doctype.auto_email_report.auto_email_report.send_daily because it was found in queue for health
2025-06-21 22:08:54,671 ERROR scheduler Skipped queueing hms_tz.nhif.api.healthcare_utils.set_uninvoiced_so_closed because it was found in queue for health
2025-06-21 22:08:54,678 ERROR scheduler Skipped queueing hms_tz.nhif.api.healthcare_utils.auto_finalize_patient_encounters because it was found in queue for health
2025-06-21 22:08:54,684 ERROR scheduler Skipped queueing hms_tz.nhif.nhif_api.reference.get_points_of_care because it was found in queue for health
2025-06-21 22:08:54,689 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.remove_unverified_record because it was found in queue for health
2025-06-21 22:08:54,692 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.update_last_sync_of_checkin because it was found in queue for health
2025-06-21 22:08:54,696 ERROR scheduler Skipped queueing hrms.hr.utils.allocate_earned_leaves because it was found in queue for health
2025-06-21 22:08:54,701 ERROR scheduler Skipped queueing hms_tz.nhif.nhif_api.price_package.get_item_types because it was found in queue for health
2025-06-21 22:08:54,704 ERROR scheduler Skipped queueing erpnext.crm.doctype.email_campaign.email_campaign.send_email_to_leads_or_contacts because it was found in queue for health
2025-06-21 22:08:54,707 ERROR scheduler Skipped queueing csf_tz.csf_tz.doctype.visibility.visibility.trigger_daily_alerts because it was found in queue for health
2025-06-21 22:08:54,713 ERROR scheduler Skipped queueing erpnext.selling.doctype.quotation.quotation.set_expired_status because it was found in queue for health
2025-06-21 22:08:54,725 ERROR scheduler Skipped queueing frappe.deferred_insert.save_to_db because it was found in queue for health
2025-06-21 22:08:54,728 ERROR scheduler Skipped queueing erpnext.crm.utils.open_leads_opportunities_based_on_todays_event because it was found in queue for health
2025-06-21 22:08:54,733 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for health
2025-06-21 22:08:54,736 ERROR scheduler Skipped queueing csf_tz.csftz_hooks.additional_salary.generate_additional_salary_records because it was found in queue for health
2025-06-21 22:08:54,743 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for health
2025-06-21 22:08:54,751 ERROR scheduler Skipped queueing hms_tz.nhif.nhif_api.verification.get_card_verifier because it was found in queue for health
2025-06-21 22:08:54,758 ERROR scheduler Skipped queueing hrms.hr.doctype.job_opening.job_opening.close_expired_job_openings because it was found in queue for health
2025-06-21 22:08:54,762 ERROR scheduler Skipped queueing erpnext.stock.doctype.serial_no.serial_no.update_maintenance_status because it was found in queue for health
2025-06-21 22:08:54,765 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for health
2025-06-21 22:08:54,771 ERROR scheduler Skipped queueing csf_tz.csftz_hooks.material_request.auto_close_material_request because it was found in queue for health
2025-06-21 22:08:54,776 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for health
2025-06-21 22:08:54,779 ERROR scheduler Skipped queueing erpnext.accounts.doctype.process_subscription.process_subscription.create_subscription_process because it was found in queue for health
2025-06-21 22:08:54,783 ERROR scheduler Skipped queueing hms_tz.nhif.api.healthcare_utils.auto_submit_nhif_patient_claim because it was found in queue for health
2025-06-21 22:08:54,799 ERROR scheduler Skipped queueing hms_tz.nhif.nhif_api.verification.get_visit_types because it was found in queue for health
2025-06-21 22:08:54,802 ERROR scheduler Skipped queueing hms_tz.nhif.nhif_api.admission.get_room_types because it was found in queue for health
2025-06-21 22:08:54,806 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for health
2025-06-21 22:08:54,809 ERROR scheduler Skipped queueing erpnext.controllers.accounts_controller.update_invoice_status because it was found in queue for health
2025-06-21 22:08:54,829 ERROR scheduler Skipped queueing erpnext.utilities.doctype.video.video.update_youtube_data because it was found in queue for health
2025-06-21 22:08:54,834 ERROR scheduler Skipped queueing erpnext.setup.doctype.company.company.cache_companies_monthly_sales_history because it was found in queue for health
2025-06-21 22:08:54,851 ERROR scheduler Skipped queueing frappe.integrations.doctype.s3_backup_settings.s3_backup_settings.take_backups_daily because it was found in queue for health
2025-06-21 22:08:54,861 ERROR scheduler Skipped queueing erpnext.projects.doctype.project.project.collect_project_status because it was found in queue for health
2025-06-21 22:08:54,868 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for health
2025-06-21 22:08:54,878 ERROR scheduler Skipped queueing erpnext.assets.doctype.asset.depreciation.post_depreciation_entries because it was found in queue for health
2025-06-21 22:08:54,886 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.notify_unreplied because it was found in queue for health
2025-06-21 22:08:54,894 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for health
2025-06-21 22:08:54,902 ERROR scheduler Skipped queueing hms_tz.nhif.nhif_api.admission.get_discharge_types because it was found in queue for health
2025-06-21 22:08:54,905 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.process_auto_attendance_for_all_shifts because it was found in queue for health
2025-06-21 22:08:54,919 ERROR scheduler Skipped queueing hms_tz.nhif.nhif_api.facility.get_facilities because it was found in queue for health
2025-06-21 22:08:54,929 ERROR scheduler Skipped queueing frappe.automation.doctype.auto_repeat.auto_repeat.make_auto_repeat_entry because it was found in queue for health
2025-06-21 22:08:54,933 ERROR scheduler Skipped queueing erpnext.buying.doctype.supplier_quotation.supplier_quotation.set_expired_status because it was found in queue for health
2025-06-21 22:08:54,937 ERROR scheduler Skipped queueing erpnext.crm.doctype.opportunity.opportunity.auto_close_opportunity because it was found in queue for health
2025-06-21 22:08:54,941 ERROR scheduler Skipped queueing frappe.social.doctype.energy_point_settings.energy_point_settings.allocate_review_points because it was found in queue for health
2025-06-21 22:08:54,949 ERROR scheduler Skipped queueing hrms.hr.doctype.leave_ledger_entry.leave_ledger_entry.process_expired_allocation because it was found in queue for health
2025-06-21 22:08:54,953 ERROR scheduler Skipped queueing erpnext.manufacturing.doctype.bom_update_tool.bom_update_tool.auto_update_latest_price_in_all_boms because it was found in queue for health
2025-06-21 22:08:54,956 ERROR scheduler Skipped queueing csf_tz.custom_api.create_delivery_note_for_all_pending_sales_invoice because it was found in queue for health
2025-06-21 22:08:54,959 ERROR scheduler Skipped queueing propms.auto_custom.statusChangeBeforeLeaseExpire because it was found in queue for health
2025-06-21 22:08:54,968 ERROR scheduler Skipped queueing frappe.core.doctype.log_settings.log_settings.run_log_clean_up because it was found in queue for health
2025-06-21 22:08:54,974 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_drive.google_drive.daily_backup because it was found in queue for health
2025-06-21 22:08:54,978 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_schedule_assignment.shift_schedule_assignment.process_auto_shift_creation because it was found in queue for health
2025-06-21 22:09:55,002 ERROR scheduler Skipped queueing erpnext.accounts.doctype.process_subscription.process_subscription.create_subscription_process because it was found in queue for health
2025-06-21 22:09:55,005 ERROR scheduler Skipped queueing erpnext.crm.utils.open_leads_opportunities_based_on_todays_event because it was found in queue for health
2025-06-21 22:09:55,007 ERROR scheduler Skipped queueing hms_tz.nhif.nhif_api.facility.get_facilities because it was found in queue for health
2025-06-21 22:09:55,012 ERROR scheduler Skipped queueing erpnext.assets.doctype.asset.depreciation.post_depreciation_entries because it was found in queue for health
2025-06-21 22:09:55,016 ERROR scheduler Skipped queueing erpnext.utilities.doctype.video.video.update_youtube_data because it was found in queue for health
2025-06-21 22:09:55,018 ERROR scheduler Skipped queueing frappe.integrations.doctype.dropbox_settings.dropbox_settings.take_backups_daily because it was found in queue for health
2025-06-21 22:09:55,020 ERROR scheduler Skipped queueing healthcare.healthcare.doctype.patient_appointment.patient_appointment.send_appointment_reminder because it was found in queue for health
2025-06-21 22:09:55,024 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for health
2025-06-21 22:09:55,025 ERROR scheduler Skipped queueing payments.payment_gateways.doctype.razorpay_settings.razorpay_settings.capture_payment because it was found in queue for health
2025-06-21 22:09:55,027 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_calendar.google_calendar.sync because it was found in queue for health
2025-06-21 22:09:55,028 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.remove_unverified_record because it was found in queue for health
2025-06-21 22:09:55,029 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for health
2025-06-21 22:09:55,032 ERROR scheduler Skipped queueing csf_tz.csftz_hooks.material_request.auto_close_material_request because it was found in queue for health
2025-06-21 22:09:55,036 ERROR scheduler Skipped queueing erpnext.crm.doctype.email_campaign.email_campaign.send_email_to_leads_or_contacts because it was found in queue for health
2025-06-21 22:09:55,041 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_drive.google_drive.daily_backup because it was found in queue for health
2025-06-21 22:09:55,042 ERROR scheduler Skipped queueing hrms.hr.doctype.daily_work_summary_group.daily_work_summary_group.send_summary because it was found in queue for health
2025-06-21 22:09:55,044 ERROR scheduler Skipped queueing hrms.hr.doctype.job_opening.job_opening.close_expired_job_openings because it was found in queue for health
2025-06-21 22:09:55,047 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for health
2025-06-21 22:09:55,050 ERROR scheduler Skipped queueing frappe.email.doctype.auto_email_report.auto_email_report.send_daily because it was found in queue for health
2025-06-21 22:09:55,051 ERROR scheduler Skipped queueing frappe.core.doctype.log_settings.log_settings.run_log_clean_up because it was found in queue for health
2025-06-21 22:09:55,056 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.process_auto_attendance_for_all_shifts because it was found in queue for health
2025-06-21 22:09:55,061 ERROR scheduler Skipped queueing hms_tz.nhif.nhif_api.admission.get_room_types because it was found in queue for health
2025-06-21 22:09:55,062 ERROR scheduler Skipped queueing erpnext.controllers.accounts_controller.update_invoice_status because it was found in queue for health
2025-06-21 22:09:55,064 ERROR scheduler Skipped queueing erpnext.crm.doctype.opportunity.opportunity.auto_close_opportunity because it was found in queue for health
2025-06-21 22:09:55,068 ERROR scheduler Skipped queueing frappe.automation.doctype.auto_repeat.auto_repeat.make_auto_repeat_entry because it was found in queue for health
2025-06-21 22:09:55,070 ERROR scheduler Skipped queueing erpnext.buying.doctype.supplier_quotation.supplier_quotation.set_expired_status because it was found in queue for health
2025-06-21 22:09:55,072 ERROR scheduler Skipped queueing erpnext.setup.doctype.company.company.cache_companies_monthly_sales_history because it was found in queue for health
2025-06-21 22:09:55,076 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for health
2025-06-21 22:09:55,077 ERROR scheduler Skipped queueing frappe.deferred_insert.save_to_db because it was found in queue for health
2025-06-21 22:09:55,079 ERROR scheduler Skipped queueing hms_tz.nhif.nhif_api.admission.get_discharge_types because it was found in queue for health
2025-06-21 22:09:55,081 ERROR scheduler Skipped queueing hms_tz.nhif.nhif_api.price_package.get_item_types because it was found in queue for health
2025-06-21 22:09:55,082 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_contacts.google_contacts.sync because it was found in queue for health
2025-06-21 22:09:55,089 ERROR scheduler Skipped queueing csf_tz.csftz_hooks.additional_salary.generate_additional_salary_records because it was found in queue for health
2025-06-21 22:09:55,091 ERROR scheduler Skipped queueing hrms.hr.doctype.interview.interview.send_interview_reminder because it was found in queue for health
2025-06-21 22:09:55,097 ERROR scheduler Skipped queueing erpnext.stock.doctype.serial_no.serial_no.update_maintenance_status because it was found in queue for health
2025-06-21 22:09:55,100 ERROR scheduler Skipped queueing erpnext.selling.doctype.quotation.quotation.set_expired_status because it was found in queue for health
2025-06-21 22:09:55,104 ERROR scheduler Skipped queueing hms_tz.nhif.nhif_api.reference.get_points_of_care because it was found in queue for health
2025-06-21 22:09:55,107 ERROR scheduler Skipped queueing hms_tz.nhif.api.healthcare_utils.set_uninvoiced_so_closed because it was found in queue for health
2025-06-21 22:09:55,110 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for health
2025-06-21 22:09:55,111 ERROR scheduler Skipped queueing erpnext.setup.doctype.email_digest.email_digest.send because it was found in queue for health
2025-06-21 22:09:55,113 ERROR scheduler Skipped queueing hrms.hr.utils.allocate_earned_leaves because it was found in queue for health
2025-06-21 22:09:55,114 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.update_last_sync_of_checkin because it was found in queue for health
2025-06-21 22:09:55,118 ERROR scheduler Skipped queueing frappe.desk.doctype.event.event.send_event_digest because it was found in queue for health
2025-06-21 22:09:55,121 ERROR scheduler Skipped queueing frappe.desk.notifications.clear_notifications because it was found in queue for health
2025-06-21 22:09:55,129 ERROR scheduler Skipped queueing frappe.social.doctype.energy_point_settings.energy_point_settings.allocate_review_points because it was found in queue for health
2025-06-21 22:09:55,131 ERROR scheduler Skipped queueing propms.auto_custom.statusChangeBeforeLeaseExpire because it was found in queue for health
2025-06-21 22:09:55,132 ERROR scheduler Skipped queueing frappe.integrations.doctype.s3_backup_settings.s3_backup_settings.take_backups_daily because it was found in queue for health
2025-06-21 22:09:55,144 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for health
2025-06-21 22:09:55,153 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.notify_unreplied because it was found in queue for health
2025-06-21 22:09:55,158 ERROR scheduler Skipped queueing erpnext.assets.doctype.asset.asset.make_post_gl_entry because it was found in queue for health
2025-06-21 22:09:55,160 ERROR scheduler Skipped queueing hrms.hr.doctype.leave_ledger_entry.leave_ledger_entry.process_expired_allocation because it was found in queue for health
2025-06-21 22:09:55,162 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_schedule_assignment.shift_schedule_assignment.process_auto_shift_creation because it was found in queue for health
2025-06-21 22:09:55,165 ERROR scheduler Skipped queueing frappe.email.queue.flush because it was found in queue for health
2025-06-21 22:09:55,168 ERROR scheduler Skipped queueing csf_tz.csf_tz.doctype.visibility.visibility.trigger_daily_alerts because it was found in queue for health
2025-06-21 22:09:55,174 ERROR scheduler Skipped queueing frappe.sessions.clear_expired_sessions because it was found in queue for health
2025-06-21 22:09:55,176 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for health
2025-06-21 22:09:55,178 ERROR scheduler Skipped queueing csf_tz.custom_api.create_delivery_note_for_all_pending_sales_invoice because it was found in queue for health
2025-06-21 22:09:55,179 ERROR scheduler Skipped queueing hrms.hr.utils.generate_leave_encashment because it was found in queue for health
2025-06-21 22:09:55,181 ERROR scheduler Skipped queueing erpnext.manufacturing.doctype.bom_update_tool.bom_update_tool.auto_update_latest_price_in_all_boms because it was found in queue for health
2025-06-21 22:09:55,185 ERROR scheduler Skipped queueing frappe.monitor.flush because it was found in queue for health
2025-06-21 22:09:55,188 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for health
2025-06-21 22:09:55,190 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for health
2025-06-21 22:09:55,192 ERROR scheduler Skipped queueing hms_tz.nhif.api.healthcare_utils.auto_submit_nhif_patient_claim because it was found in queue for health
2025-06-21 22:10:55,708 ERROR scheduler Skipped queueing hms_tz.nhif.nhif_api.price_package.get_item_types because it was found in queue for health
2025-06-21 22:10:55,713 ERROR scheduler Skipped queueing frappe.integrations.doctype.dropbox_settings.dropbox_settings.take_backups_daily because it was found in queue for health
2025-06-21 22:10:55,715 ERROR scheduler Skipped queueing frappe.desk.doctype.event.event.send_event_digest because it was found in queue for health
2025-06-21 22:10:55,716 ERROR scheduler Skipped queueing erpnext.setup.doctype.email_digest.email_digest.send because it was found in queue for health
2025-06-21 22:10:55,719 ERROR scheduler Skipped queueing frappe.social.doctype.energy_point_settings.energy_point_settings.allocate_review_points because it was found in queue for health
2025-06-21 22:10:55,720 ERROR scheduler Skipped queueing hms_tz.nhif.nhif_api.admission.get_room_types because it was found in queue for health
2025-06-21 22:10:55,724 ERROR scheduler Skipped queueing erpnext.crm.doctype.email_campaign.email_campaign.send_email_to_leads_or_contacts because it was found in queue for health
2025-06-21 22:10:55,725 ERROR scheduler Skipped queueing hrms.hr.doctype.daily_work_summary_group.daily_work_summary_group.send_summary because it was found in queue for health
2025-06-21 22:10:55,727 ERROR scheduler Skipped queueing csf_tz.csftz_hooks.additional_salary.generate_additional_salary_records because it was found in queue for health
2025-06-21 22:10:55,728 ERROR scheduler Skipped queueing healthcare.healthcare.doctype.patient_appointment.patient_appointment.send_appointment_reminder because it was found in queue for health
2025-06-21 22:10:55,731 ERROR scheduler Skipped queueing hrms.hr.doctype.job_opening.job_opening.close_expired_job_openings because it was found in queue for health
2025-06-21 22:10:55,732 ERROR scheduler Skipped queueing hrms.hr.doctype.leave_ledger_entry.leave_ledger_entry.process_expired_allocation because it was found in queue for health
2025-06-21 22:10:55,734 ERROR scheduler Skipped queueing hms_tz.nhif.nhif_api.reference.get_points_of_care because it was found in queue for health
2025-06-21 22:10:55,735 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_drive.google_drive.daily_backup because it was found in queue for health
2025-06-21 22:10:55,736 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for health
2025-06-21 22:10:55,737 ERROR scheduler Skipped queueing frappe.automation.doctype.auto_repeat.auto_repeat.make_auto_repeat_entry because it was found in queue for health
2025-06-21 22:10:55,738 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_calendar.google_calendar.sync because it was found in queue for health
2025-06-21 22:10:55,740 ERROR scheduler Skipped queueing erpnext.utilities.doctype.video.video.update_youtube_data because it was found in queue for health
2025-06-21 22:10:55,744 ERROR scheduler Skipped queueing frappe.email.doctype.auto_email_report.auto_email_report.send_daily because it was found in queue for health
2025-06-21 22:10:55,746 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for health
2025-06-21 22:10:55,748 ERROR scheduler Skipped queueing frappe.desk.notifications.clear_notifications because it was found in queue for health
2025-06-21 22:10:55,749 ERROR scheduler Skipped queueing propms.auto_custom.statusChangeBeforeLeaseExpire because it was found in queue for health
2025-06-21 22:10:55,755 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_contacts.google_contacts.sync because it was found in queue for health
2025-06-21 22:10:55,758 ERROR scheduler Skipped queueing erpnext.manufacturing.doctype.bom_update_tool.bom_update_tool.auto_update_latest_price_in_all_boms because it was found in queue for health
2025-06-21 22:10:55,760 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_schedule_assignment.shift_schedule_assignment.process_auto_shift_creation because it was found in queue for health
2025-06-21 22:10:55,761 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for health
2025-06-21 22:10:55,762 ERROR scheduler Skipped queueing hrms.hr.doctype.interview.interview.send_interview_reminder because it was found in queue for health
2025-06-21 22:10:55,764 ERROR scheduler Skipped queueing frappe.deferred_insert.save_to_db because it was found in queue for health
2025-06-21 22:10:55,766 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.update_last_sync_of_checkin because it was found in queue for health
2025-06-21 22:10:55,768 ERROR scheduler Skipped queueing erpnext.crm.doctype.opportunity.opportunity.auto_close_opportunity because it was found in queue for health
2025-06-21 22:10:55,770 ERROR scheduler Skipped queueing hms_tz.nhif.api.healthcare_utils.set_uninvoiced_so_closed because it was found in queue for health
2025-06-21 22:10:55,775 ERROR scheduler Skipped queueing erpnext.crm.utils.open_leads_opportunities_based_on_todays_event because it was found in queue for health
2025-06-21 22:10:55,777 ERROR scheduler Skipped queueing erpnext.stock.doctype.serial_no.serial_no.update_maintenance_status because it was found in queue for health
2025-06-21 22:10:55,778 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.remove_unverified_record because it was found in queue for health
2025-06-21 22:10:55,779 ERROR scheduler Skipped queueing csf_tz.csftz_hooks.material_request.auto_close_material_request because it was found in queue for health
2025-06-21 22:10:55,781 ERROR scheduler Skipped queueing frappe.email.queue.flush because it was found in queue for health
2025-06-21 22:10:55,785 ERROR scheduler Skipped queueing hrms.hr.utils.allocate_earned_leaves because it was found in queue for health
2025-06-21 22:10:55,789 ERROR scheduler Skipped queueing erpnext.controllers.accounts_controller.update_invoice_status because it was found in queue for health
2025-06-21 22:10:55,791 ERROR scheduler Skipped queueing hms_tz.nhif.nhif_api.facility.get_facilities because it was found in queue for health
2025-06-21 22:10:55,793 ERROR scheduler Skipped queueing erpnext.setup.doctype.company.company.cache_companies_monthly_sales_history because it was found in queue for health
2025-06-21 22:10:55,795 ERROR scheduler Skipped queueing erpnext.buying.doctype.supplier_quotation.supplier_quotation.set_expired_status because it was found in queue for health
2025-06-21 22:10:55,797 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for health
2025-06-21 22:10:55,804 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for health
2025-06-21 22:10:55,805 ERROR scheduler Skipped queueing frappe.core.doctype.log_settings.log_settings.run_log_clean_up because it was found in queue for health
2025-06-21 22:10:55,807 ERROR scheduler Skipped queueing hrms.hr.utils.generate_leave_encashment because it was found in queue for health
2025-06-21 22:10:55,809 ERROR scheduler Skipped queueing erpnext.assets.doctype.asset.depreciation.post_depreciation_entries because it was found in queue for health
2025-06-21 22:10:55,812 ERROR scheduler Skipped queueing erpnext.accounts.doctype.process_subscription.process_subscription.create_subscription_process because it was found in queue for health
2025-06-21 22:10:55,813 ERROR scheduler Skipped queueing erpnext.assets.doctype.asset.asset.make_post_gl_entry because it was found in queue for health
2025-06-21 22:10:55,816 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.process_auto_attendance_for_all_shifts because it was found in queue for health
2025-06-21 22:10:55,817 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for health
2025-06-21 22:10:55,820 ERROR scheduler Skipped queueing csf_tz.custom_api.create_delivery_note_for_all_pending_sales_invoice because it was found in queue for health
2025-06-21 22:10:55,821 ERROR scheduler Skipped queueing hms_tz.nhif.nhif_api.admission.get_discharge_types because it was found in queue for health
2025-06-21 22:10:55,823 ERROR scheduler Skipped queueing hms_tz.nhif.api.healthcare_utils.auto_submit_nhif_patient_claim because it was found in queue for health
2025-06-21 22:10:55,824 ERROR scheduler Skipped queueing frappe.monitor.flush because it was found in queue for health
2025-06-21 22:10:55,828 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.notify_unreplied because it was found in queue for health
2025-06-21 22:10:55,831 ERROR scheduler Skipped queueing frappe.sessions.clear_expired_sessions because it was found in queue for health
2025-06-21 22:10:55,832 ERROR scheduler Skipped queueing frappe.integrations.doctype.s3_backup_settings.s3_backup_settings.take_backups_daily because it was found in queue for health
2025-06-21 22:10:55,834 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for health
2025-06-21 22:10:55,835 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for health
2025-06-21 22:10:55,836 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for health
2025-06-21 22:10:55,837 ERROR scheduler Skipped queueing erpnext.selling.doctype.quotation.quotation.set_expired_status because it was found in queue for health
2025-06-21 22:10:55,840 ERROR scheduler Skipped queueing payments.payment_gateways.doctype.razorpay_settings.razorpay_settings.capture_payment because it was found in queue for health
2025-06-21 22:10:55,842 ERROR scheduler Skipped queueing csf_tz.csf_tz.doctype.visibility.visibility.trigger_daily_alerts because it was found in queue for health
2025-06-21 22:11:56,554 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.process_auto_attendance_for_all_shifts because it was found in queue for health
2025-06-21 22:11:56,561 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.update_last_sync_of_checkin because it was found in queue for health
2025-06-21 22:11:56,564 ERROR scheduler Skipped queueing erpnext.setup.doctype.email_digest.email_digest.send because it was found in queue for health
2025-06-21 22:11:56,568 ERROR scheduler Skipped queueing erpnext.crm.utils.open_leads_opportunities_based_on_todays_event because it was found in queue for health
2025-06-21 22:11:56,577 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_drive.google_drive.daily_backup because it was found in queue for health
2025-06-21 22:11:56,582 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for health
2025-06-21 22:11:56,587 ERROR scheduler Skipped queueing frappe.email.queue.flush because it was found in queue for health
2025-06-21 22:11:56,597 ERROR scheduler Skipped queueing erpnext.accounts.doctype.process_subscription.process_subscription.create_subscription_process because it was found in queue for health
2025-06-21 22:11:56,599 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for health
2025-06-21 22:11:56,602 ERROR scheduler Skipped queueing hrms.hr.utils.generate_leave_encashment because it was found in queue for health
2025-06-21 22:11:56,604 ERROR scheduler Skipped queueing frappe.email.doctype.auto_email_report.auto_email_report.send_daily because it was found in queue for health
2025-06-21 22:11:56,606 ERROR scheduler Skipped queueing frappe.monitor.flush because it was found in queue for health
2025-06-21 22:11:56,612 ERROR scheduler Skipped queueing frappe.integrations.doctype.s3_backup_settings.s3_backup_settings.take_backups_daily because it was found in queue for health
2025-06-21 22:11:56,613 ERROR scheduler Skipped queueing healthcare.healthcare.doctype.patient_appointment.patient_appointment.send_appointment_reminder because it was found in queue for health
2025-06-21 22:11:56,620 ERROR scheduler Skipped queueing hrms.hr.doctype.leave_ledger_entry.leave_ledger_entry.process_expired_allocation because it was found in queue for health
2025-06-21 22:11:56,623 ERROR scheduler Skipped queueing hms_tz.nhif.api.healthcare_utils.create_invoiced_items_if_not_created because it was found in queue for health
2025-06-21 22:11:56,625 ERROR scheduler Skipped queueing erpnext.assets.doctype.asset.depreciation.post_depreciation_entries because it was found in queue for health
2025-06-21 22:11:56,626 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.remove_unverified_record because it was found in queue for health
2025-06-21 22:11:56,628 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_calendar.google_calendar.sync because it was found in queue for health
2025-06-21 22:11:56,630 ERROR scheduler Skipped queueing frappe.social.doctype.energy_point_settings.energy_point_settings.allocate_review_points because it was found in queue for health
2025-06-21 22:11:56,632 ERROR scheduler Skipped queueing erpnext.manufacturing.doctype.bom_update_tool.bom_update_tool.auto_update_latest_price_in_all_boms because it was found in queue for health
2025-06-21 22:11:56,634 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for health
2025-06-21 22:11:56,635 ERROR scheduler Skipped queueing frappe.automation.doctype.auto_repeat.auto_repeat.make_auto_repeat_entry because it was found in queue for health
2025-06-21 22:11:56,647 ERROR scheduler Skipped queueing frappe.core.doctype.log_settings.log_settings.run_log_clean_up because it was found in queue for health
2025-06-21 22:11:56,648 ERROR scheduler Skipped queueing hrms.hr.doctype.interview.interview.send_interview_reminder because it was found in queue for health
2025-06-21 22:11:56,653 ERROR scheduler Skipped queueing payments.payment_gateways.doctype.razorpay_settings.razorpay_settings.capture_payment because it was found in queue for health
2025-06-21 22:11:56,656 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for health
2025-06-21 22:11:56,659 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for health
2025-06-21 22:11:56,661 ERROR scheduler Skipped queueing frappe.sessions.clear_expired_sessions because it was found in queue for health
2025-06-21 22:11:56,662 ERROR scheduler Skipped queueing frappe.integrations.doctype.dropbox_settings.dropbox_settings.take_backups_daily because it was found in queue for health
2025-06-21 22:11:56,679 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for health
2025-06-21 22:11:56,686 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for health
2025-06-21 22:11:56,691 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_contacts.google_contacts.sync because it was found in queue for health
2025-06-21 22:11:56,694 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_schedule_assignment.shift_schedule_assignment.process_auto_shift_creation because it was found in queue for health
2025-06-21 22:11:56,701 ERROR scheduler Skipped queueing hrms.hr.utils.allocate_earned_leaves because it was found in queue for health
2025-06-21 22:11:56,710 ERROR scheduler Skipped queueing frappe.desk.notifications.clear_notifications because it was found in queue for health
2025-06-21 22:11:56,715 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for health
2025-06-21 22:11:56,726 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.pull because it was found in queue for health
2025-06-21 22:11:56,733 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for health
2025-06-21 22:12:56,962 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for health
2025-06-21 22:12:56,969 ERROR scheduler Skipped queueing frappe.integrations.doctype.dropbox_settings.dropbox_settings.take_backups_daily because it was found in queue for health
2025-06-21 22:12:56,980 ERROR scheduler Skipped queueing erpnext.accounts.doctype.process_subscription.process_subscription.create_subscription_process because it was found in queue for health
2025-06-21 22:12:56,991 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for health
2025-06-21 22:12:56,995 ERROR scheduler Skipped queueing frappe.social.doctype.energy_point_settings.energy_point_settings.allocate_review_points because it was found in queue for health
2025-06-21 22:12:56,999 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_schedule_assignment.shift_schedule_assignment.process_auto_shift_creation because it was found in queue for health
2025-06-21 22:12:57,010 ERROR scheduler Skipped queueing frappe.integrations.doctype.s3_backup_settings.s3_backup_settings.take_backups_daily because it was found in queue for health
2025-06-21 22:12:57,026 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.process_auto_attendance_for_all_shifts because it was found in queue for health
2025-06-21 22:12:57,031 ERROR scheduler Skipped queueing frappe.core.doctype.log_settings.log_settings.run_log_clean_up because it was found in queue for health
2025-06-21 22:12:57,044 ERROR scheduler Skipped queueing hrms.hr.utils.allocate_earned_leaves because it was found in queue for health
2025-06-21 22:12:57,077 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_contacts.google_contacts.sync because it was found in queue for health
2025-06-21 22:12:57,082 ERROR scheduler Skipped queueing hrms.hr.utils.generate_leave_encashment because it was found in queue for health
2025-06-23 08:54:27,518 ERROR scheduler Skipped queueing frappe.core.doctype.log_settings.log_settings.run_log_clean_up because it was found in queue for health
2025-06-23 08:54:29,005 ERROR scheduler Skipped queueing frappe.social.doctype.energy_point_settings.energy_point_settings.allocate_review_points because it was found in queue for health
2025-06-23 08:54:29,207 ERROR scheduler Skipped queueing frappe.integrations.doctype.s3_backup_settings.s3_backup_settings.take_backups_daily because it was found in queue for health
2025-06-23 08:55:39,549 ERROR scheduler Skipped queueing erpnext.projects.doctype.project.project.hourly_reminder because it was found in queue for health
2025-06-23 08:55:39,554 ERROR scheduler Skipped queueing erpnext.accounts.doctype.process_payment_reconciliation.process_payment_reconciliation.trigger_reconciliation_for_queued_docs because it was found in queue for health
2025-06-23 08:55:39,558 ERROR scheduler Skipped queueing frappe.utils.global_search.sync_global_search because it was found in queue for health
2025-06-23 08:55:39,563 ERROR scheduler Skipped queueing hrms.controllers.employee_reminders.send_work_anniversary_reminders because it was found in queue for health
2025-06-23 08:55:39,568 ERROR scheduler Skipped queueing erpnext.accounts.utils.run_ledger_health_checks because it was found in queue for health
2025-06-23 08:55:39,573 ERROR scheduler Skipped queueing erpnext.crm.doctype.opportunity.opportunity.auto_close_opportunity because it was found in queue for health
2025-06-23 08:55:39,578 ERROR scheduler Skipped queueing hms_tz.nhif.api.healthcare_utils.create_invoiced_items_if_not_created because it was found in queue for health
2025-06-23 08:55:39,583 ERROR scheduler Skipped queueing erpnext.projects.doctype.project.project.update_project_sales_billing because it was found in queue for health
2025-06-23 08:55:39,589 ERROR scheduler Skipped queueing hms_tz.nhif.nhif_api.admission.send_overstay_nofication because it was found in queue for health
2025-06-23 08:55:39,595 ERROR scheduler Skipped queueing hms_tz.nhif.api.healthcare_utils.set_uninvoiced_so_closed because it was found in queue for health
2025-06-23 08:55:39,602 ERROR scheduler Skipped queueing frappe.desk.doctype.changelog_feed.changelog_feed.fetch_changelog_feed because it was found in queue for health
2025-06-23 08:55:39,608 ERROR scheduler Skipped queueing erpnext.buying.doctype.supplier_quotation.supplier_quotation.set_expired_status because it was found in queue for health
2025-06-23 08:55:39,612 ERROR scheduler Skipped queueing erpnext.stock.doctype.serial_no.serial_no.update_maintenance_status because it was found in queue for health
2025-06-23 08:55:39,618 ERROR scheduler Skipped queueing csf_tz.bank_api.reconciliation because it was found in queue for health
2025-06-23 08:55:39,627 ERROR scheduler Skipped queueing frappe.integrations.doctype.s3_backup_settings.s3_backup_settings.take_backups_daily because it was found in queue for health
2025-06-23 08:55:39,639 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_calendar.google_calendar.sync because it was found in queue for health
2025-06-23 08:55:39,648 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.pull because it was found in queue for health
2025-06-23 08:55:39,654 ERROR scheduler Skipped queueing erpnext.projects.doctype.project.project.collect_project_status because it was found in queue for health
2025-06-23 08:55:39,660 ERROR scheduler Skipped queueing csf_tz.csftz_hooks.material_request.auto_close_material_request because it was found in queue for health
2025-06-23 08:55:39,668 ERROR scheduler Skipped queueing frappe.email.doctype.auto_email_report.auto_email_report.send_daily because it was found in queue for health
2025-06-23 08:55:39,675 ERROR scheduler Skipped queueing hms_tz.nhif.api.inpatient_record.daily_update_inpatient_occupancies because it was found in queue for health
2025-06-23 08:55:39,684 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.notify_unreplied because it was found in queue for health
2025-06-23 08:55:39,689 ERROR scheduler Skipped queueing erpnext.quality_management.doctype.quality_review.quality_review.review because it was found in queue for health
2025-06-23 08:55:39,694 ERROR scheduler Skipped queueing erpnext.support.doctype.service_level_agreement.service_level_agreement.check_agreement_status because it was found in queue for health
2025-06-23 08:55:39,699 ERROR scheduler Skipped queueing erpnext.accounts.doctype.process_statement_of_accounts.process_statement_of_accounts.send_auto_email because it was found in queue for health
2025-06-23 08:55:39,707 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.update_last_sync_of_checkin because it was found in queue for health
2025-06-23 08:55:39,716 ERROR scheduler Skipped queueing erpnext.projects.doctype.task.task.set_tasks_as_overdue because it was found in queue for health
2025-06-23 08:55:39,724 ERROR scheduler Skipped queueing hrms.controllers.employee_reminders.send_birthday_reminders because it was found in queue for health
2025-06-23 08:55:39,730 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for health
2025-06-23 08:55:39,736 ERROR scheduler Skipped queueing erpnext.setup.doctype.email_digest.email_digest.send because it was found in queue for health
2025-06-23 08:55:39,748 ERROR scheduler Skipped queueing erpnext.crm.doctype.email_campaign.email_campaign.set_email_campaign_status because it was found in queue for health
2025-06-23 08:55:39,754 ERROR scheduler Skipped queueing frappe.email.doctype.notification.notification.trigger_daily_alerts because it was found in queue for health
2025-06-23 08:55:39,764 ERROR scheduler Skipped queueing erpnext.buying.doctype.supplier_scorecard.supplier_scorecard.refresh_scorecards because it was found in queue for health
2025-06-23 08:55:39,770 ERROR scheduler Skipped queueing hms_tz.nhif.api.healthcare_utils.delete_or_cancel_draft_document because it was found in queue for health
2025-06-23 08:55:39,779 ERROR scheduler Skipped queueing frappe.automation.doctype.reminder.reminder.send_reminders because it was found in queue for health
2025-06-23 08:55:39,791 ERROR scheduler Skipped queueing propms.lease_invoice_schedule.make_lease_invoice_schedule because it was found in queue for health
2025-06-23 08:55:39,797 ERROR scheduler Skipped queueing propms.auto_custom.statusChangeAfterLeaseExpire because it was found in queue for health
2025-06-23 08:55:39,805 ERROR scheduler Skipped queueing erpnext.projects.doctype.project.project.send_project_status_email_to_users because it was found in queue for health
2025-06-23 08:55:39,810 ERROR scheduler Skipped queueing erpnext.accounts.utils.auto_create_exchange_rate_revaluation_weekly because it was found in queue for health
2025-06-23 08:55:39,816 ERROR scheduler Skipped queueing hrms.hr.doctype.interview.interview.send_interview_reminder because it was found in queue for health
2025-06-23 08:55:39,822 ERROR scheduler Skipped queueing hrms.hr.doctype.leave_ledger_entry.leave_ledger_entry.process_expired_allocation because it was found in queue for health
2025-06-23 08:55:39,827 ERROR scheduler Skipped queueing erpnext.stock.reorder_item.reorder_item because it was found in queue for health
2025-06-23 08:55:39,832 ERROR scheduler Skipped queueing csf_tz.csf_tz.doctype.visibility.visibility.trigger_daily_alerts because it was found in queue for health
2025-06-23 08:55:39,838 ERROR scheduler Skipped queueing erpnext.projects.doctype.project.project.project_status_update_reminder because it was found in queue for health
2025-06-23 08:55:39,854 ERROR scheduler Skipped queueing csf_tz.custom_api.create_delivery_note_for_all_pending_sales_invoice because it was found in queue for health
2025-06-23 08:55:39,862 ERROR scheduler Skipped queueing erpnext.accounts.doctype.fiscal_year.fiscal_year.auto_create_fiscal_year because it was found in queue for health
2025-06-23 08:55:39,871 ERROR scheduler Skipped queueing healthcare.healthcare.doctype.fee_validity.fee_validity.update_validity_status because it was found in queue for health
2025-06-23 08:55:39,878 ERROR scheduler Skipped queueing erpnext.utilities.doctype.video.video.update_youtube_data because it was found in queue for health
2025-06-23 08:55:39,884 ERROR scheduler Skipped queueing frappe.email.queue.flush because it was found in queue for health
2025-06-23 08:55:39,891 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for health
2025-06-23 08:55:39,899 ERROR scheduler Skipped queueing csf_tz.custom_api.auto_close_dn because it was found in queue for health
2025-06-23 08:55:39,906 ERROR scheduler Skipped queueing csf_tz.stanbic.sftp.process_download_files because it was found in queue for health
2025-06-23 08:55:39,920 ERROR scheduler Skipped queueing csf_tz.custom_api.make_stock_reconciliation_for_all_pending_material_request because it was found in queue for health
2025-06-23 08:55:39,925 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for health
2025-06-23 08:55:39,931 ERROR scheduler Skipped queueing my_test_hourly because it was found in queue for health
2025-06-23 08:55:39,936 ERROR scheduler Skipped queueing erpnext.erpnext_integrations.doctype.plaid_settings.plaid_settings.automatic_synchronization because it was found in queue for health
2025-06-23 08:55:39,940 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for health
2025-06-23 08:55:39,946 ERROR scheduler Skipped queueing healthcare.healthcare.doctype.patient_appointment.patient_appointment.update_appointment_status because it was found in queue for health
2025-06-23 08:55:39,953 ERROR scheduler Skipped queueing frappe.email.doctype.newsletter.newsletter.send_scheduled_email because it was found in queue for health
2025-06-23 08:55:39,958 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for health
2025-06-23 08:55:39,964 ERROR scheduler Skipped queueing propms.lease_invoice.leaseInvoiceAutoCreate because it was found in queue for health
2025-06-23 08:55:39,972 ERROR scheduler Skipped queueing csf_tz.csftz_hooks.additional_salary.generate_additional_salary_records because it was found in queue for health
2025-06-23 08:55:39,978 ERROR scheduler Skipped queueing hms_tz.nhif.api.healthcare_utils.auto_submit_nhif_patient_claim because it was found in queue for health
2025-06-23 08:55:39,989 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_daily_updates because it was found in queue for health
2025-06-23 08:55:39,995 ERROR scheduler Skipped queueing erpnext.support.doctype.issue.issue.auto_close_tickets because it was found in queue for health
2025-06-23 08:55:40,001 ERROR scheduler Skipped queueing frappe.monitor.flush because it was found in queue for health
2025-06-23 08:55:40,009 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_drive.google_drive.daily_backup because it was found in queue for health
2025-06-23 08:55:40,014 ERROR scheduler Skipped queueing erpnext.crm.doctype.email_campaign.email_campaign.send_email_to_leads_or_contacts because it was found in queue for health
2025-06-23 08:55:40,019 ERROR scheduler Skipped queueing frappe.model.utils.link_count.update_link_count because it was found in queue for health
2025-06-23 08:55:40,028 ERROR scheduler Skipped queueing erpnext.assets.doctype.asset.asset.update_maintenance_status because it was found in queue for health
2025-06-23 08:55:40,034 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for health
2025-06-23 08:55:40,044 ERROR scheduler Skipped queueing frappe.utils.change_log.check_for_update because it was found in queue for health
2025-06-23 08:55:40,049 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for health
2025-06-23 08:55:40,062 ERROR scheduler Skipped queueing erpnext.manufacturing.doctype.bom_update_tool.bom_update_tool.auto_update_latest_price_in_all_boms because it was found in queue for health
2025-06-23 08:55:40,067 ERROR scheduler Skipped queueing erpnext.selling.doctype.quotation.quotation.set_expired_status because it was found in queue for health
2025-06-23 08:55:40,072 ERROR scheduler Skipped queueing hrms.hr.doctype.daily_work_summary_group.daily_work_summary_group.trigger_emails because it was found in queue for health
2025-06-23 08:55:40,080 ERROR scheduler Skipped queueing erpnext.accounts.utils.auto_create_exchange_rate_revaluation_daily because it was found in queue for health
2025-06-23 08:55:40,087 ERROR scheduler Skipped queueing frappe.social.doctype.energy_point_log.energy_point_log.send_weekly_summary because it was found in queue for health
2025-06-23 08:55:40,092 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.remove_unverified_record because it was found in queue for health
