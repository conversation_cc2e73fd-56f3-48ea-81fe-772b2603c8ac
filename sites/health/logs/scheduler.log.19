2025-06-12 08:26:17,484 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.update_last_sync_of_checkin because it was found in queue for health
2025-06-12 08:26:17,485 ERROR scheduler Skipped queueing propms.lease_invoice_schedule.make_lease_invoice_schedule because it was found in queue for health
2025-06-12 08:26:17,485 ERROR scheduler Skipped queueing frappe.social.doctype.energy_point_settings.energy_point_settings.allocate_review_points because it was found in queue for health
2025-06-12 08:26:17,486 ERROR scheduler Skipped queueing frappe.utils.global_search.sync_global_search because it was found in queue for health
2025-06-12 08:26:17,487 ERROR scheduler Skipped queueing erpnext.support.doctype.issue.issue.auto_close_tickets because it was found in queue for health
2025-06-12 08:26:17,488 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for health
2025-06-12 08:26:17,489 ERROR scheduler Skipped queueing payments.payment_gateways.doctype.razorpay_settings.razorpay_settings.capture_payment because it was found in queue for health
2025-06-12 08:26:17,492 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for health
2025-06-12 08:26:17,493 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_calendar.google_calendar.sync because it was found in queue for health
2025-06-12 08:26:17,495 ERROR scheduler Skipped queueing erpnext.manufacturing.doctype.bom_update_tool.bom_update_tool.auto_update_latest_price_in_all_boms because it was found in queue for health
2025-06-12 08:26:17,497 ERROR scheduler Skipped queueing hrms.hr.utils.generate_leave_encashment because it was found in queue for health
2025-06-12 08:26:17,497 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for health
2025-06-12 08:26:17,498 ERROR scheduler Skipped queueing hms_tz.nhif.api.healthcare_utils.auto_finalize_patient_encounters because it was found in queue for health
2025-06-12 08:26:17,499 ERROR scheduler Skipped queueing hrms.hr.doctype.interview.interview.send_interview_reminder because it was found in queue for health
2025-06-12 08:26:17,500 ERROR scheduler Skipped queueing csf_tz.custom_api.create_delivery_note_for_all_pending_sales_invoice because it was found in queue for health
2025-06-12 08:26:17,501 ERROR scheduler Skipped queueing healthcare.healthcare.doctype.fee_validity.fee_validity.update_validity_status because it was found in queue for health
2025-06-12 08:26:17,502 ERROR scheduler Skipped queueing erpnext.projects.doctype.project.project.send_project_status_email_to_users because it was found in queue for health
2025-06-12 08:26:17,502 ERROR scheduler Skipped queueing frappe.core.doctype.log_settings.log_settings.run_log_clean_up because it was found in queue for health
2025-06-12 08:26:17,503 ERROR scheduler Skipped queueing frappe.email.doctype.auto_email_report.auto_email_report.send_daily because it was found in queue for health
2025-06-12 08:26:17,506 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.process_auto_attendance_for_all_shifts because it was found in queue for health
2025-06-12 08:26:17,508 ERROR scheduler Skipped queueing hrms.hr.doctype.leave_ledger_entry.leave_ledger_entry.process_expired_allocation because it was found in queue for health
2025-06-12 08:26:17,509 ERROR scheduler Skipped queueing erpnext.support.doctype.service_level_agreement.service_level_agreement.check_agreement_status because it was found in queue for health
2025-06-12 08:26:17,510 ERROR scheduler Skipped queueing csf_tz.stanbic.sftp.process_download_files because it was found in queue for health
2025-06-12 08:26:17,510 ERROR scheduler Skipped queueing csf_tz.csf_tz.doctype.parking_bill.parking_bill.check_bills_all_vehicles because it was found in queue for health
2025-06-12 08:26:17,511 ERROR scheduler Skipped queueing hms_tz.nhif.api.healthcare_utils.auto_submit_nhif_patient_claim because it was found in queue for health
2025-06-12 08:26:17,512 ERROR scheduler Skipped queueing erpnext.buying.doctype.supplier_quotation.supplier_quotation.set_expired_status because it was found in queue for health
2025-06-12 08:26:17,513 ERROR scheduler Skipped queueing my_test_hourly because it was found in queue for health
2025-06-12 08:26:17,514 ERROR scheduler Skipped queueing frappe.model.utils.link_count.update_link_count because it was found in queue for health
2025-06-12 08:26:17,516 ERROR scheduler Skipped queueing hrms.hr.doctype.daily_work_summary_group.daily_work_summary_group.send_summary because it was found in queue for health
2025-06-12 08:26:17,517 ERROR scheduler Skipped queueing erpnext.buying.doctype.supplier_scorecard.supplier_scorecard.refresh_scorecards because it was found in queue for health
2025-06-12 08:26:17,518 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for health
2025-06-12 08:26:17,519 ERROR scheduler Skipped queueing erpnext.projects.doctype.project.project.update_project_sales_billing because it was found in queue for health
2025-06-12 08:26:17,519 ERROR scheduler Skipped queueing erpnext.accounts.doctype.process_subscription.process_subscription.create_subscription_process because it was found in queue for health
2025-06-12 08:26:17,520 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_drive.google_drive.daily_backup because it was found in queue for health
2025-06-12 08:26:17,521 ERROR scheduler Skipped queueing frappe.desk.doctype.event.event.send_event_digest because it was found in queue for health
2025-06-12 08:26:17,522 ERROR scheduler Skipped queueing erpnext.assets.doctype.asset.depreciation.post_depreciation_entries because it was found in queue for health
2025-06-12 08:26:17,523 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_schedule_assignment.shift_schedule_assignment.process_auto_shift_creation because it was found in queue for health
2025-06-12 08:26:17,523 ERROR scheduler Skipped queueing erpnext.setup.doctype.company.company.cache_companies_monthly_sales_history because it was found in queue for health
2025-06-12 08:26:17,524 ERROR scheduler Skipped queueing erpnext.erpnext_integrations.doctype.plaid_settings.plaid_settings.automatic_synchronization because it was found in queue for health
2025-06-12 08:26:17,525 ERROR scheduler Skipped queueing erpnext.crm.utils.open_leads_opportunities_based_on_todays_event because it was found in queue for health
2025-06-12 08:26:17,527 ERROR scheduler Skipped queueing erpnext.assets.doctype.asset.asset.update_maintenance_status because it was found in queue for health
2025-06-12 08:26:17,528 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for health
2025-06-12 08:26:17,529 ERROR scheduler Skipped queueing erpnext.stock.doctype.serial_no.serial_no.update_maintenance_status because it was found in queue for health
2025-06-12 08:26:17,530 ERROR scheduler Skipped queueing erpnext.setup.doctype.email_digest.email_digest.send because it was found in queue for health
2025-06-12 08:26:17,531 ERROR scheduler Skipped queueing csf_tz.custom_api.auto_close_dn because it was found in queue for health
2025-06-12 08:26:17,532 ERROR scheduler Skipped queueing frappe.email.doctype.newsletter.newsletter.send_scheduled_email because it was found in queue for health
2025-06-12 08:26:17,533 ERROR scheduler Skipped queueing frappe.sessions.clear_expired_sessions because it was found in queue for health
2025-06-12 08:26:17,534 ERROR scheduler Skipped queueing erpnext.accounts.doctype.fiscal_year.fiscal_year.auto_create_fiscal_year because it was found in queue for health
2025-06-12 08:26:17,535 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.remove_unverified_record because it was found in queue for health
2025-06-12 08:26:17,536 ERROR scheduler Skipped queueing frappe.integrations.doctype.dropbox_settings.dropbox_settings.take_backups_daily because it was found in queue for health
2025-06-12 08:26:17,538 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for health
2025-06-12 08:26:17,538 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for health
2025-06-12 08:26:17,540 ERROR scheduler Skipped queueing hrms.hr.doctype.daily_work_summary_group.daily_work_summary_group.trigger_emails because it was found in queue for health
2025-06-12 08:26:17,542 ERROR scheduler Skipped queueing csf_tz.bank_api.reconciliation because it was found in queue for health
2025-06-12 08:26:17,543 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for health
2025-06-12 08:26:17,544 ERROR scheduler Skipped queueing hrms.hr.utils.allocate_earned_leaves because it was found in queue for health
2025-06-12 08:26:17,545 ERROR scheduler Skipped queueing hms_tz.nhif.api.healthcare_utils.set_uninvoiced_so_closed because it was found in queue for health
2025-06-12 08:26:17,546 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for health
2025-06-12 08:26:17,547 ERROR scheduler Skipped queueing hms_tz.nhif.api.healthcare_utils.delete_or_cancel_draft_document because it was found in queue for health
2025-06-12 08:26:17,548 ERROR scheduler Skipped queueing frappe.integrations.doctype.s3_backup_settings.s3_backup_settings.take_backups_daily because it was found in queue for health
2025-06-12 08:26:17,549 ERROR scheduler Skipped queueing hms_tz.nhif.nhif_api.admission.send_overstay_nofication because it was found in queue for health
2025-06-12 08:26:17,550 ERROR scheduler Skipped queueing frappe.desk.notifications.clear_notifications because it was found in queue for health
2025-06-12 08:26:17,551 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.pull because it was found in queue for health
2025-06-12 08:26:17,552 ERROR scheduler Skipped queueing csf_tz.csftz_hooks.additional_salary.generate_additional_salary_records because it was found in queue for health
2025-06-12 08:26:17,553 ERROR scheduler Skipped queueing erpnext.crm.doctype.contract.contract.update_status_for_contracts because it was found in queue for health
2025-06-12 08:26:17,557 ERROR scheduler Skipped queueing hrms.hr.doctype.job_opening.job_opening.close_expired_job_openings because it was found in queue for health
2025-06-12 08:26:17,559 ERROR scheduler Skipped queueing erpnext.accounts.doctype.process_payment_reconciliation.process_payment_reconciliation.trigger_reconciliation_for_queued_docs because it was found in queue for health
2025-06-12 08:26:17,560 ERROR scheduler Skipped queueing frappe.email.doctype.notification.notification.trigger_daily_alerts because it was found in queue for health
2025-06-12 08:26:17,560 ERROR scheduler Skipped queueing erpnext.quality_management.doctype.quality_review.quality_review.review because it was found in queue for health
2025-06-12 08:26:17,561 ERROR scheduler Skipped queueing propms.auto_custom.statusChangeAfterLeaseExpire because it was found in queue for health
2025-06-12 08:26:17,563 ERROR scheduler Skipped queueing erpnext.stock.reorder_item.reorder_item because it was found in queue for health
2025-06-12 08:26:17,563 ERROR scheduler Skipped queueing erpnext.accounts.utils.run_ledger_health_checks because it was found in queue for health
2025-06-12 08:26:17,564 ERROR scheduler Skipped queueing erpnext.projects.doctype.project.project.project_status_update_reminder because it was found in queue for health
2025-06-12 08:26:17,565 ERROR scheduler Skipped queueing hms_tz.nhif.api.healthcare_utils.create_invoiced_items_if_not_created because it was found in queue for health
2025-06-12 08:26:17,566 ERROR scheduler Skipped queueing csf_tz.csftz_hooks.items_revaluation.process_incorrect_balance_qty because it was found in queue for health
2025-06-12 08:27:17,662 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for health
2025-06-12 08:27:17,664 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for health
2025-06-12 08:27:17,668 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_drive.google_drive.daily_backup because it was found in queue for health
2025-06-12 08:27:17,672 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for health
2025-06-12 08:27:17,673 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for health
2025-06-12 08:27:17,676 ERROR scheduler Skipped queueing erpnext.accounts.doctype.process_subscription.process_subscription.create_subscription_process because it was found in queue for health
2025-06-12 08:27:17,680 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for health
2025-06-12 08:27:17,684 ERROR scheduler Skipped queueing frappe.desk.notifications.clear_notifications because it was found in queue for health
2025-06-12 08:27:17,685 ERROR scheduler Skipped queueing erpnext.crm.utils.open_leads_opportunities_based_on_todays_event because it was found in queue for health
2025-06-12 08:27:17,686 ERROR scheduler Skipped queueing frappe.email.doctype.auto_email_report.auto_email_report.send_daily because it was found in queue for health
2025-06-12 08:27:17,688 ERROR scheduler Skipped queueing frappe.integrations.doctype.dropbox_settings.dropbox_settings.take_backups_daily because it was found in queue for health
2025-06-12 08:27:17,688 ERROR scheduler Skipped queueing frappe.integrations.doctype.s3_backup_settings.s3_backup_settings.take_backups_daily because it was found in queue for health
2025-06-12 08:27:17,690 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_schedule_assignment.shift_schedule_assignment.process_auto_shift_creation because it was found in queue for health
2025-06-12 08:27:17,693 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for health
2025-06-12 08:27:17,696 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for health
2025-06-12 08:27:17,696 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.update_last_sync_of_checkin because it was found in queue for health
2025-06-12 08:27:17,698 ERROR scheduler Skipped queueing erpnext.setup.doctype.email_digest.email_digest.send because it was found in queue for health
2025-06-12 08:27:17,699 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.remove_unverified_record because it was found in queue for health
2025-06-12 08:27:17,700 ERROR scheduler Skipped queueing hrms.hr.doctype.leave_ledger_entry.leave_ledger_entry.process_expired_allocation because it was found in queue for health
2025-06-12 08:27:17,704 ERROR scheduler Skipped queueing frappe.sessions.clear_expired_sessions because it was found in queue for health
2025-06-12 08:27:17,706 ERROR scheduler Skipped queueing frappe.core.doctype.log_settings.log_settings.run_log_clean_up because it was found in queue for health
2025-06-12 08:27:17,710 ERROR scheduler Skipped queueing erpnext.manufacturing.doctype.bom_update_tool.bom_update_tool.auto_update_latest_price_in_all_boms because it was found in queue for health
2025-06-12 08:27:17,711 ERROR scheduler Skipped queueing erpnext.assets.doctype.asset.depreciation.post_depreciation_entries because it was found in queue for health
2025-06-12 08:27:17,718 ERROR scheduler Skipped queueing hrms.hr.utils.generate_leave_encashment because it was found in queue for health
2025-06-12 08:27:17,720 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for health
2025-06-12 08:27:17,724 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for health
2025-06-12 08:27:17,724 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_contacts.google_contacts.sync because it was found in queue for health
2025-06-12 08:27:17,726 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.process_auto_attendance_for_all_shifts because it was found in queue for health
2025-06-12 08:27:17,729 ERROR scheduler Skipped queueing frappe.automation.doctype.auto_repeat.auto_repeat.make_auto_repeat_entry because it was found in queue for health
2025-06-12 08:27:17,730 ERROR scheduler Skipped queueing hrms.hr.utils.allocate_earned_leaves because it was found in queue for health
2025-06-12 08:27:17,735 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for health
2025-06-12 08:27:17,737 ERROR scheduler Skipped queueing frappe.social.doctype.energy_point_settings.energy_point_settings.allocate_review_points because it was found in queue for health
2025-06-12 08:27:17,738 ERROR scheduler Skipped queueing propms.auto_custom.statusChangeAfterLeaseExpire because it was found in queue for health
2025-06-12 08:28:17,751 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for health
2025-06-12 08:28:17,752 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.update_last_sync_of_checkin because it was found in queue for health
2025-06-12 08:28:17,756 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for health
2025-06-12 08:28:17,757 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for health
2025-06-12 08:28:17,760 ERROR scheduler Skipped queueing erpnext.crm.utils.open_leads_opportunities_based_on_todays_event because it was found in queue for health
2025-06-12 08:28:17,763 ERROR scheduler Skipped queueing frappe.core.doctype.log_settings.log_settings.run_log_clean_up because it was found in queue for health
2025-06-12 08:28:17,766 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.process_auto_attendance_for_all_shifts because it was found in queue for health
2025-06-12 08:28:17,768 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for health
2025-06-12 08:28:17,769 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_contacts.google_contacts.sync because it was found in queue for health
2025-06-12 08:28:17,771 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_drive.google_drive.daily_backup because it was found in queue for health
2025-06-12 08:28:17,774 ERROR scheduler Skipped queueing frappe.automation.doctype.auto_repeat.auto_repeat.make_auto_repeat_entry because it was found in queue for health
2025-06-12 08:28:17,779 ERROR scheduler Skipped queueing frappe.email.doctype.auto_email_report.auto_email_report.send_daily because it was found in queue for health
2025-06-12 08:28:17,780 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.remove_unverified_record because it was found in queue for health
2025-06-12 08:28:17,783 ERROR scheduler Skipped queueing hrms.hr.doctype.leave_ledger_entry.leave_ledger_entry.process_expired_allocation because it was found in queue for health
2025-06-12 08:28:17,785 ERROR scheduler Skipped queueing hrms.hr.utils.generate_leave_encashment because it was found in queue for health
2025-06-12 08:28:17,789 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_schedule_assignment.shift_schedule_assignment.process_auto_shift_creation because it was found in queue for health
2025-06-12 08:28:17,791 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for health
2025-06-12 08:28:17,795 ERROR scheduler Skipped queueing erpnext.setup.doctype.email_digest.email_digest.send because it was found in queue for health
2025-06-12 08:28:17,797 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for health
2025-06-12 08:28:17,798 ERROR scheduler Skipped queueing propms.auto_custom.statusChangeAfterLeaseExpire because it was found in queue for health
2025-06-12 08:28:17,800 ERROR scheduler Skipped queueing erpnext.accounts.doctype.process_subscription.process_subscription.create_subscription_process because it was found in queue for health
2025-06-12 08:28:17,803 ERROR scheduler Skipped queueing frappe.desk.notifications.clear_notifications because it was found in queue for health
2025-06-12 08:28:17,804 ERROR scheduler Skipped queueing erpnext.manufacturing.doctype.bom_update_tool.bom_update_tool.auto_update_latest_price_in_all_boms because it was found in queue for health
2025-06-12 08:28:17,805 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for health
2025-06-12 08:28:17,806 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for health
2025-06-12 08:28:17,808 ERROR scheduler Skipped queueing hrms.hr.utils.allocate_earned_leaves because it was found in queue for health
2025-06-12 08:28:17,810 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for health
2025-06-12 08:28:17,811 ERROR scheduler Skipped queueing frappe.integrations.doctype.s3_backup_settings.s3_backup_settings.take_backups_daily because it was found in queue for health
2025-06-12 08:28:17,825 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for health
2025-06-12 08:28:17,828 ERROR scheduler Skipped queueing erpnext.assets.doctype.asset.depreciation.post_depreciation_entries because it was found in queue for health
2025-06-12 08:28:17,829 ERROR scheduler Skipped queueing frappe.integrations.doctype.dropbox_settings.dropbox_settings.take_backups_daily because it was found in queue for health
2025-06-12 08:28:17,830 ERROR scheduler Skipped queueing frappe.sessions.clear_expired_sessions because it was found in queue for health
2025-06-12 08:28:17,832 ERROR scheduler Skipped queueing frappe.social.doctype.energy_point_settings.energy_point_settings.allocate_review_points because it was found in queue for health
2025-06-12 08:29:17,922 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for health
2025-06-12 08:29:17,925 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_calendar.google_calendar.sync because it was found in queue for health
2025-06-12 08:29:17,928 ERROR scheduler Skipped queueing payments.payment_gateways.doctype.razorpay_settings.razorpay_settings.capture_payment because it was found in queue for health
2025-06-12 08:29:17,929 ERROR scheduler Skipped queueing healthcare.healthcare.doctype.patient_appointment.patient_appointment.send_appointment_reminder because it was found in queue for health
2025-06-12 08:29:17,933 ERROR scheduler Skipped queueing frappe.integrations.doctype.dropbox_settings.dropbox_settings.take_backups_daily because it was found in queue for health
2025-06-12 08:29:17,934 ERROR scheduler Skipped queueing hrms.hr.doctype.leave_ledger_entry.leave_ledger_entry.process_expired_allocation because it was found in queue for health
2025-06-12 08:29:17,935 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for health
2025-06-12 08:29:17,937 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for health
2025-06-12 08:29:17,940 ERROR scheduler Skipped queueing hrms.hr.utils.allocate_earned_leaves because it was found in queue for health
2025-06-12 08:29:17,943 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.process_auto_attendance_for_all_shifts because it was found in queue for health
2025-06-12 08:29:17,944 ERROR scheduler Skipped queueing frappe.core.doctype.log_settings.log_settings.run_log_clean_up because it was found in queue for health
2025-06-12 08:29:17,945 ERROR scheduler Skipped queueing frappe.desk.notifications.clear_notifications because it was found in queue for health
2025-06-12 08:29:17,947 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for health
2025-06-12 08:29:17,949 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_drive.google_drive.daily_backup because it was found in queue for health
2025-06-12 08:29:17,954 ERROR scheduler Skipped queueing frappe.monitor.flush because it was found in queue for health
2025-06-12 08:29:17,956 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for health
2025-06-12 08:29:17,958 ERROR scheduler Skipped queueing erpnext.crm.utils.open_leads_opportunities_based_on_todays_event because it was found in queue for health
2025-06-12 08:29:17,963 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_schedule_assignment.shift_schedule_assignment.process_auto_shift_creation because it was found in queue for health
2025-06-12 08:29:17,964 ERROR scheduler Skipped queueing erpnext.manufacturing.doctype.bom_update_tool.bom_update_tool.auto_update_latest_price_in_all_boms because it was found in queue for health
2025-06-12 08:29:17,965 ERROR scheduler Skipped queueing frappe.email.queue.flush because it was found in queue for health
2025-06-12 08:29:17,967 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.update_last_sync_of_checkin because it was found in queue for health
2025-06-12 08:29:17,968 ERROR scheduler Skipped queueing hrms.hr.doctype.interview.interview.send_interview_reminder because it was found in queue for health
2025-06-12 08:29:17,969 ERROR scheduler Skipped queueing erpnext.setup.doctype.email_digest.email_digest.send because it was found in queue for health
2025-06-12 08:29:17,971 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for health
2025-06-12 08:29:17,973 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for health
2025-06-12 08:29:17,975 ERROR scheduler Skipped queueing frappe.email.doctype.auto_email_report.auto_email_report.send_daily because it was found in queue for health
2025-06-12 08:29:17,976 ERROR scheduler Skipped queueing frappe.automation.doctype.auto_repeat.auto_repeat.make_auto_repeat_entry because it was found in queue for health
2025-06-12 08:29:17,982 ERROR scheduler Skipped queueing propms.auto_custom.statusChangeAfterLeaseExpire because it was found in queue for health
2025-06-12 08:29:17,984 ERROR scheduler Skipped queueing frappe.sessions.clear_expired_sessions because it was found in queue for health
2025-06-12 08:29:17,986 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_contacts.google_contacts.sync because it was found in queue for health
2025-06-12 08:29:17,989 ERROR scheduler Skipped queueing frappe.social.doctype.energy_point_settings.energy_point_settings.allocate_review_points because it was found in queue for health
2025-06-12 08:29:17,990 ERROR scheduler Skipped queueing hrms.hr.utils.generate_leave_encashment because it was found in queue for health
2025-06-12 08:29:17,991 ERROR scheduler Skipped queueing erpnext.assets.doctype.asset.depreciation.post_depreciation_entries because it was found in queue for health
2025-06-12 08:29:17,992 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for health
2025-06-12 08:29:18,000 ERROR scheduler Skipped queueing erpnext.accounts.doctype.process_subscription.process_subscription.create_subscription_process because it was found in queue for health
2025-06-12 08:29:18,001 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for health
2025-06-12 08:29:18,002 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.remove_unverified_record because it was found in queue for health
2025-06-12 08:29:18,003 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for health
2025-06-12 08:29:18,005 ERROR scheduler Skipped queueing frappe.integrations.doctype.s3_backup_settings.s3_backup_settings.take_backups_daily because it was found in queue for health
2025-06-12 08:30:18,222 ERROR scheduler Skipped queueing frappe.email.queue.flush because it was found in queue for health
2025-06-12 08:30:18,223 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for health
2025-06-12 08:30:18,224 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for health
2025-06-12 08:30:18,225 ERROR scheduler Skipped queueing erpnext.manufacturing.doctype.bom_update_tool.bom_update_tool.auto_update_latest_price_in_all_boms because it was found in queue for health
2025-06-12 08:30:18,231 ERROR scheduler Skipped queueing hrms.hr.utils.allocate_earned_leaves because it was found in queue for health
2025-06-12 08:30:18,232 ERROR scheduler Skipped queueing erpnext.assets.doctype.asset.depreciation.post_depreciation_entries because it was found in queue for health
2025-06-12 08:30:18,234 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for health
2025-06-12 08:30:18,235 ERROR scheduler Skipped queueing healthcare.healthcare.doctype.patient_appointment.patient_appointment.send_appointment_reminder because it was found in queue for health
2025-06-12 08:30:18,238 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.remove_unverified_record because it was found in queue for health
2025-06-12 08:30:18,240 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for health
2025-06-12 08:30:18,241 ERROR scheduler Skipped queueing hrms.hr.doctype.leave_ledger_entry.leave_ledger_entry.process_expired_allocation because it was found in queue for health
2025-06-12 08:30:18,244 ERROR scheduler Skipped queueing hrms.hr.doctype.interview.interview.send_interview_reminder because it was found in queue for health
2025-06-12 08:30:18,247 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for health
2025-06-12 08:30:18,253 ERROR scheduler Skipped queueing hrms.hr.utils.generate_leave_encashment because it was found in queue for health
2025-06-12 08:30:18,256 ERROR scheduler Skipped queueing frappe.integrations.doctype.dropbox_settings.dropbox_settings.take_backups_daily because it was found in queue for health
2025-06-12 08:30:18,260 ERROR scheduler Skipped queueing erpnext.crm.utils.open_leads_opportunities_based_on_todays_event because it was found in queue for health
2025-06-12 08:30:18,262 ERROR scheduler Skipped queueing frappe.integrations.doctype.s3_backup_settings.s3_backup_settings.take_backups_daily because it was found in queue for health
2025-06-12 08:30:18,263 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_drive.google_drive.daily_backup because it was found in queue for health
2025-06-12 08:30:18,266 ERROR scheduler Skipped queueing erpnext.setup.doctype.email_digest.email_digest.send because it was found in queue for health
2025-06-12 08:30:18,267 ERROR scheduler Skipped queueing frappe.core.doctype.log_settings.log_settings.run_log_clean_up because it was found in queue for health
2025-06-12 08:30:18,268 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for health
2025-06-12 08:30:18,269 ERROR scheduler Skipped queueing frappe.email.doctype.auto_email_report.auto_email_report.send_daily because it was found in queue for health
2025-06-12 08:30:18,272 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for health
2025-06-12 08:30:18,274 ERROR scheduler Skipped queueing frappe.automation.doctype.auto_repeat.auto_repeat.make_auto_repeat_entry because it was found in queue for health
2025-06-12 08:30:18,278 ERROR scheduler Skipped queueing frappe.monitor.flush because it was found in queue for health
2025-06-12 08:30:18,279 ERROR scheduler Skipped queueing erpnext.accounts.doctype.process_subscription.process_subscription.create_subscription_process because it was found in queue for health
2025-06-12 08:30:18,284 ERROR scheduler Skipped queueing frappe.sessions.clear_expired_sessions because it was found in queue for health
2025-06-12 08:30:18,285 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_schedule_assignment.shift_schedule_assignment.process_auto_shift_creation because it was found in queue for health
2025-06-12 08:30:18,287 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_contacts.google_contacts.sync because it was found in queue for health
2025-06-12 08:30:18,294 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.process_auto_attendance_for_all_shifts because it was found in queue for health
2025-06-12 08:30:18,298 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for health
2025-06-12 08:30:18,300 ERROR scheduler Skipped queueing frappe.desk.notifications.clear_notifications because it was found in queue for health
2025-06-12 08:30:18,301 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_calendar.google_calendar.sync because it was found in queue for health
2025-06-12 08:30:18,304 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for health
2025-06-12 08:30:18,305 ERROR scheduler Skipped queueing frappe.social.doctype.energy_point_settings.energy_point_settings.allocate_review_points because it was found in queue for health
2025-06-12 08:30:18,306 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for health
2025-06-12 08:30:18,307 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.update_last_sync_of_checkin because it was found in queue for health
2025-06-12 08:30:18,310 ERROR scheduler Skipped queueing payments.payment_gateways.doctype.razorpay_settings.razorpay_settings.capture_payment because it was found in queue for health
2025-06-12 08:31:18,334 ERROR scheduler Skipped queueing frappe.desk.notifications.clear_notifications because it was found in queue for health
2025-06-12 08:31:18,336 ERROR scheduler Skipped queueing frappe.model.utils.link_count.update_link_count because it was found in queue for health
2025-06-12 08:31:18,341 ERROR scheduler Skipped queueing erpnext.manufacturing.doctype.bom_update_log.bom_update_log.resume_bom_cost_update_jobs because it was found in queue for health
2025-06-12 08:31:18,344 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for health
2025-06-12 08:31:18,346 ERROR scheduler Skipped queueing csf_tz.stanbic.sftp.process_download_files because it was found in queue for health
2025-06-12 08:31:18,350 ERROR scheduler Skipped queueing erpnext.crm.utils.open_leads_opportunities_based_on_todays_event because it was found in queue for health
2025-06-12 08:31:18,356 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for health
2025-06-12 08:31:18,357 ERROR scheduler Skipped queueing frappe.integrations.doctype.s3_backup_settings.s3_backup_settings.take_backups_daily because it was found in queue for health
2025-06-12 08:31:18,363 ERROR scheduler Skipped queueing erpnext.accounts.doctype.gl_entry.gl_entry.rename_gle_sle_docs because it was found in queue for health
2025-06-12 08:31:18,366 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for health
2025-06-12 08:31:18,373 ERROR scheduler Skipped queueing frappe.core.doctype.log_settings.log_settings.run_log_clean_up because it was found in queue for health
2025-06-12 08:31:18,375 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.notify_unreplied because it was found in queue for health
2025-06-12 08:31:18,376 ERROR scheduler Skipped queueing frappe.utils.global_search.sync_global_search because it was found in queue for health
2025-06-12 08:31:18,377 ERROR scheduler Skipped queueing frappe.automation.doctype.reminder.reminder.send_reminders because it was found in queue for health
2025-06-12 08:31:18,379 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for health
2025-06-12 08:31:18,381 ERROR scheduler Skipped queueing frappe.automation.doctype.auto_repeat.auto_repeat.make_auto_repeat_entry because it was found in queue for health
2025-06-12 08:31:18,385 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for health
2025-06-12 08:31:18,389 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_contacts.google_contacts.sync because it was found in queue for health
2025-06-12 08:31:18,391 ERROR scheduler Skipped queueing hms_tz.nhif.api.healthcare_utils.create_invoiced_items_if_not_created because it was found in queue for health
2025-06-12 08:31:18,393 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for health
2025-06-12 08:31:18,394 ERROR scheduler Skipped queueing erpnext.accounts.doctype.process_payment_reconciliation.process_payment_reconciliation.trigger_reconciliation_for_queued_docs because it was found in queue for health
2025-06-12 08:31:18,395 ERROR scheduler Skipped queueing hrms.hr.utils.generate_leave_encashment because it was found in queue for health
2025-06-12 08:31:18,396 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.process_auto_attendance_for_all_shifts because it was found in queue for health
2025-06-12 08:31:18,398 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for health
2025-06-12 08:31:18,401 ERROR scheduler Skipped queueing erpnext.assets.doctype.asset.depreciation.post_depreciation_entries because it was found in queue for health
2025-06-12 08:31:18,402 ERROR scheduler Skipped queueing csf_tz.stanbic.sftp.sync_all_stanbank_files because it was found in queue for health
2025-06-12 08:31:18,403 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.remove_unverified_record because it was found in queue for health
2025-06-12 08:31:18,405 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_drive.google_drive.daily_backup because it was found in queue for health
2025-06-12 08:31:18,406 ERROR scheduler Skipped queueing erpnext.manufacturing.doctype.bom_update_tool.bom_update_tool.auto_update_latest_price_in_all_boms because it was found in queue for health
2025-06-12 08:31:18,407 ERROR scheduler Skipped queueing hrms.hr.utils.allocate_earned_leaves because it was found in queue for health
2025-06-12 08:31:18,408 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for health
2025-06-12 08:31:18,423 ERROR scheduler Skipped queueing frappe.social.doctype.energy_point_settings.energy_point_settings.allocate_review_points because it was found in queue for health
2025-06-12 08:31:18,424 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for health
2025-06-12 08:31:18,425 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.update_last_sync_of_checkin because it was found in queue for health
2025-06-12 08:31:18,426 ERROR scheduler Skipped queueing frappe.deferred_insert.save_to_db because it was found in queue for health
2025-06-12 08:31:18,427 ERROR scheduler Skipped queueing erpnext.setup.doctype.email_digest.email_digest.send because it was found in queue for health
2025-06-12 08:31:18,429 ERROR scheduler Skipped queueing frappe.integrations.doctype.dropbox_settings.dropbox_settings.take_backups_daily because it was found in queue for health
2025-06-12 08:31:18,431 ERROR scheduler Skipped queueing erpnext.accounts.doctype.process_subscription.process_subscription.create_subscription_process because it was found in queue for health
2025-06-12 08:31:18,438 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for health
2025-06-12 08:31:18,439 ERROR scheduler Skipped queueing frappe.email.doctype.auto_email_report.auto_email_report.send_daily because it was found in queue for health
2025-06-12 08:31:18,440 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.pull because it was found in queue for health
2025-06-12 08:31:18,441 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_schedule_assignment.shift_schedule_assignment.process_auto_shift_creation because it was found in queue for health
2025-06-12 08:31:18,442 ERROR scheduler Skipped queueing frappe.sessions.clear_expired_sessions because it was found in queue for health
2025-06-12 08:31:18,443 ERROR scheduler Skipped queueing hrms.hr.doctype.leave_ledger_entry.leave_ledger_entry.process_expired_allocation because it was found in queue for health
2025-06-12 08:32:18,646 ERROR scheduler Skipped queueing frappe.desk.notifications.clear_notifications because it was found in queue for health
2025-06-12 08:32:18,652 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for health
2025-06-12 08:32:18,653 ERROR scheduler Skipped queueing hrms.hr.utils.generate_leave_encashment because it was found in queue for health
2025-06-12 08:32:18,657 ERROR scheduler Skipped queueing frappe.sessions.clear_expired_sessions because it was found in queue for health
2025-06-12 08:32:18,661 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.remove_unverified_record because it was found in queue for health
2025-06-12 08:32:18,664 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for health
2025-06-12 08:32:18,666 ERROR scheduler Skipped queueing erpnext.assets.doctype.asset.depreciation.post_depreciation_entries because it was found in queue for health
2025-06-12 08:32:18,669 ERROR scheduler Skipped queueing hrms.hr.doctype.leave_ledger_entry.leave_ledger_entry.process_expired_allocation because it was found in queue for health
2025-06-12 08:32:18,670 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for health
2025-06-12 08:32:18,676 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for health
2025-06-12 08:32:18,678 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_drive.google_drive.daily_backup because it was found in queue for health
2025-06-12 08:32:18,680 ERROR scheduler Skipped queueing erpnext.setup.doctype.email_digest.email_digest.send because it was found in queue for health
2025-06-12 08:32:18,682 ERROR scheduler Skipped queueing hrms.hr.utils.allocate_earned_leaves because it was found in queue for health
2025-06-12 08:32:18,684 ERROR scheduler Skipped queueing frappe.email.doctype.auto_email_report.auto_email_report.send_daily because it was found in queue for health
2025-06-12 08:32:18,701 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for health
2025-06-12 08:32:18,703 ERROR scheduler Skipped queueing frappe.core.doctype.log_settings.log_settings.run_log_clean_up because it was found in queue for health
2025-06-12 08:32:18,704 ERROR scheduler Skipped queueing erpnext.accounts.doctype.process_subscription.process_subscription.create_subscription_process because it was found in queue for health
2025-06-12 08:32:18,708 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_contacts.google_contacts.sync because it was found in queue for health
2025-06-12 08:32:18,717 ERROR scheduler Skipped queueing erpnext.crm.utils.open_leads_opportunities_based_on_todays_event because it was found in queue for health
2025-06-12 08:32:18,718 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for health
2025-06-12 08:32:18,723 ERROR scheduler Skipped queueing frappe.integrations.doctype.s3_backup_settings.s3_backup_settings.take_backups_daily because it was found in queue for health
2025-06-12 08:32:18,725 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for health
2025-06-12 08:32:18,728 ERROR scheduler Skipped queueing frappe.social.doctype.energy_point_settings.energy_point_settings.allocate_review_points because it was found in queue for health
2025-06-12 08:32:18,733 ERROR scheduler Skipped queueing frappe.automation.doctype.auto_repeat.auto_repeat.make_auto_repeat_entry because it was found in queue for health
2025-06-12 08:32:18,734 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for health
2025-06-12 08:32:18,736 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for health
2025-06-12 08:32:18,739 ERROR scheduler Skipped queueing erpnext.manufacturing.doctype.bom_update_tool.bom_update_tool.auto_update_latest_price_in_all_boms because it was found in queue for health
2025-06-12 08:32:18,740 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for health
2025-06-12 08:32:18,742 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.update_last_sync_of_checkin because it was found in queue for health
2025-06-12 08:32:18,750 ERROR scheduler Skipped queueing frappe.integrations.doctype.dropbox_settings.dropbox_settings.take_backups_daily because it was found in queue for health
2025-06-12 08:33:18,771 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for health
2025-06-12 08:33:18,777 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for health
2025-06-12 08:33:18,794 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for health
2025-06-12 08:33:18,823 ERROR scheduler Skipped queueing frappe.integrations.doctype.s3_backup_settings.s3_backup_settings.take_backups_daily because it was found in queue for health
2025-06-12 08:33:18,827 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for health
2025-06-12 08:33:18,829 ERROR scheduler Skipped queueing hrms.hr.utils.generate_leave_encashment because it was found in queue for health
2025-06-12 08:33:18,836 ERROR scheduler Skipped queueing erpnext.crm.utils.open_leads_opportunities_based_on_todays_event because it was found in queue for health
2025-06-12 08:33:18,844 ERROR scheduler Skipped queueing frappe.desk.notifications.clear_notifications because it was found in queue for health
2025-06-12 08:33:18,849 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.update_last_sync_of_checkin because it was found in queue for health
2025-06-12 08:33:18,853 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for health
2025-06-12 08:33:18,860 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_drive.google_drive.daily_backup because it was found in queue for health
2025-06-12 08:33:18,877 ERROR scheduler Skipped queueing frappe.core.doctype.log_settings.log_settings.run_log_clean_up because it was found in queue for health
2025-06-12 08:33:18,883 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for health
2025-06-12 08:33:18,900 ERROR scheduler Skipped queueing frappe.automation.doctype.auto_repeat.auto_repeat.make_auto_repeat_entry because it was found in queue for health
2025-06-12 08:33:18,907 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for health
2025-06-12 08:33:18,911 ERROR scheduler Skipped queueing erpnext.setup.doctype.email_digest.email_digest.send because it was found in queue for health
2025-06-12 08:33:18,920 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for health
2025-06-12 08:33:18,925 ERROR scheduler Skipped queueing frappe.integrations.doctype.dropbox_settings.dropbox_settings.take_backups_daily because it was found in queue for health
2025-06-12 08:33:18,933 ERROR scheduler Skipped queueing erpnext.manufacturing.doctype.bom_update_tool.bom_update_tool.auto_update_latest_price_in_all_boms because it was found in queue for health
2025-06-12 08:33:18,944 ERROR scheduler Skipped queueing erpnext.assets.doctype.asset.depreciation.post_depreciation_entries because it was found in queue for health
2025-06-12 08:34:19,091 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for health
2025-06-12 08:34:19,105 ERROR scheduler Skipped queueing frappe.core.doctype.log_settings.log_settings.run_log_clean_up because it was found in queue for health
2025-06-12 08:34:19,112 ERROR scheduler Skipped queueing frappe.desk.notifications.clear_notifications because it was found in queue for health
2025-06-12 08:34:19,124 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.update_last_sync_of_checkin because it was found in queue for health
2025-06-12 08:34:19,138 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for health
2025-06-12 08:34:19,140 ERROR scheduler Skipped queueing frappe.integrations.doctype.dropbox_settings.dropbox_settings.take_backups_daily because it was found in queue for health
2025-06-12 08:34:19,158 ERROR scheduler Skipped queueing erpnext.assets.doctype.asset.depreciation.post_depreciation_entries because it was found in queue for health
2025-06-12 08:34:19,159 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for health
2025-06-12 08:34:19,163 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for health
2025-06-12 08:34:19,171 ERROR scheduler Skipped queueing frappe.automation.doctype.auto_repeat.auto_repeat.make_auto_repeat_entry because it was found in queue for health
2025-06-12 08:34:19,174 ERROR scheduler Skipped queueing hrms.hr.utils.generate_leave_encashment because it was found in queue for health
2025-06-12 08:35:19,260 ERROR scheduler Skipped queueing frappe.desk.notifications.clear_notifications because it was found in queue for health
2025-06-12 08:35:19,262 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.update_last_sync_of_checkin because it was found in queue for health
2025-06-12 08:35:19,267 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for health
2025-06-12 08:35:19,269 ERROR scheduler Skipped queueing hrms.hr.utils.generate_leave_encashment because it was found in queue for health
2025-06-12 08:35:19,270 ERROR scheduler Skipped queueing frappe.core.doctype.log_settings.log_settings.run_log_clean_up because it was found in queue for health
2025-06-12 08:35:19,274 ERROR scheduler Skipped queueing frappe.integrations.doctype.dropbox_settings.dropbox_settings.take_backups_daily because it was found in queue for health
2025-06-12 08:35:19,296 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for health
2025-06-12 08:35:19,308 ERROR scheduler Skipped queueing frappe.automation.doctype.auto_repeat.auto_repeat.make_auto_repeat_entry because it was found in queue for health
2025-06-12 08:35:19,313 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for health
2025-06-12 08:35:19,317 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for health
2025-06-12 08:35:19,318 ERROR scheduler Skipped queueing erpnext.assets.doctype.asset.depreciation.post_depreciation_entries because it was found in queue for health
2025-06-12 09:01:23,975 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.update_last_sync_of_checkin because it was found in queue for health
2025-06-12 09:01:23,990 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for health
2025-06-12 09:01:24,002 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for health
2025-06-12 09:01:24,007 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for health
2025-06-12 09:01:24,019 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for health
2025-06-12 09:01:24,031 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for health
2025-06-12 09:01:24,034 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for health
2025-06-12 09:01:24,037 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for health
2025-06-12 09:01:24,051 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for health
2025-06-12 09:01:24,074 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_schedule_assignment.shift_schedule_assignment.process_auto_shift_creation because it was found in queue for health
2025-06-12 09:02:24,103 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for health
2025-06-12 09:02:24,106 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for health
2025-06-12 09:02:24,130 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for health
2025-06-12 09:02:24,131 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_schedule_assignment.shift_schedule_assignment.process_auto_shift_creation because it was found in queue for health
2025-06-12 09:02:24,136 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for health
2025-06-12 09:02:24,142 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for health
2025-06-12 09:02:24,153 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for health
2025-06-12 09:02:24,159 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for health
2025-06-12 09:02:24,173 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for health
2025-06-12 09:02:24,182 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.update_last_sync_of_checkin because it was found in queue for health
2025-06-12 10:01:34,458 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for health
2025-06-12 10:01:34,467 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.process_auto_attendance_for_all_shifts because it was found in queue for health
2025-06-12 10:01:34,476 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for health
2025-06-12 10:01:34,477 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for health
2025-06-12 10:01:34,484 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.update_last_sync_of_checkin because it was found in queue for health
2025-06-12 10:01:34,486 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for health
2025-06-12 10:01:34,490 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for health
2025-06-12 10:01:34,493 ERROR scheduler Skipped queueing frappe.monitor.flush because it was found in queue for health
2025-06-12 10:01:34,502 ERROR scheduler Skipped queueing hms_tz.nhif.api.healthcare_utils.create_invoiced_items_if_not_created because it was found in queue for health
2025-06-12 10:01:34,504 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for health
2025-06-12 10:01:34,507 ERROR scheduler Skipped queueing hms_tz.nhif.api.healthcare_utils.set_uninvoiced_so_closed because it was found in queue for health
2025-06-12 10:01:34,514 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for health
2025-06-12 10:01:34,515 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_schedule_assignment.shift_schedule_assignment.process_auto_shift_creation because it was found in queue for health
2025-06-12 10:01:34,520 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for health
2025-06-12 10:01:34,522 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for health
2025-06-12 10:01:34,527 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for health
2025-06-12 10:02:34,743 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for health
2025-06-12 10:02:34,746 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for health
2025-06-12 10:02:34,752 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for health
2025-06-12 10:02:34,761 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for health
2025-06-12 10:02:34,766 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for health
2025-06-12 10:02:34,771 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.process_auto_attendance_for_all_shifts because it was found in queue for health
2025-06-12 10:02:34,772 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_schedule_assignment.shift_schedule_assignment.process_auto_shift_creation because it was found in queue for health
2025-06-12 10:02:34,778 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for health
2025-06-12 10:02:34,782 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for health
2025-06-12 10:02:34,786 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for health
2025-06-12 10:02:34,800 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for health
2025-06-12 10:02:34,805 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for health
2025-06-12 10:02:34,810 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.update_last_sync_of_checkin because it was found in queue for health
2025-06-12 10:03:34,925 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for health
2025-06-12 10:03:34,949 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_schedule_assignment.shift_schedule_assignment.process_auto_shift_creation because it was found in queue for health
2025-06-12 10:03:34,972 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for health
2025-06-12 10:04:35,082 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_schedule_assignment.shift_schedule_assignment.process_auto_shift_creation because it was found in queue for health
2025-06-12 10:04:35,131 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for health
2025-06-12 10:04:35,152 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for health
2025-06-12 11:01:45,183 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for health
2025-06-12 11:01:45,190 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_schedule_assignment.shift_schedule_assignment.process_auto_shift_creation because it was found in queue for health
2025-06-12 11:01:45,208 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for health
2025-06-12 11:01:45,222 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for health
2025-06-12 11:01:45,246 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for health
2025-06-12 11:01:45,254 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for health
2025-06-12 11:01:45,267 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.update_last_sync_of_checkin because it was found in queue for health
2025-06-12 11:01:45,279 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for health
2025-06-12 11:01:45,295 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for health
2025-06-12 11:01:45,325 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for health
2025-06-12 11:02:45,354 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for health
2025-06-12 11:02:45,365 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for health
2025-06-12 11:02:45,389 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.update_last_sync_of_checkin because it was found in queue for health
2025-06-12 11:02:45,392 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for health
2025-06-12 11:02:45,397 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for health
2025-06-12 11:02:45,399 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for health
2025-06-12 11:02:45,413 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for health
2025-06-12 11:02:45,418 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for health
2025-06-12 11:02:45,434 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for health
2025-06-12 11:02:45,447 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_schedule_assignment.shift_schedule_assignment.process_auto_shift_creation because it was found in queue for health
2025-06-12 12:01:56,023 ERROR scheduler Skipped queueing payments.payment_gateways.doctype.razorpay_settings.razorpay_settings.capture_payment because it was found in queue for health
2025-06-12 12:01:56,029 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.pull because it was found in queue for health
2025-06-12 12:01:56,030 ERROR scheduler Skipped queueing erpnext.projects.doctype.project.project.collect_project_status because it was found in queue for health
2025-06-12 12:01:56,033 ERROR scheduler Skipped queueing erpnext.projects.doctype.project.project.project_status_update_reminder because it was found in queue for health
2025-06-12 12:01:56,037 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for health
2025-06-12 12:01:56,043 ERROR scheduler Skipped queueing frappe.automation.doctype.reminder.reminder.send_reminders because it was found in queue for health
2025-06-12 12:01:56,047 ERROR scheduler Skipped queueing propms.lease_invoice.leaseInvoiceAutoCreate because it was found in queue for health
2025-06-12 12:01:56,048 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for health
2025-06-12 12:01:56,051 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_schedule_assignment.shift_schedule_assignment.process_auto_shift_creation because it was found in queue for health
2025-06-12 12:01:56,053 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for health
2025-06-12 12:01:56,056 ERROR scheduler Skipped queueing erpnext.manufacturing.doctype.bom_update_log.bom_update_log.resume_bom_cost_update_jobs because it was found in queue for health
2025-06-12 12:01:56,057 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for health
2025-06-12 12:01:56,063 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.notify_unreplied because it was found in queue for health
2025-06-12 12:01:56,068 ERROR scheduler Skipped queueing hms_tz.nhif.api.healthcare_utils.create_invoiced_items_if_not_created because it was found in queue for health
2025-06-12 12:01:56,076 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.update_last_sync_of_checkin because it was found in queue for health
2025-06-12 12:01:56,080 ERROR scheduler Skipped queueing frappe.email.queue.flush because it was found in queue for health
2025-06-12 12:01:56,082 ERROR scheduler Skipped queueing frappe.utils.global_search.sync_global_search because it was found in queue for health
2025-06-12 12:01:56,094 ERROR scheduler Skipped queueing hms_tz.nhif.api.healthcare_utils.set_uninvoiced_so_closed because it was found in queue for health
2025-06-12 12:01:56,097 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for health
2025-06-12 12:01:56,099 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for health
2025-06-12 12:01:56,100 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for health
2025-06-12 12:01:56,111 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for health
2025-06-12 12:01:56,118 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_calendar.google_calendar.sync because it was found in queue for health
2025-06-12 12:01:56,122 ERROR scheduler Skipped queueing frappe.model.utils.link_count.update_link_count because it was found in queue for health
2025-06-12 12:01:56,123 ERROR scheduler Skipped queueing frappe.monitor.flush because it was found in queue for health
2025-06-12 12:01:56,127 ERROR scheduler Skipped queueing erpnext.erpnext_integrations.doctype.plaid_settings.plaid_settings.automatic_synchronization because it was found in queue for health
2025-06-12 12:01:56,133 ERROR scheduler Skipped queueing healthcare.healthcare.doctype.patient_appointment.patient_appointment.send_appointment_reminder because it was found in queue for health
2025-06-12 12:01:56,134 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.process_auto_attendance_for_all_shifts because it was found in queue for health
2025-06-12 12:01:56,148 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for health
2025-06-12 12:01:56,149 ERROR scheduler Skipped queueing erpnext.projects.doctype.project.project.hourly_reminder because it was found in queue for health
2025-06-12 12:01:56,152 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for health
2025-06-12 12:01:56,154 ERROR scheduler Skipped queueing hrms.hr.doctype.daily_work_summary_group.daily_work_summary_group.trigger_emails because it was found in queue for health
2025-06-12 12:02:56,279 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for health
2025-06-12 12:02:56,283 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for health
2025-06-12 12:02:56,286 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.process_auto_attendance_for_all_shifts because it was found in queue for health
2025-06-12 12:02:56,289 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for health
2025-06-12 12:02:56,291 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for health
2025-06-12 12:02:56,298 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_schedule_assignment.shift_schedule_assignment.process_auto_shift_creation because it was found in queue for health
2025-06-12 12:02:56,302 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for health
2025-06-12 12:02:56,307 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for health
2025-06-12 12:02:56,317 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for health
2025-06-12 12:02:56,326 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for health
2025-06-12 12:02:56,327 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.update_last_sync_of_checkin because it was found in queue for health
2025-06-12 12:02:56,336 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for health
2025-06-12 12:02:56,357 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for health
2025-06-12 12:03:56,464 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for health
2025-06-12 12:03:56,468 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for health
2025-06-12 12:03:56,483 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for health
2025-06-12 12:03:56,484 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for health
2025-06-12 12:03:56,492 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.update_last_sync_of_checkin because it was found in queue for health
2025-06-12 12:03:56,516 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_schedule_assignment.shift_schedule_assignment.process_auto_shift_creation because it was found in queue for health
2025-06-12 12:03:56,520 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for health
2025-06-12 12:03:56,533 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for health
2025-06-12 12:03:56,535 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for health
2025-06-12 12:03:56,539 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for health
2025-06-12 12:03:56,551 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for health
2025-06-12 12:03:56,553 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.process_auto_attendance_for_all_shifts because it was found in queue for health
2025-06-12 12:03:56,558 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for health
2025-06-12 12:04:56,762 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for health
2025-06-12 12:04:56,775 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for health
2025-06-12 12:04:56,794 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for health
2025-06-12 12:04:56,808 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for health
2025-06-12 12:04:56,816 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for health
2025-06-12 12:04:56,836 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_schedule_assignment.shift_schedule_assignment.process_auto_shift_creation because it was found in queue for health
2025-06-12 12:05:56,969 ERROR scheduler Skipped queueing healthcare.healthcare.doctype.patient_appointment.patient_appointment.send_appointment_reminder because it was found in queue for health
2025-06-12 12:05:56,970 ERROR scheduler Skipped queueing hrms.hr.doctype.interview.interview.send_interview_reminder because it was found in queue for health
2025-06-12 12:05:56,983 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for health
2025-06-12 12:05:56,996 ERROR scheduler Skipped queueing frappe.monitor.flush because it was found in queue for health
2025-06-12 12:05:57,002 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_calendar.google_calendar.sync because it was found in queue for health
2025-06-12 12:05:57,012 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_schedule_assignment.shift_schedule_assignment.process_auto_shift_creation because it was found in queue for health
2025-06-12 12:05:57,018 ERROR scheduler Skipped queueing payments.payment_gateways.doctype.razorpay_settings.razorpay_settings.capture_payment because it was found in queue for health
2025-06-12 12:05:57,034 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for health
2025-06-12 12:05:57,041 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for health
2025-06-12 12:05:57,074 ERROR scheduler Skipped queueing frappe.email.queue.flush because it was found in queue for health
2025-06-12 12:05:57,078 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for health
2025-06-12 12:05:57,081 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for health
2025-06-12 13:01:06,918 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.update_last_sync_of_checkin because it was found in queue for health
2025-06-12 13:01:06,942 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for health
2025-06-12 13:01:06,968 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_schedule_assignment.shift_schedule_assignment.process_auto_shift_creation because it was found in queue for health
2025-06-12 13:01:06,977 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for health
2025-06-12 13:01:06,985 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for health
2025-06-12 13:02:07,012 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for health
2025-06-12 13:02:07,037 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.update_last_sync_of_checkin because it was found in queue for health
2025-06-12 13:02:07,052 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_schedule_assignment.shift_schedule_assignment.process_auto_shift_creation because it was found in queue for health
2025-06-12 13:02:07,075 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for health
2025-06-12 13:02:07,085 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for health
2025-06-12 14:01:17,365 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for health
2025-06-12 14:01:17,369 ERROR scheduler Skipped queueing hrms.hr.doctype.interview.interview.send_interview_reminder because it was found in queue for health
2025-06-12 14:01:17,370 ERROR scheduler Skipped queueing frappe.automation.doctype.reminder.reminder.send_reminders because it was found in queue for health
2025-06-12 14:01:17,376 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for health
2025-06-12 14:01:17,378 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for health
2025-06-12 14:01:17,383 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for health
2025-06-12 14:01:17,390 ERROR scheduler Skipped queueing erpnext.accounts.doctype.process_payment_reconciliation.process_payment_reconciliation.trigger_reconciliation_for_queued_docs because it was found in queue for health
2025-06-12 14:01:17,394 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for health
2025-06-12 14:01:17,399 ERROR scheduler Skipped queueing erpnext.manufacturing.doctype.bom_update_log.bom_update_log.resume_bom_cost_update_jobs because it was found in queue for health
2025-06-12 14:01:17,401 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for health
2025-06-12 14:01:17,402 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.process_auto_attendance_for_all_shifts because it was found in queue for health
2025-06-12 14:01:17,412 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for health
2025-06-12 14:01:17,417 ERROR scheduler Skipped queueing csf_tz.stanbic.sftp.process_download_files because it was found in queue for health
2025-06-12 14:01:17,418 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for health
2025-06-12 14:01:17,425 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.update_last_sync_of_checkin because it was found in queue for health
2025-06-12 14:01:17,432 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_schedule_assignment.shift_schedule_assignment.process_auto_shift_creation because it was found in queue for health
2025-06-12 14:01:17,436 ERROR scheduler Skipped queueing frappe.monitor.flush because it was found in queue for health
2025-06-12 14:01:17,440 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for health
2025-06-12 14:02:17,570 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for health
2025-06-12 14:02:17,576 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for health
2025-06-12 14:02:17,592 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.update_last_sync_of_checkin because it was found in queue for health
2025-06-12 14:02:17,597 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for health
2025-06-12 14:02:17,601 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for health
2025-06-12 14:02:17,604 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for health
2025-06-12 14:02:17,612 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for health
2025-06-12 14:02:17,613 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for health
2025-06-12 14:02:17,618 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.process_auto_attendance_for_all_shifts because it was found in queue for health
2025-06-12 14:02:17,623 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for health
2025-06-12 14:02:17,624 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for health
2025-06-12 14:02:17,640 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_schedule_assignment.shift_schedule_assignment.process_auto_shift_creation because it was found in queue for health
2025-06-12 14:03:17,689 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for health
2025-06-12 14:03:17,718 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_schedule_assignment.shift_schedule_assignment.process_auto_shift_creation because it was found in queue for health
2025-06-12 14:04:17,928 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for health
2025-06-12 14:04:17,930 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_schedule_assignment.shift_schedule_assignment.process_auto_shift_creation because it was found in queue for health
2025-06-12 15:01:27,697 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for health
2025-06-12 15:01:27,701 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_schedule_assignment.shift_schedule_assignment.process_auto_shift_creation because it was found in queue for health
2025-06-12 15:01:27,702 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for health
2025-06-12 15:01:27,704 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for health
2025-06-12 15:01:27,705 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for health
2025-06-12 15:01:27,712 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for health
2025-06-12 15:01:27,722 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for health
2025-06-12 15:01:27,729 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.update_last_sync_of_checkin because it was found in queue for health
2025-06-12 15:01:27,737 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for health
2025-06-12 15:01:27,747 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for health
2025-06-12 15:01:27,752 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for health
2025-06-12 15:01:27,766 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for health
2025-06-12 16:01:29,950 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.update_last_sync_of_checkin because it was found in queue for health
2025-06-12 16:01:29,959 ERROR scheduler Skipped queueing erpnext.projects.doctype.project.project.hourly_reminder because it was found in queue for health
2025-06-12 16:01:29,961 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for health
2025-06-12 16:01:29,962 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for health
2025-06-12 16:01:29,963 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.process_auto_attendance_for_all_shifts because it was found in queue for health
2025-06-12 16:01:29,964 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for health
2025-06-12 16:01:29,967 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for health
2025-06-12 16:01:29,979 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for health
2025-06-12 16:01:29,988 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for health
2025-06-12 16:01:29,989 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_schedule_assignment.shift_schedule_assignment.process_auto_shift_creation because it was found in queue for health
2025-06-12 16:01:30,006 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for health
2025-06-12 16:01:30,008 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for health
2025-06-12 16:01:30,012 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for health
2025-06-12 16:01:30,023 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for health
2025-06-12 16:02:30,194 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for health
2025-06-12 16:02:30,214 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for health
2025-06-12 16:02:30,218 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for health
2025-06-12 16:02:30,225 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for health
2025-06-12 16:02:30,228 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for health
2025-06-12 16:02:30,231 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for health
2025-06-12 16:02:30,233 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for health
2025-06-12 16:02:30,240 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_schedule_assignment.shift_schedule_assignment.process_auto_shift_creation because it was found in queue for health
2025-06-12 16:02:30,242 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.process_auto_attendance_for_all_shifts because it was found in queue for health
2025-06-12 16:02:30,252 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.update_last_sync_of_checkin because it was found in queue for health
2025-06-12 16:02:30,255 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for health
2025-06-12 16:02:30,257 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for health
2025-06-12 16:02:30,267 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for health
2025-06-12 16:03:30,366 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for health
2025-06-12 16:03:30,378 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_schedule_assignment.shift_schedule_assignment.process_auto_shift_creation because it was found in queue for health
2025-06-12 16:03:30,391 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for health
2025-06-12 16:03:30,404 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for health
2025-06-12 16:03:30,409 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for health
2025-06-12 16:03:30,428 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for health
2025-06-12 16:03:30,439 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for health
2025-06-12 16:03:30,442 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for health
2025-06-12 17:01:40,613 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for health
2025-06-12 17:01:40,616 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for health
2025-06-12 17:01:40,635 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for health
2025-06-12 17:01:40,641 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for health
2025-06-12 17:01:40,646 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for health
2025-06-12 17:01:40,656 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for health
2025-06-12 17:01:40,659 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.update_last_sync_of_checkin because it was found in queue for health
2025-06-12 17:01:40,663 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for health
2025-06-12 17:01:40,675 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_schedule_assignment.shift_schedule_assignment.process_auto_shift_creation because it was found in queue for health
2025-06-12 17:01:40,700 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for health
2025-06-12 17:01:40,709 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for health
2025-06-12 18:01:51,123 ERROR scheduler Skipped queueing erpnext.manufacturing.doctype.bom_update_log.bom_update_log.resume_bom_cost_update_jobs because it was found in queue for health
2025-06-12 18:01:51,129 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for health
2025-06-12 18:01:51,133 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_schedule_assignment.shift_schedule_assignment.process_auto_shift_creation because it was found in queue for health
2025-06-12 18:01:51,136 ERROR scheduler Skipped queueing my_test_hourly because it was found in queue for health
2025-06-12 18:01:51,139 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for health
2025-06-12 18:01:51,141 ERROR scheduler Skipped queueing frappe.model.utils.link_count.update_link_count because it was found in queue for health
2025-06-12 18:01:51,144 ERROR scheduler Skipped queueing frappe.email.queue.flush because it was found in queue for health
2025-06-12 18:01:51,145 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_calendar.google_calendar.sync because it was found in queue for health
2025-06-12 18:01:51,149 ERROR scheduler Skipped queueing payments.payment_gateways.doctype.razorpay_settings.razorpay_settings.capture_payment because it was found in queue for health
2025-06-12 18:01:51,156 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.notify_unreplied because it was found in queue for health
2025-06-12 18:01:51,171 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.pull because it was found in queue for health
2025-06-12 18:01:51,180 ERROR scheduler Skipped queueing hms_tz.nhif.api.healthcare_utils.set_uninvoiced_so_closed because it was found in queue for health
2025-06-12 18:01:51,183 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for health
2025-06-12 18:01:51,188 ERROR scheduler Skipped queueing erpnext.accounts.doctype.process_payment_reconciliation.process_payment_reconciliation.trigger_reconciliation_for_queued_docs because it was found in queue for health
2025-06-12 18:01:51,193 ERROR scheduler Skipped queueing erpnext.erpnext_integrations.doctype.plaid_settings.plaid_settings.automatic_synchronization because it was found in queue for health
2025-06-12 18:01:51,199 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for health
2025-06-12 18:01:51,204 ERROR scheduler Skipped queueing healthcare.healthcare.doctype.patient_appointment.patient_appointment.send_appointment_reminder because it was found in queue for health
2025-06-12 18:01:51,209 ERROR scheduler Skipped queueing erpnext.projects.doctype.project.project.hourly_reminder because it was found in queue for health
2025-06-12 18:01:51,214 ERROR scheduler Skipped queueing frappe.monitor.flush because it was found in queue for health
2025-06-12 18:01:51,220 ERROR scheduler Skipped queueing csf_tz.stanbic.sftp.process_download_files because it was found in queue for health
2025-06-12 18:01:51,222 ERROR scheduler Skipped queueing erpnext.projects.doctype.project.project.project_status_update_reminder because it was found in queue for health
2025-06-12 18:01:51,242 ERROR scheduler Skipped queueing csf_tz.csf_tz.doctype.parking_bill.parking_bill.check_bills_all_vehicles because it was found in queue for health
2025-06-12 18:01:51,251 ERROR scheduler Skipped queueing hrms.hr.doctype.interview.interview.send_interview_reminder because it was found in queue for health
