2025-06-13 12:01:25,285 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for health
2025-06-13 12:01:25,288 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for health
2025-06-13 12:01:25,291 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_calendar.google_calendar.sync because it was found in queue for health
2025-06-13 12:01:25,296 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.update_last_sync_of_checkin because it was found in queue for health
2025-06-13 12:01:25,298 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for health
2025-06-13 12:01:25,303 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for health
2025-06-13 12:01:25,313 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for health
2025-06-13 12:02:26,072 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for health
2025-06-13 12:02:26,076 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.process_auto_attendance_for_all_shifts because it was found in queue for health
2025-06-13 12:02:26,079 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for health
2025-06-13 12:02:26,091 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for health
2025-06-13 12:02:26,093 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for health
2025-06-13 12:02:26,094 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for health
2025-06-13 12:02:26,096 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_schedule_assignment.shift_schedule_assignment.process_auto_shift_creation because it was found in queue for health
2025-06-13 12:02:26,111 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.update_last_sync_of_checkin because it was found in queue for health
2025-06-13 12:02:26,139 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for health
2025-06-13 12:02:26,145 ERROR scheduler Skipped queueing hms_tz.nhif.api.healthcare_utils.create_invoiced_items_if_not_created because it was found in queue for health
2025-06-13 12:02:26,148 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.pull because it was found in queue for health
2025-06-13 12:02:26,151 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for health
2025-06-13 12:02:26,160 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for health
2025-06-13 12:02:26,169 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for health
2025-06-13 12:02:26,175 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for health
2025-06-13 12:03:26,574 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_schedule_assignment.shift_schedule_assignment.process_auto_shift_creation because it was found in queue for health
2025-06-13 12:03:26,576 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for health
2025-06-13 12:03:26,579 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.pull because it was found in queue for health
2025-06-13 12:03:26,580 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.process_auto_attendance_for_all_shifts because it was found in queue for health
2025-06-13 12:03:26,591 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for health
2025-06-13 12:03:26,605 ERROR scheduler Skipped queueing hms_tz.nhif.api.healthcare_utils.create_invoiced_items_if_not_created because it was found in queue for health
2025-06-13 12:03:26,606 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.update_last_sync_of_checkin because it was found in queue for health
2025-06-13 12:03:26,616 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for health
2025-06-13 12:03:26,617 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for health
2025-06-13 12:03:26,621 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for health
2025-06-13 12:03:26,628 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for health
2025-06-13 12:03:26,629 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for health
2025-06-13 12:03:26,642 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for health
2025-06-13 12:03:26,653 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for health
2025-06-13 12:03:26,670 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for health
2025-06-13 12:04:26,921 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for health
2025-06-13 12:05:27,654 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for health
2025-06-13 12:05:27,683 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_calendar.google_calendar.sync because it was found in queue for health
2025-06-13 12:05:27,701 ERROR scheduler Skipped queueing frappe.email.queue.flush because it was found in queue for health
2025-06-13 12:05:27,705 ERROR scheduler Skipped queueing healthcare.healthcare.doctype.patient_appointment.patient_appointment.send_appointment_reminder because it was found in queue for health
2025-06-13 12:05:27,711 ERROR scheduler Skipped queueing payments.payment_gateways.doctype.razorpay_settings.razorpay_settings.capture_payment because it was found in queue for health
2025-06-13 12:05:27,716 ERROR scheduler Skipped queueing frappe.monitor.flush because it was found in queue for health
2025-06-13 12:05:27,749 ERROR scheduler Skipped queueing hrms.hr.doctype.interview.interview.send_interview_reminder because it was found in queue for health
2025-06-13 13:01:48,819 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for health
2025-06-13 13:01:48,823 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for health
2025-06-13 13:01:48,906 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for health
2025-06-13 13:02:49,224 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for health
2025-06-13 13:02:49,242 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for health
2025-06-13 13:02:49,259 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for health
2025-06-13 13:02:49,274 ERROR scheduler Skipped queueing hms_tz.nhif.api.healthcare_utils.create_invoiced_items_if_not_created because it was found in queue for health
2025-06-13 13:02:49,300 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.pull because it was found in queue for health
2025-06-13 14:01:09,959 ERROR scheduler Skipped queueing frappe.email.doctype.newsletter.newsletter.send_scheduled_email because it was found in queue for health
2025-06-13 14:01:09,961 ERROR scheduler Skipped queueing frappe.monitor.flush because it was found in queue for health
2025-06-13 14:01:09,963 ERROR scheduler Skipped queueing frappe.email.queue.flush because it was found in queue for health
2025-06-13 14:01:09,965 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for health
2025-06-13 14:01:09,968 ERROR scheduler Skipped queueing frappe.deferred_insert.save_to_db because it was found in queue for health
2025-06-13 14:01:09,970 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for health
2025-06-13 14:01:09,971 ERROR scheduler Skipped queueing frappe.automation.doctype.reminder.reminder.send_reminders because it was found in queue for health
2025-06-13 14:01:09,974 ERROR scheduler Skipped queueing erpnext.erpnext_integrations.doctype.plaid_settings.plaid_settings.automatic_synchronization because it was found in queue for health
2025-06-13 14:01:09,980 ERROR scheduler Skipped queueing csf_tz.stanbic.sftp.sync_all_stanbank_files because it was found in queue for health
2025-06-13 14:01:09,983 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_schedule_assignment.shift_schedule_assignment.process_auto_shift_creation because it was found in queue for health
2025-06-13 14:01:09,986 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for health
2025-06-13 14:01:09,988 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.process_auto_attendance_for_all_shifts because it was found in queue for health
2025-06-13 14:01:09,989 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.notify_unreplied because it was found in queue for health
2025-06-13 14:01:09,991 ERROR scheduler Skipped queueing hrms.hr.doctype.interview.interview.send_interview_reminder because it was found in queue for health
2025-06-13 14:01:09,992 ERROR scheduler Skipped queueing erpnext.projects.doctype.project.project.hourly_reminder because it was found in queue for health
2025-06-13 14:01:09,994 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for health
2025-06-13 14:01:09,996 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.update_last_sync_of_checkin because it was found in queue for health
2025-06-13 14:01:09,998 ERROR scheduler Skipped queueing erpnext.accounts.doctype.process_payment_reconciliation.process_payment_reconciliation.trigger_reconciliation_for_queued_docs because it was found in queue for health
2025-06-13 14:01:09,999 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for health
2025-06-13 14:01:10,001 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for health
2025-06-13 14:01:10,002 ERROR scheduler Skipped queueing hms_tz.nhif.api.healthcare_utils.create_invoiced_items_if_not_created because it was found in queue for health
2025-06-13 14:01:10,003 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for health
2025-06-13 14:01:10,004 ERROR scheduler Skipped queueing erpnext.projects.doctype.project.project.collect_project_status because it was found in queue for health
2025-06-13 14:01:10,008 ERROR scheduler Skipped queueing erpnext.manufacturing.doctype.bom_update_log.bom_update_log.resume_bom_cost_update_jobs because it was found in queue for health
2025-06-13 14:01:10,014 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for health
2025-06-13 14:01:10,016 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for health
2025-06-13 14:01:10,019 ERROR scheduler Skipped queueing healthcare.healthcare.doctype.patient_appointment.patient_appointment.send_appointment_reminder because it was found in queue for health
2025-06-13 14:01:10,026 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for health
2025-06-13 14:01:10,031 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.pull because it was found in queue for health
2025-06-13 14:01:10,036 ERROR scheduler Skipped queueing erpnext.utilities.doctype.video.video.update_youtube_data because it was found in queue for health
2025-06-13 14:02:10,323 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for health
2025-06-13 14:02:10,327 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.update_last_sync_of_checkin because it was found in queue for health
2025-06-13 14:02:10,341 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for health
2025-06-13 14:02:10,343 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for health
2025-06-13 14:02:10,350 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for health
2025-06-13 14:02:10,352 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for health
2025-06-13 14:02:10,356 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for health
2025-06-13 14:02:10,358 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for health
2025-06-13 14:02:10,371 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for health
2025-06-13 14:02:10,374 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.process_auto_attendance_for_all_shifts because it was found in queue for health
2025-06-13 14:02:10,381 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for health
2025-06-13 14:02:10,389 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_schedule_assignment.shift_schedule_assignment.process_auto_shift_creation because it was found in queue for health
2025-06-13 14:02:10,392 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for health
2025-06-13 14:03:10,488 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for health
2025-06-13 14:03:10,499 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for health
2025-06-13 14:03:10,507 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for health
2025-06-13 14:03:10,513 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for health
2025-06-13 14:03:10,540 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for health
2025-06-13 14:03:10,555 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for health
2025-06-13 14:03:10,556 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for health
2025-06-13 14:03:10,557 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for health
2025-06-13 14:03:10,558 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_schedule_assignment.shift_schedule_assignment.process_auto_shift_creation because it was found in queue for health
2025-06-13 15:01:30,958 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for health
2025-06-13 15:01:30,970 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.update_last_sync_of_checkin because it was found in queue for health
2025-06-13 15:01:30,978 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for health
2025-06-13 15:01:31,007 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for health
2025-06-13 15:01:31,013 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for health
2025-06-13 15:02:31,461 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for health
2025-06-13 15:02:31,478 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for health
2025-06-13 15:02:31,490 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for health
2025-06-13 15:02:31,528 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for health
2025-06-13 15:02:31,532 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.update_last_sync_of_checkin because it was found in queue for health
2025-06-13 16:01:51,233 ERROR scheduler Skipped queueing frappe.automation.doctype.reminder.reminder.send_reminders because it was found in queue for health
2025-06-13 16:01:51,239 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for health
2025-06-13 16:01:51,242 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_schedule_assignment.shift_schedule_assignment.process_auto_shift_creation because it was found in queue for health
2025-06-13 16:01:51,258 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for health
2025-06-13 16:01:51,270 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for health
2025-06-13 16:01:51,278 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.process_auto_attendance_for_all_shifts because it was found in queue for health
2025-06-13 16:01:51,283 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for health
2025-06-13 16:01:51,286 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for health
2025-06-13 16:01:51,294 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for health
2025-06-13 16:01:51,295 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.update_last_sync_of_checkin because it was found in queue for health
2025-06-13 16:01:51,299 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for health
2025-06-13 16:01:51,300 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for health
2025-06-13 16:01:51,317 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for health
2025-06-13 16:02:51,561 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for health
2025-06-13 16:02:51,562 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.update_last_sync_of_checkin because it was found in queue for health
2025-06-13 16:02:51,568 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for health
2025-06-13 16:02:51,574 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for health
2025-06-13 16:02:51,577 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_schedule_assignment.shift_schedule_assignment.process_auto_shift_creation because it was found in queue for health
2025-06-13 16:02:51,587 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for health
2025-06-13 16:02:51,589 ERROR scheduler Skipped queueing hms_tz.nhif.api.healthcare_utils.create_invoiced_items_if_not_created because it was found in queue for health
2025-06-13 16:02:51,591 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for health
2025-06-13 16:02:51,603 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for health
2025-06-13 16:02:51,607 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for health
2025-06-13 16:02:51,630 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.process_auto_attendance_for_all_shifts because it was found in queue for health
2025-06-13 16:02:51,631 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for health
2025-06-13 16:02:51,639 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for health
2025-06-13 16:03:51,741 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for health
2025-06-13 16:03:51,759 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for health
2025-06-13 16:03:51,761 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for health
2025-06-13 16:03:51,789 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.update_last_sync_of_checkin because it was found in queue for health
2025-06-13 16:03:51,795 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for health
2025-06-13 16:03:51,799 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for health
2025-06-13 16:03:51,833 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for health
2025-06-13 18:01:31,342 ERROR scheduler Skipped queueing erpnext.projects.doctype.project.project.project_status_update_reminder because it was found in queue for health
2025-06-13 18:01:31,348 ERROR scheduler Skipped queueing erpnext.projects.doctype.project.project.hourly_reminder because it was found in queue for health
2025-06-13 18:01:31,355 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for health
2025-06-13 18:01:31,357 ERROR scheduler Skipped queueing hrms.hr.doctype.daily_work_summary_group.daily_work_summary_group.trigger_emails because it was found in queue for health
2025-06-13 18:01:31,361 ERROR scheduler Skipped queueing frappe.deferred_insert.save_to_db because it was found in queue for health
2025-06-13 18:01:31,366 ERROR scheduler Skipped queueing payments.payment_gateways.doctype.razorpay_settings.razorpay_settings.capture_payment because it was found in queue for health
2025-06-13 18:01:31,367 ERROR scheduler Skipped queueing my_test_hourly because it was found in queue for health
2025-06-13 18:01:31,369 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.process_auto_attendance_for_all_shifts because it was found in queue for health
2025-06-13 18:01:31,373 ERROR scheduler Skipped queueing erpnext.manufacturing.doctype.bom_update_log.bom_update_log.resume_bom_cost_update_jobs because it was found in queue for health
2025-06-13 18:01:31,374 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for health
2025-06-13 18:01:31,376 ERROR scheduler Skipped queueing healthcare.healthcare.doctype.patient_appointment.patient_appointment.send_appointment_reminder because it was found in queue for health
2025-06-13 18:01:31,389 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for health
2025-06-13 18:01:31,394 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for health
2025-06-13 18:01:31,400 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for health
2025-06-13 18:01:31,411 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for health
2025-06-13 18:01:31,417 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for health
2025-06-13 18:01:31,423 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for health
2025-06-13 18:01:31,428 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_schedule_assignment.shift_schedule_assignment.process_auto_shift_creation because it was found in queue for health
2025-06-13 18:01:31,431 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.update_last_sync_of_checkin because it was found in queue for health
2025-06-13 18:01:31,437 ERROR scheduler Skipped queueing frappe.utils.global_search.sync_global_search because it was found in queue for health
2025-06-13 18:01:31,442 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_calendar.google_calendar.sync because it was found in queue for health
2025-06-13 18:01:31,445 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for health
2025-06-13 18:01:31,449 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for health
2025-06-13 18:02:31,891 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_schedule_assignment.shift_schedule_assignment.process_auto_shift_creation because it was found in queue for health
2025-06-13 18:02:31,900 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for health
2025-06-13 18:02:31,906 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.update_last_sync_of_checkin because it was found in queue for health
2025-06-13 18:02:31,910 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for health
2025-06-13 18:02:31,914 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for health
2025-06-13 18:02:31,924 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for health
2025-06-13 18:02:31,934 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for health
2025-06-13 18:02:31,945 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.process_auto_attendance_for_all_shifts because it was found in queue for health
2025-06-13 18:02:31,950 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for health
2025-06-13 18:02:31,965 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for health
2025-06-13 18:02:31,969 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for health
2025-06-13 18:02:31,980 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for health
2025-06-13 18:02:32,006 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for health
2025-06-13 18:03:32,488 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for health
2025-06-13 18:03:32,544 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for health
2025-06-13 18:04:33,068 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for health
2025-06-13 18:04:33,073 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for health
2025-06-13 18:05:33,225 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for health
2025-06-13 18:05:33,229 ERROR scheduler Skipped queueing healthcare.healthcare.doctype.patient_appointment.patient_appointment.send_appointment_reminder because it was found in queue for health
2025-06-13 18:05:33,244 ERROR scheduler Skipped queueing frappe.email.queue.flush because it was found in queue for health
2025-06-13 18:05:33,250 ERROR scheduler Skipped queueing frappe.monitor.flush because it was found in queue for health
2025-06-13 18:05:33,269 ERROR scheduler Skipped queueing payments.payment_gateways.doctype.razorpay_settings.razorpay_settings.capture_payment because it was found in queue for health
2025-06-13 18:05:33,279 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for health
2025-06-13 18:05:33,289 ERROR scheduler Skipped queueing hrms.hr.doctype.interview.interview.send_interview_reminder because it was found in queue for health
2025-06-13 18:05:33,298 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_calendar.google_calendar.sync because it was found in queue for health
2025-06-15 15:56:49,419 ERROR scheduler Skipped queueing frappe.desk.doctype.changelog_feed.changelog_feed.fetch_changelog_feed because it was found in queue for health
2025-06-15 15:56:49,421 ERROR scheduler Skipped queueing frappe.utils.global_search.sync_global_search because it was found in queue for health
2025-06-15 15:56:49,422 ERROR scheduler Skipped queueing erpnext.manufacturing.doctype.bom_update_log.bom_update_log.resume_bom_cost_update_jobs because it was found in queue for health
2025-06-15 15:56:49,422 ERROR scheduler Skipped queueing frappe.social.doctype.energy_point_log.energy_point_log.send_weekly_summary because it was found in queue for health
2025-06-15 15:56:49,424 ERROR scheduler Skipped queueing hms_tz.nhif.api.healthcare_utils.enqueue_auto_create_nhif_patient_claims because it was found in queue for health
2025-06-15 15:56:49,426 ERROR scheduler Skipped queueing frappe.email.doctype.notification.notification.trigger_daily_alerts because it was found in queue for health
2025-06-15 15:56:49,427 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for health
2025-06-15 15:56:49,428 ERROR scheduler Skipped queueing erpnext.crm.doctype.contract.contract.update_status_for_contracts because it was found in queue for health
2025-06-15 15:56:49,429 ERROR scheduler Skipped queueing erpnext.erpnext_integrations.doctype.plaid_settings.plaid_settings.automatic_synchronization because it was found in queue for health
2025-06-15 15:56:49,429 ERROR scheduler Skipped queueing frappe.email.doctype.newsletter.newsletter.send_scheduled_email because it was found in queue for health
2025-06-15 15:56:49,431 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for health
2025-06-15 15:56:49,432 ERROR scheduler Skipped queueing hrms.hr.utils.allocate_earned_leaves because it was found in queue for health
2025-06-15 15:56:49,433 ERROR scheduler Skipped queueing hms_tz.nhif.api.healthcare_utils.auto_submit_nhif_patient_claim because it was found in queue for health
2025-06-15 15:56:49,434 ERROR scheduler Skipped queueing erpnext.accounts.utils.auto_create_exchange_rate_revaluation_weekly because it was found in queue for health
2025-06-15 15:56:49,435 ERROR scheduler Skipped queueing erpnext.setup.doctype.email_digest.email_digest.send because it was found in queue for health
2025-06-15 15:56:49,436 ERROR scheduler Skipped queueing erpnext.accounts.doctype.process_subscription.process_subscription.create_subscription_process because it was found in queue for health
2025-06-15 15:56:49,437 ERROR scheduler Skipped queueing hms_tz.nhif.nhif_api.verification.get_visit_types because it was found in queue for health
2025-06-15 15:56:49,439 ERROR scheduler Skipped queueing hms_tz.nhif.nhif_api.reference.get_points_of_care because it was found in queue for health
2025-06-15 15:56:49,441 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for health
2025-06-15 15:56:49,442 ERROR scheduler Skipped queueing erpnext.crm.doctype.email_campaign.email_campaign.send_email_to_leads_or_contacts because it was found in queue for health
2025-06-15 15:56:49,443 ERROR scheduler Skipped queueing erpnext.accounts.doctype.process_payment_reconciliation.process_payment_reconciliation.trigger_reconciliation_for_queued_docs because it was found in queue for health
2025-06-15 15:56:49,444 ERROR scheduler Skipped queueing erpnext.assets.doctype.asset_maintenance_log.asset_maintenance_log.update_asset_maintenance_log_status because it was found in queue for health
2025-06-15 15:56:49,445 ERROR scheduler Skipped queueing csf_tz.stanbic.sftp.sync_all_stanbank_files because it was found in queue for health
2025-06-15 15:56:49,446 ERROR scheduler Skipped queueing hrms.hr.doctype.job_opening.job_opening.close_expired_job_openings because it was found in queue for health
2025-06-15 15:56:49,447 ERROR scheduler Skipped queueing frappe.sessions.clear_expired_sessions because it was found in queue for health
2025-06-15 15:56:49,449 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for health
2025-06-15 15:56:49,451 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.process_auto_attendance_for_all_shifts because it was found in queue for health
2025-06-15 15:56:49,452 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for health
2025-06-15 15:56:49,453 ERROR scheduler Skipped queueing frappe.desk.notifications.clear_notifications because it was found in queue for health
2025-06-15 15:56:49,453 ERROR scheduler Skipped queueing erpnext.selling.doctype.quotation.quotation.set_expired_status because it was found in queue for health
2025-06-15 15:56:49,455 ERROR scheduler Skipped queueing hrms.hr.doctype.leave_ledger_entry.leave_ledger_entry.process_expired_allocation because it was found in queue for health
2025-06-15 15:56:49,456 ERROR scheduler Skipped queueing frappe.core.doctype.log_settings.log_settings.run_log_clean_up because it was found in queue for health
2025-06-15 15:56:49,457 ERROR scheduler Skipped queueing erpnext.stock.reorder_item.reorder_item because it was found in queue for health
2025-06-15 15:56:49,458 ERROR scheduler Skipped queueing erpnext.manufacturing.doctype.bom_update_tool.bom_update_tool.auto_update_latest_price_in_all_boms because it was found in queue for health
2025-06-15 15:56:49,458 ERROR scheduler Skipped queueing erpnext.accounts.doctype.gl_entry.gl_entry.rename_gle_sle_docs because it was found in queue for health
2025-06-15 15:56:49,459 ERROR scheduler Skipped queueing erpnext.projects.doctype.project.project.send_project_status_email_to_users because it was found in queue for health
2025-06-15 15:56:49,460 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.update_last_sync_of_checkin because it was found in queue for health
2025-06-15 15:56:49,461 ERROR scheduler Skipped queueing erpnext.buying.doctype.supplier_scorecard.supplier_scorecard.refresh_scorecards because it was found in queue for health
2025-06-15 15:56:49,462 ERROR scheduler Skipped queueing erpnext.crm.utils.open_leads_opportunities_based_on_todays_event because it was found in queue for health
2025-06-15 15:56:49,463 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for health
2025-06-15 15:56:49,465 ERROR scheduler Skipped queueing frappe.integrations.doctype.dropbox_settings.dropbox_settings.take_backups_daily because it was found in queue for health
2025-06-15 15:56:49,466 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_schedule_assignment.shift_schedule_assignment.process_auto_shift_creation because it was found in queue for health
2025-06-15 15:56:49,467 ERROR scheduler Skipped queueing hms_tz.nhif.nhif_api.price_package.get_nhif_schemes because it was found in queue for health
2025-06-15 15:56:49,468 ERROR scheduler Skipped queueing erpnext.assets.doctype.asset.depreciation.post_depreciation_entries because it was found in queue for health
2025-06-15 15:56:49,469 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.pull because it was found in queue for health
2025-06-15 15:56:49,470 ERROR scheduler Skipped queueing frappe.deferred_insert.save_to_db because it was found in queue for health
2025-06-15 15:56:49,471 ERROR scheduler Skipped queueing hms_tz.nhif.nhif_api.price_package.get_item_types because it was found in queue for health
2025-06-15 15:56:49,472 ERROR scheduler Skipped queueing hms_tz.nhif.nhif_api.approval.get_approval_services because it was found in queue for health
2025-06-15 15:56:49,473 ERROR scheduler Skipped queueing frappe.automation.doctype.reminder.reminder.send_reminders because it was found in queue for health
2025-06-15 15:56:49,475 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_contacts.google_contacts.sync because it was found in queue for health
2025-06-15 15:56:49,478 ERROR scheduler Skipped queueing erpnext.utilities.doctype.video.video.update_youtube_data because it was found in queue for health
2025-06-15 15:56:49,479 ERROR scheduler Skipped queueing frappe.automation.doctype.auto_repeat.auto_repeat.make_auto_repeat_entry because it was found in queue for health
2025-06-15 15:56:49,479 ERROR scheduler Skipped queueing frappe.social.doctype.energy_point_settings.energy_point_settings.allocate_review_points because it was found in queue for health
2025-06-15 15:56:49,480 ERROR scheduler Skipped queueing hms_tz.nhif.api.healthcare_utils.delete_or_cancel_draft_document because it was found in queue for health
2025-06-15 15:56:49,481 ERROR scheduler Skipped queueing payments.payment_gateways.doctype.razorpay_settings.razorpay_settings.capture_payment because it was found in queue for health
2025-06-15 15:56:49,483 ERROR scheduler Skipped queueing erpnext.projects.doctype.project.project.project_status_update_reminder because it was found in queue for health
2025-06-15 15:56:49,484 ERROR scheduler Skipped queueing frappe.email.doctype.auto_email_report.auto_email_report.send_daily because it was found in queue for health
2025-06-15 15:56:49,486 ERROR scheduler Skipped queueing hrms.hr.doctype.daily_work_summary_group.daily_work_summary_group.send_summary because it was found in queue for health
2025-06-15 15:56:49,489 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for health
2025-06-15 15:56:49,489 ERROR scheduler Skipped queueing csf_tz.csf_tz.doctype.visibility.visibility.trigger_daily_alerts because it was found in queue for health
2025-06-15 15:56:49,490 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_drive.google_drive.daily_backup because it was found in queue for health
2025-06-15 15:56:49,492 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for health
2025-06-15 15:56:49,493 ERROR scheduler Skipped queueing hrms.hr.doctype.daily_work_summary_group.daily_work_summary_group.trigger_emails because it was found in queue for health
2025-06-15 15:56:49,494 ERROR scheduler Skipped queueing erpnext.assets.doctype.asset.asset.make_post_gl_entry because it was found in queue for health
2025-06-15 15:56:49,496 ERROR scheduler Skipped queueing erpnext.quality_management.doctype.quality_review.quality_review.review because it was found in queue for health
2025-06-15 15:56:49,498 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.remove_unverified_record because it was found in queue for health
2025-06-15 15:56:49,499 ERROR scheduler Skipped queueing csf_tz.csftz_hooks.items_revaluation.process_incorrect_balance_qty because it was found in queue for health
2025-06-15 15:56:49,499 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for health
2025-06-15 15:56:49,501 ERROR scheduler Skipped queueing frappe.integrations.doctype.s3_backup_settings.s3_backup_settings.take_backups_daily because it was found in queue for health
2025-06-15 15:56:49,503 ERROR scheduler Skipped queueing frappe.utils.change_log.check_for_update because it was found in queue for health
2025-06-15 15:56:49,504 ERROR scheduler Skipped queueing propms.lease_invoice.leaseInvoiceAutoCreate because it was found in queue for health
2025-06-15 15:56:49,506 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for health
2025-06-15 15:56:49,507 ERROR scheduler Skipped queueing healthcare.healthcare.doctype.patient_appointment.patient_appointment.update_appointment_status because it was found in queue for health
2025-06-15 15:56:49,508 ERROR scheduler Skipped queueing hrms.hr.utils.generate_leave_encashment because it was found in queue for health
2025-06-15 15:56:49,510 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_weekly_updates because it was found in queue for health
2025-06-15 15:56:49,512 ERROR scheduler Skipped queueing erpnext.stock.doctype.serial_no.serial_no.update_maintenance_status because it was found in queue for health
2025-06-15 15:56:49,513 ERROR scheduler Skipped queueing erpnext.projects.doctype.project.project.update_project_sales_billing because it was found in queue for health
2025-06-15 15:56:49,514 ERROR scheduler Skipped queueing hms_tz.nhif.api.healthcare_utils.auto_finalize_patient_encounters because it was found in queue for health
2025-06-15 15:57:49,665 ERROR scheduler Skipped queueing frappe.desk.doctype.changelog_feed.changelog_feed.fetch_changelog_feed because it was found in queue for health
2025-06-15 15:57:49,671 ERROR scheduler Skipped queueing hrms.hr.utils.generate_leave_encashment because it was found in queue for health
2025-06-15 15:57:49,672 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.update_last_sync_of_checkin because it was found in queue for health
2025-06-15 15:57:49,673 ERROR scheduler Skipped queueing erpnext.crm.utils.open_leads_opportunities_based_on_todays_event because it was found in queue for health
2025-06-15 15:57:49,675 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_contacts.google_contacts.sync because it was found in queue for health
2025-06-15 15:57:49,681 ERROR scheduler Skipped queueing frappe.email.doctype.auto_email_report.auto_email_report.send_daily because it was found in queue for health
2025-06-15 15:57:49,683 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for health
2025-06-15 15:57:49,684 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for health
2025-06-15 15:57:49,687 ERROR scheduler Skipped queueing erpnext.assets.doctype.asset.depreciation.post_depreciation_entries because it was found in queue for health
2025-06-15 15:57:49,688 ERROR scheduler Skipped queueing frappe.social.doctype.energy_point_settings.energy_point_settings.allocate_review_points because it was found in queue for health
2025-06-15 15:57:49,690 ERROR scheduler Skipped queueing hrms.hr.utils.allocate_earned_leaves because it was found in queue for health
2025-06-15 15:57:49,692 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_schedule_assignment.shift_schedule_assignment.process_auto_shift_creation because it was found in queue for health
2025-06-15 15:57:49,693 ERROR scheduler Skipped queueing frappe.integrations.doctype.s3_backup_settings.s3_backup_settings.take_backups_daily because it was found in queue for health
2025-06-15 15:57:49,695 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.remove_unverified_record because it was found in queue for health
2025-06-15 15:57:49,699 ERROR scheduler Skipped queueing erpnext.manufacturing.doctype.bom_update_tool.bom_update_tool.auto_update_latest_price_in_all_boms because it was found in queue for health
2025-06-15 15:57:49,702 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for health
2025-06-15 15:57:49,702 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for health
2025-06-15 15:57:49,706 ERROR scheduler Skipped queueing frappe.automation.doctype.auto_repeat.auto_repeat.make_auto_repeat_entry because it was found in queue for health
2025-06-15 15:57:49,707 ERROR scheduler Skipped queueing erpnext.accounts.doctype.process_subscription.process_subscription.create_subscription_process because it was found in queue for health
2025-06-15 15:57:49,710 ERROR scheduler Skipped queueing hrms.hr.doctype.leave_ledger_entry.leave_ledger_entry.process_expired_allocation because it was found in queue for health
2025-06-15 15:57:49,712 ERROR scheduler Skipped queueing frappe.email.queue.flush because it was found in queue for health
2025-06-15 15:57:49,713 ERROR scheduler Skipped queueing frappe.social.doctype.energy_point_log.energy_point_log.send_weekly_summary because it was found in queue for health
2025-06-15 15:57:49,715 ERROR scheduler Skipped queueing erpnext.setup.doctype.email_digest.email_digest.send because it was found in queue for health
2025-06-15 15:57:49,718 ERROR scheduler Skipped queueing frappe.sessions.clear_expired_sessions because it was found in queue for health
2025-06-15 15:57:49,719 ERROR scheduler Skipped queueing frappe.core.doctype.log_settings.log_settings.run_log_clean_up because it was found in queue for health
2025-06-15 15:57:49,721 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for health
2025-06-15 15:57:49,726 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_weekly_updates because it was found in queue for health
2025-06-15 15:57:49,726 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for health
2025-06-15 15:57:49,727 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.process_auto_attendance_for_all_shifts because it was found in queue for health
2025-06-15 15:57:49,728 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for health
2025-06-15 15:57:49,729 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_drive.google_drive.daily_backup because it was found in queue for health
2025-06-15 15:57:49,732 ERROR scheduler Skipped queueing frappe.utils.change_log.check_for_update because it was found in queue for health
2025-06-15 15:57:49,732 ERROR scheduler Skipped queueing frappe.integrations.doctype.dropbox_settings.dropbox_settings.take_backups_daily because it was found in queue for health
2025-06-15 15:57:49,734 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for health
2025-06-15 15:57:49,739 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for health
2025-06-15 15:57:49,744 ERROR scheduler Skipped queueing frappe.desk.notifications.clear_notifications because it was found in queue for health
2025-06-15 15:57:49,746 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for health
2025-06-15 15:58:49,871 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_weekly_updates because it was found in queue for health
2025-06-15 15:58:49,873 ERROR scheduler Skipped queueing frappe.social.doctype.energy_point_log.energy_point_log.send_weekly_summary because it was found in queue for health
2025-06-15 15:58:49,880 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for health
2025-06-15 15:58:49,882 ERROR scheduler Skipped queueing frappe.utils.change_log.check_for_update because it was found in queue for health
2025-06-15 15:58:49,882 ERROR scheduler Skipped queueing erpnext.manufacturing.doctype.bom_update_tool.bom_update_tool.auto_update_latest_price_in_all_boms because it was found in queue for health
2025-06-15 15:58:49,884 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for health
2025-06-15 15:58:49,889 ERROR scheduler Skipped queueing erpnext.setup.doctype.email_digest.email_digest.send because it was found in queue for health
2025-06-15 15:58:49,891 ERROR scheduler Skipped queueing erpnext.crm.utils.open_leads_opportunities_based_on_todays_event because it was found in queue for health
2025-06-15 15:58:49,895 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for health
2025-06-15 15:58:49,896 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.process_auto_attendance_for_all_shifts because it was found in queue for health
2025-06-15 15:58:49,898 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.update_last_sync_of_checkin because it was found in queue for health
2025-06-15 15:58:49,899 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for health
2025-06-15 15:58:49,902 ERROR scheduler Skipped queueing frappe.automation.doctype.auto_repeat.auto_repeat.make_auto_repeat_entry because it was found in queue for health
2025-06-15 15:58:49,904 ERROR scheduler Skipped queueing hrms.hr.doctype.leave_ledger_entry.leave_ledger_entry.process_expired_allocation because it was found in queue for health
2025-06-15 15:58:49,908 ERROR scheduler Skipped queueing frappe.social.doctype.energy_point_settings.energy_point_settings.allocate_review_points because it was found in queue for health
2025-06-15 15:58:49,909 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_schedule_assignment.shift_schedule_assignment.process_auto_shift_creation because it was found in queue for health
2025-06-15 15:58:49,909 ERROR scheduler Skipped queueing frappe.sessions.clear_expired_sessions because it was found in queue for health
2025-06-15 15:58:49,911 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for health
2025-06-15 15:58:49,912 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for health
2025-06-15 15:58:49,913 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_drive.google_drive.daily_backup because it was found in queue for health
2025-06-15 15:58:49,915 ERROR scheduler Skipped queueing frappe.desk.doctype.changelog_feed.changelog_feed.fetch_changelog_feed because it was found in queue for health
2025-06-15 15:58:49,917 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_contacts.google_contacts.sync because it was found in queue for health
2025-06-15 15:58:49,917 ERROR scheduler Skipped queueing frappe.email.queue.flush because it was found in queue for health
2025-06-15 15:58:49,918 ERROR scheduler Skipped queueing hrms.hr.utils.allocate_earned_leaves because it was found in queue for health
2025-06-15 15:58:49,920 ERROR scheduler Skipped queueing frappe.integrations.doctype.s3_backup_settings.s3_backup_settings.take_backups_daily because it was found in queue for health
2025-06-15 15:58:49,921 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for health
2025-06-15 15:58:49,924 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for health
2025-06-15 15:58:49,925 ERROR scheduler Skipped queueing frappe.core.doctype.log_settings.log_settings.run_log_clean_up because it was found in queue for health
2025-06-15 15:58:49,927 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for health
2025-06-15 15:58:49,931 ERROR scheduler Skipped queueing frappe.integrations.doctype.dropbox_settings.dropbox_settings.take_backups_daily because it was found in queue for health
2025-06-15 15:58:49,942 ERROR scheduler Skipped queueing erpnext.accounts.doctype.process_subscription.process_subscription.create_subscription_process because it was found in queue for health
2025-06-15 15:58:49,944 ERROR scheduler Skipped queueing frappe.email.doctype.auto_email_report.auto_email_report.send_daily because it was found in queue for health
2025-06-15 15:58:49,946 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for health
2025-06-15 15:58:49,946 ERROR scheduler Skipped queueing hrms.hr.utils.generate_leave_encashment because it was found in queue for health
2025-06-15 15:58:49,948 ERROR scheduler Skipped queueing frappe.desk.notifications.clear_notifications because it was found in queue for health
2025-06-15 15:58:49,952 ERROR scheduler Skipped queueing erpnext.assets.doctype.asset.depreciation.post_depreciation_entries because it was found in queue for health
2025-06-15 15:58:49,954 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.remove_unverified_record because it was found in queue for health
2025-06-15 15:59:50,355 ERROR scheduler Skipped queueing erpnext.assets.doctype.asset.depreciation.post_depreciation_entries because it was found in queue for health
2025-06-15 15:59:50,357 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_schedule_assignment.shift_schedule_assignment.process_auto_shift_creation because it was found in queue for health
2025-06-15 15:59:50,358 ERROR scheduler Skipped queueing frappe.desk.doctype.changelog_feed.changelog_feed.fetch_changelog_feed because it was found in queue for health
2025-06-15 15:59:50,359 ERROR scheduler Skipped queueing erpnext.accounts.doctype.process_subscription.process_subscription.create_subscription_process because it was found in queue for health
2025-06-15 15:59:50,360 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.update_last_sync_of_checkin because it was found in queue for health
2025-06-15 15:59:50,363 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for health
2025-06-15 15:59:50,370 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for health
2025-06-15 15:59:50,372 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for health
2025-06-15 15:59:50,374 ERROR scheduler Skipped queueing frappe.email.queue.flush because it was found in queue for health
2025-06-15 15:59:50,375 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_weekly_updates because it was found in queue for health
2025-06-15 15:59:50,382 ERROR scheduler Skipped queueing frappe.automation.doctype.auto_repeat.auto_repeat.make_auto_repeat_entry because it was found in queue for health
2025-06-15 15:59:50,383 ERROR scheduler Skipped queueing frappe.email.doctype.auto_email_report.auto_email_report.send_daily because it was found in queue for health
2025-06-15 15:59:50,388 ERROR scheduler Skipped queueing frappe.integrations.doctype.dropbox_settings.dropbox_settings.take_backups_daily because it was found in queue for health
2025-06-15 15:59:50,392 ERROR scheduler Skipped queueing frappe.utils.change_log.check_for_update because it was found in queue for health
2025-06-15 15:59:50,396 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_contacts.google_contacts.sync because it was found in queue for health
2025-06-15 15:59:50,397 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.remove_unverified_record because it was found in queue for health
2025-06-15 15:59:50,398 ERROR scheduler Skipped queueing erpnext.setup.doctype.email_digest.email_digest.send because it was found in queue for health
2025-06-15 15:59:50,401 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for health
2025-06-15 15:59:50,403 ERROR scheduler Skipped queueing frappe.desk.notifications.clear_notifications because it was found in queue for health
2025-06-15 15:59:50,408 ERROR scheduler Skipped queueing hrms.hr.utils.allocate_earned_leaves because it was found in queue for health
2025-06-15 15:59:50,410 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for health
2025-06-15 15:59:50,412 ERROR scheduler Skipped queueing frappe.core.doctype.log_settings.log_settings.run_log_clean_up because it was found in queue for health
2025-06-15 15:59:50,413 ERROR scheduler Skipped queueing erpnext.crm.utils.open_leads_opportunities_based_on_todays_event because it was found in queue for health
2025-06-15 15:59:50,417 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.process_auto_attendance_for_all_shifts because it was found in queue for health
2025-06-15 15:59:50,419 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for health
2025-06-15 15:59:50,420 ERROR scheduler Skipped queueing frappe.social.doctype.energy_point_settings.energy_point_settings.allocate_review_points because it was found in queue for health
2025-06-15 15:59:50,422 ERROR scheduler Skipped queueing hrms.hr.utils.generate_leave_encashment because it was found in queue for health
2025-06-15 15:59:50,425 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_drive.google_drive.daily_backup because it was found in queue for health
2025-06-15 15:59:50,427 ERROR scheduler Skipped queueing hrms.hr.doctype.leave_ledger_entry.leave_ledger_entry.process_expired_allocation because it was found in queue for health
2025-06-15 15:59:50,428 ERROR scheduler Skipped queueing frappe.integrations.doctype.s3_backup_settings.s3_backup_settings.take_backups_daily because it was found in queue for health
2025-06-15 15:59:50,429 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for health
2025-06-15 15:59:50,431 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for health
2025-06-15 15:59:50,432 ERROR scheduler Skipped queueing frappe.social.doctype.energy_point_log.energy_point_log.send_weekly_summary because it was found in queue for health
2025-06-15 15:59:50,436 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for health
2025-06-15 15:59:50,439 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for health
2025-06-15 15:59:50,442 ERROR scheduler Skipped queueing frappe.sessions.clear_expired_sessions because it was found in queue for health
2025-06-15 15:59:50,443 ERROR scheduler Skipped queueing erpnext.manufacturing.doctype.bom_update_tool.bom_update_tool.auto_update_latest_price_in_all_boms because it was found in queue for health
2025-06-15 21:08:36,300 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.remove_unverified_record because it was found in queue for health
2025-06-15 21:08:36,325 ERROR scheduler Skipped queueing frappe.integrations.doctype.s3_backup_settings.s3_backup_settings.take_backups_daily because it was found in queue for health
2025-06-15 21:08:36,332 ERROR scheduler Skipped queueing frappe.desk.doctype.changelog_feed.changelog_feed.fetch_changelog_feed because it was found in queue for health
2025-06-15 21:08:36,341 ERROR scheduler Skipped queueing frappe.social.doctype.energy_point_log.energy_point_log.send_weekly_summary because it was found in queue for health
2025-06-15 21:08:36,369 ERROR scheduler Skipped queueing frappe.desk.notifications.clear_notifications because it was found in queue for health
2025-06-15 21:08:36,403 ERROR scheduler Skipped queueing erpnext.setup.doctype.email_digest.email_digest.send because it was found in queue for health
2025-06-15 21:08:36,411 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for health
2025-06-15 21:08:36,433 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for health
2025-06-15 21:08:36,447 ERROR scheduler Skipped queueing frappe.utils.change_log.check_for_update because it was found in queue for health
2025-06-15 21:08:36,472 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for health
2025-06-15 21:08:36,477 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for health
2025-06-15 21:08:36,492 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_schedule_assignment.shift_schedule_assignment.process_auto_shift_creation because it was found in queue for health
2025-06-15 21:08:36,519 ERROR scheduler Skipped queueing frappe.sessions.clear_expired_sessions because it was found in queue for health
2025-06-15 21:08:36,522 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for health
2025-06-15 21:08:36,530 ERROR scheduler Skipped queueing frappe.automation.doctype.auto_repeat.auto_repeat.make_auto_repeat_entry because it was found in queue for health
2025-06-15 21:08:36,570 ERROR scheduler Skipped queueing hrms.hr.doctype.leave_ledger_entry.leave_ledger_entry.process_expired_allocation because it was found in queue for health
2025-06-15 21:08:36,576 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for health
2025-06-15 21:08:36,581 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for health
2025-06-15 21:08:36,585 ERROR scheduler Skipped queueing erpnext.manufacturing.doctype.bom_update_tool.bom_update_tool.auto_update_latest_price_in_all_boms because it was found in queue for health
2025-06-15 21:08:36,610 ERROR scheduler Skipped queueing frappe.integrations.doctype.dropbox_settings.dropbox_settings.take_backups_daily because it was found in queue for health
2025-06-15 21:08:36,615 ERROR scheduler Skipped queueing frappe.email.doctype.auto_email_report.auto_email_report.send_daily because it was found in queue for health
2025-06-15 21:08:36,621 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for health
2025-06-15 21:08:36,628 ERROR scheduler Skipped queueing hrms.hr.utils.allocate_earned_leaves because it was found in queue for health
2025-06-15 21:08:36,642 ERROR scheduler Skipped queueing frappe.social.doctype.energy_point_settings.energy_point_settings.allocate_review_points because it was found in queue for health
2025-06-15 21:08:36,653 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_contacts.google_contacts.sync because it was found in queue for health
2025-06-15 21:08:36,657 ERROR scheduler Skipped queueing frappe.core.doctype.log_settings.log_settings.run_log_clean_up because it was found in queue for health
2025-06-15 21:08:36,664 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for health
2025-06-15 21:08:36,677 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for health
2025-06-15 21:08:36,682 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_weekly_updates because it was found in queue for health
2025-06-15 21:08:36,694 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_drive.google_drive.daily_backup because it was found in queue for health
2025-06-15 21:09:38,110 ERROR scheduler Skipped queueing frappe.automation.doctype.reminder.reminder.send_reminders because it was found in queue for health
2025-06-15 21:09:38,123 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for health
2025-06-15 21:09:38,139 ERROR scheduler Skipped queueing erpnext.projects.doctype.project.project.collect_project_status because it was found in queue for health
2025-06-15 21:09:38,143 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_schedule_assignment.shift_schedule_assignment.process_auto_shift_creation because it was found in queue for health
2025-06-15 21:09:38,147 ERROR scheduler Skipped queueing frappe.email.queue.flush because it was found in queue for health
2025-06-15 21:09:38,151 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_contacts.google_contacts.sync because it was found in queue for health
2025-06-15 21:09:38,156 ERROR scheduler Skipped queueing frappe.model.utils.link_count.update_link_count because it was found in queue for health
2025-06-15 21:09:38,163 ERROR scheduler Skipped queueing erpnext.projects.doctype.project.project.project_status_update_reminder because it was found in queue for health
2025-06-15 21:09:38,170 ERROR scheduler Skipped queueing frappe.monitor.flush because it was found in queue for health
2025-06-15 21:09:38,179 ERROR scheduler Skipped queueing csf_tz.csf_tz.doctype.parking_bill.parking_bill.check_bills_all_vehicles because it was found in queue for health
2025-06-15 21:09:38,187 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for health
2025-06-15 21:09:38,190 ERROR scheduler Skipped queueing csf_tz.csf_tz.doctype.vehicle_fine_record.vehicle_fine_record.check_fine_all_vehicles because it was found in queue for health
2025-06-15 21:09:38,203 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for health
2025-06-15 21:09:38,207 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.remove_unverified_record because it was found in queue for health
2025-06-15 21:09:38,214 ERROR scheduler Skipped queueing frappe.integrations.doctype.s3_backup_settings.s3_backup_settings.take_backups_daily because it was found in queue for health
2025-06-15 21:09:38,218 ERROR scheduler Skipped queueing frappe.sessions.clear_expired_sessions because it was found in queue for health
2025-06-15 21:09:38,224 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for health
2025-06-15 21:09:38,231 ERROR scheduler Skipped queueing frappe.social.doctype.energy_point_log.energy_point_log.send_weekly_summary because it was found in queue for health
2025-06-15 21:09:38,238 ERROR scheduler Skipped queueing erpnext.utilities.doctype.video.video.update_youtube_data because it was found in queue for health
2025-06-15 21:09:38,250 ERROR scheduler Skipped queueing erpnext.setup.doctype.email_digest.email_digest.send because it was found in queue for health
2025-06-15 21:09:38,254 ERROR scheduler Skipped queueing erpnext.erpnext_integrations.doctype.plaid_settings.plaid_settings.automatic_synchronization because it was found in queue for health
2025-06-15 21:09:38,259 ERROR scheduler Skipped queueing erpnext.accounts.doctype.process_payment_reconciliation.process_payment_reconciliation.trigger_reconciliation_for_queued_docs because it was found in queue for health
2025-06-15 21:09:38,262 ERROR scheduler Skipped queueing frappe.email.doctype.newsletter.newsletter.send_scheduled_email because it was found in queue for health
2025-06-15 21:09:38,279 ERROR scheduler Skipped queueing hrms.hr.utils.allocate_earned_leaves because it was found in queue for health
2025-06-15 21:09:38,282 ERROR scheduler Skipped queueing erpnext.manufacturing.doctype.bom_update_tool.bom_update_tool.auto_update_latest_price_in_all_boms because it was found in queue for health
2025-06-15 21:09:38,286 ERROR scheduler Skipped queueing healthcare.healthcare.doctype.patient_appointment.patient_appointment.send_appointment_reminder because it was found in queue for health
2025-06-15 21:09:38,297 ERROR scheduler Skipped queueing frappe.desk.notifications.clear_notifications because it was found in queue for health
2025-06-15 21:09:38,302 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_weekly_updates because it was found in queue for health
2025-06-15 21:09:38,323 ERROR scheduler Skipped queueing csf_tz.csftz_hooks.items_revaluation.process_incorrect_balance_qty because it was found in queue for health
2025-06-15 21:09:38,326 ERROR scheduler Skipped queueing erpnext.manufacturing.doctype.bom_update_log.bom_update_log.resume_bom_cost_update_jobs because it was found in queue for health
2025-06-15 21:09:38,331 ERROR scheduler Skipped queueing erpnext.projects.doctype.project.project.hourly_reminder because it was found in queue for health
2025-06-15 21:09:38,337 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for health
2025-06-15 21:09:38,346 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_calendar.google_calendar.sync because it was found in queue for health
2025-06-15 21:09:38,349 ERROR scheduler Skipped queueing csf_tz.stanbic.sftp.process_download_files because it was found in queue for health
2025-06-15 21:09:38,358 ERROR scheduler Skipped queueing frappe.automation.doctype.auto_repeat.auto_repeat.make_auto_repeat_entry because it was found in queue for health
2025-06-15 21:09:38,365 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for health
2025-06-15 21:09:38,373 ERROR scheduler Skipped queueing frappe.email.doctype.auto_email_report.auto_email_report.send_daily because it was found in queue for health
2025-06-15 21:09:38,377 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for health
2025-06-15 21:09:38,384 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_drive.google_drive.daily_backup because it was found in queue for health
2025-06-15 21:09:38,392 ERROR scheduler Skipped queueing frappe.social.doctype.energy_point_settings.energy_point_settings.allocate_review_points because it was found in queue for health
2025-06-15 21:09:38,396 ERROR scheduler Skipped queueing frappe.utils.change_log.check_for_update because it was found in queue for health
2025-06-15 21:09:38,401 ERROR scheduler Skipped queueing frappe.integrations.doctype.dropbox_settings.dropbox_settings.take_backups_daily because it was found in queue for health
2025-06-15 21:09:38,405 ERROR scheduler Skipped queueing hrms.hr.doctype.leave_ledger_entry.leave_ledger_entry.process_expired_allocation because it was found in queue for health
2025-06-15 21:09:38,413 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for health
2025-06-15 21:09:38,417 ERROR scheduler Skipped queueing hrms.hr.doctype.daily_work_summary_group.daily_work_summary_group.trigger_emails because it was found in queue for health
2025-06-15 21:09:38,426 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.pull because it was found in queue for health
2025-06-15 21:09:38,430 ERROR scheduler Skipped queueing csf_tz.stanbic.sftp.sync_all_stanbank_files because it was found in queue for health
2025-06-15 21:09:38,433 ERROR scheduler Skipped queueing frappe.deferred_insert.save_to_db because it was found in queue for health
2025-06-15 21:09:38,447 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for health
2025-06-15 21:09:38,454 ERROR scheduler Skipped queueing payments.payment_gateways.doctype.razorpay_settings.razorpay_settings.capture_payment because it was found in queue for health
2025-06-15 21:09:38,470 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.notify_unreplied because it was found in queue for health
2025-06-15 21:09:38,475 ERROR scheduler Skipped queueing frappe.utils.global_search.sync_global_search because it was found in queue for health
2025-06-15 21:09:38,481 ERROR scheduler Skipped queueing hms_tz.nhif.api.healthcare_utils.set_uninvoiced_so_closed because it was found in queue for health
2025-06-15 21:09:38,488 ERROR scheduler Skipped queueing hrms.hr.doctype.interview.interview.send_interview_reminder because it was found in queue for health
2025-06-15 21:09:38,491 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for health
2025-06-15 21:09:38,505 ERROR scheduler Skipped queueing erpnext.accounts.doctype.gl_entry.gl_entry.rename_gle_sle_docs because it was found in queue for health
2025-06-15 21:09:38,516 ERROR scheduler Skipped queueing my_test_hourly because it was found in queue for health
2025-06-15 21:09:38,536 ERROR scheduler Skipped queueing frappe.desk.doctype.changelog_feed.changelog_feed.fetch_changelog_feed because it was found in queue for health
2025-06-15 21:09:38,540 ERROR scheduler Skipped queueing hms_tz.nhif.api.healthcare_utils.create_invoiced_items_if_not_created because it was found in queue for health
2025-06-15 21:09:38,546 ERROR scheduler Skipped queueing frappe.core.doctype.log_settings.log_settings.run_log_clean_up because it was found in queue for health
2025-06-15 21:10:40,565 ERROR scheduler Skipped queueing frappe.desk.doctype.changelog_feed.changelog_feed.fetch_changelog_feed because it was found in queue for health
2025-06-15 21:10:40,581 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_contacts.google_contacts.sync because it was found in queue for health
2025-06-15 21:10:40,590 ERROR scheduler Skipped queueing erpnext.erpnext_integrations.doctype.plaid_settings.plaid_settings.automatic_synchronization because it was found in queue for health
2025-06-15 21:10:40,599 ERROR scheduler Skipped queueing payments.payment_gateways.doctype.razorpay_settings.razorpay_settings.capture_payment because it was found in queue for health
2025-06-15 21:10:40,602 ERROR scheduler Skipped queueing hrms.hr.doctype.leave_ledger_entry.leave_ledger_entry.process_expired_allocation because it was found in queue for health
2025-06-15 21:10:40,613 ERROR scheduler Skipped queueing frappe.integrations.doctype.s3_backup_settings.s3_backup_settings.take_backups_daily because it was found in queue for health
2025-06-15 21:10:40,617 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for health
2025-06-15 21:10:40,629 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for health
2025-06-15 21:10:40,642 ERROR scheduler Skipped queueing frappe.utils.change_log.check_for_update because it was found in queue for health
2025-06-15 21:10:40,653 ERROR scheduler Skipped queueing csf_tz.stanbic.sftp.process_download_files because it was found in queue for health
2025-06-15 21:10:40,657 ERROR scheduler Skipped queueing erpnext.accounts.doctype.process_payment_reconciliation.process_payment_reconciliation.trigger_reconciliation_for_queued_docs because it was found in queue for health
2025-06-15 21:10:40,661 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.pull because it was found in queue for health
2025-06-15 21:10:40,666 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for health
2025-06-15 21:10:40,671 ERROR scheduler Skipped queueing frappe.email.queue.flush because it was found in queue for health
2025-06-15 21:10:40,675 ERROR scheduler Skipped queueing frappe.automation.doctype.auto_repeat.auto_repeat.make_auto_repeat_entry because it was found in queue for health
2025-06-15 21:10:40,683 ERROR scheduler Skipped queueing frappe.integrations.doctype.dropbox_settings.dropbox_settings.take_backups_daily because it was found in queue for health
2025-06-15 21:10:40,691 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_schedule_assignment.shift_schedule_assignment.process_auto_shift_creation because it was found in queue for health
2025-06-15 21:10:40,695 ERROR scheduler Skipped queueing csf_tz.stanbic.sftp.sync_all_stanbank_files because it was found in queue for health
2025-06-15 21:10:40,698 ERROR scheduler Skipped queueing frappe.email.doctype.newsletter.newsletter.send_scheduled_email because it was found in queue for health
2025-06-15 21:10:40,725 ERROR scheduler Skipped queueing frappe.utils.global_search.sync_global_search because it was found in queue for health
2025-06-15 21:10:40,767 ERROR scheduler Skipped queueing frappe.deferred_insert.save_to_db because it was found in queue for health
2025-06-15 21:10:40,849 ERROR scheduler Skipped queueing frappe.social.doctype.energy_point_log.energy_point_log.send_weekly_summary because it was found in queue for health
2025-06-15 21:10:40,856 ERROR scheduler Skipped queueing hms_tz.nhif.api.healthcare_utils.create_invoiced_items_if_not_created because it was found in queue for health
2025-06-15 21:10:40,863 ERROR scheduler Skipped queueing frappe.email.doctype.auto_email_report.auto_email_report.send_daily because it was found in queue for health
2025-06-15 21:10:40,875 ERROR scheduler Skipped queueing csf_tz.csf_tz.doctype.vehicle_fine_record.vehicle_fine_record.check_fine_all_vehicles because it was found in queue for health
2025-06-15 21:10:40,887 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for health
2025-06-15 21:10:40,891 ERROR scheduler Skipped queueing frappe.monitor.flush because it was found in queue for health
2025-06-15 21:10:40,898 ERROR scheduler Skipped queueing erpnext.accounts.doctype.gl_entry.gl_entry.rename_gle_sle_docs because it was found in queue for health
2025-06-15 21:10:40,902 ERROR scheduler Skipped queueing frappe.social.doctype.energy_point_settings.energy_point_settings.allocate_review_points because it was found in queue for health
2025-06-15 21:10:40,908 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for health
2025-06-15 21:10:40,913 ERROR scheduler Skipped queueing my_test_hourly because it was found in queue for health
2025-06-15 21:10:40,916 ERROR scheduler Skipped queueing csf_tz.csftz_hooks.items_revaluation.process_incorrect_balance_qty because it was found in queue for health
2025-06-15 21:10:40,920 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for health
2025-06-15 21:10:40,927 ERROR scheduler Skipped queueing healthcare.healthcare.doctype.patient_appointment.patient_appointment.send_appointment_reminder because it was found in queue for health
2025-06-15 21:10:40,938 ERROR scheduler Skipped queueing erpnext.projects.doctype.project.project.collect_project_status because it was found in queue for health
2025-06-15 21:10:40,942 ERROR scheduler Skipped queueing hrms.hr.utils.allocate_earned_leaves because it was found in queue for health
2025-06-15 21:10:40,948 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for health
2025-06-15 21:10:40,955 ERROR scheduler Skipped queueing erpnext.utilities.doctype.video.video.update_youtube_data because it was found in queue for health
2025-06-15 21:10:40,959 ERROR scheduler Skipped queueing hrms.hr.doctype.interview.interview.send_interview_reminder because it was found in queue for health
2025-06-15 21:10:40,963 ERROR scheduler Skipped queueing frappe.model.utils.link_count.update_link_count because it was found in queue for health
2025-06-15 21:10:40,967 ERROR scheduler Skipped queueing erpnext.manufacturing.doctype.bom_update_log.bom_update_log.resume_bom_cost_update_jobs because it was found in queue for health
2025-06-15 21:10:40,981 ERROR scheduler Skipped queueing erpnext.projects.doctype.project.project.hourly_reminder because it was found in queue for health
2025-06-15 21:10:40,988 ERROR scheduler Skipped queueing frappe.sessions.clear_expired_sessions because it was found in queue for health
2025-06-15 21:10:40,998 ERROR scheduler Skipped queueing frappe.core.doctype.log_settings.log_settings.run_log_clean_up because it was found in queue for health
2025-06-15 21:10:41,007 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for health
2025-06-15 21:10:41,015 ERROR scheduler Skipped queueing erpnext.setup.doctype.email_digest.email_digest.send because it was found in queue for health
2025-06-15 21:10:41,020 ERROR scheduler Skipped queueing erpnext.manufacturing.doctype.bom_update_tool.bom_update_tool.auto_update_latest_price_in_all_boms because it was found in queue for health
2025-06-15 21:10:41,036 ERROR scheduler Skipped queueing hms_tz.nhif.api.healthcare_utils.set_uninvoiced_so_closed because it was found in queue for health
2025-06-15 21:10:41,044 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.remove_unverified_record because it was found in queue for health
2025-06-15 21:10:41,047 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for health
2025-06-15 21:10:41,062 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_weekly_updates because it was found in queue for health
2025-06-15 21:10:41,065 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_calendar.google_calendar.sync because it was found in queue for health
2025-06-15 21:10:41,076 ERROR scheduler Skipped queueing hrms.hr.doctype.daily_work_summary_group.daily_work_summary_group.trigger_emails because it was found in queue for health
2025-06-15 21:10:41,080 ERROR scheduler Skipped queueing frappe.desk.notifications.clear_notifications because it was found in queue for health
2025-06-15 21:10:41,085 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.notify_unreplied because it was found in queue for health
2025-06-15 21:10:41,089 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_drive.google_drive.daily_backup because it was found in queue for health
2025-06-15 21:10:41,092 ERROR scheduler Skipped queueing frappe.automation.doctype.reminder.reminder.send_reminders because it was found in queue for health
2025-06-15 21:10:41,096 ERROR scheduler Skipped queueing csf_tz.csf_tz.doctype.parking_bill.parking_bill.check_bills_all_vehicles because it was found in queue for health
2025-06-15 21:10:41,102 ERROR scheduler Skipped queueing erpnext.projects.doctype.project.project.project_status_update_reminder because it was found in queue for health
2025-06-15 21:10:41,105 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for health
2025-06-15 21:11:41,131 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_contacts.google_contacts.sync because it was found in queue for health
2025-06-15 21:11:41,137 ERROR scheduler Skipped queueing frappe.desk.doctype.changelog_feed.changelog_feed.fetch_changelog_feed because it was found in queue for health
2025-06-15 21:11:41,164 ERROR scheduler Skipped queueing hms_tz.nhif.api.healthcare_utils.create_invoiced_items_if_not_created because it was found in queue for health
2025-06-15 21:11:41,169 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_schedule_assignment.shift_schedule_assignment.process_auto_shift_creation because it was found in queue for health
2025-06-15 21:11:41,173 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_drive.google_drive.daily_backup because it was found in queue for health
2025-06-15 21:11:41,180 ERROR scheduler Skipped queueing frappe.utils.change_log.check_for_update because it was found in queue for health
2025-06-15 21:11:41,184 ERROR scheduler Skipped queueing csf_tz.csftz_hooks.items_revaluation.process_incorrect_balance_qty because it was found in queue for health
2025-06-15 21:11:41,194 ERROR scheduler Skipped queueing frappe.core.doctype.log_settings.log_settings.run_log_clean_up because it was found in queue for health
2025-06-15 21:11:41,198 ERROR scheduler Skipped queueing my_test_hourly because it was found in queue for health
2025-06-15 21:11:41,204 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for health
2025-06-15 21:11:41,207 ERROR scheduler Skipped queueing hrms.hr.doctype.interview.interview.send_interview_reminder because it was found in queue for health
2025-06-15 21:11:41,212 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.notify_unreplied because it was found in queue for health
2025-06-15 21:11:41,217 ERROR scheduler Skipped queueing frappe.monitor.flush because it was found in queue for health
2025-06-15 21:11:41,239 ERROR scheduler Skipped queueing erpnext.projects.doctype.project.project.hourly_reminder because it was found in queue for health
2025-06-15 21:11:41,246 ERROR scheduler Skipped queueing frappe.automation.doctype.reminder.reminder.send_reminders because it was found in queue for health
2025-06-15 21:11:41,250 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for health
2025-06-15 21:11:41,255 ERROR scheduler Skipped queueing hms_tz.nhif.api.healthcare_utils.set_uninvoiced_so_closed because it was found in queue for health
2025-06-15 21:11:41,264 ERROR scheduler Skipped queueing frappe.email.doctype.newsletter.newsletter.send_scheduled_email because it was found in queue for health
2025-06-15 21:11:41,274 ERROR scheduler Skipped queueing erpnext.accounts.doctype.process_payment_reconciliation.process_payment_reconciliation.trigger_reconciliation_for_queued_docs because it was found in queue for health
2025-06-15 21:11:41,281 ERROR scheduler Skipped queueing hrms.hr.doctype.leave_ledger_entry.leave_ledger_entry.process_expired_allocation because it was found in queue for health
2025-06-15 21:11:41,285 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for health
2025-06-15 21:11:41,297 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for health
2025-06-15 21:11:41,302 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_calendar.google_calendar.sync because it was found in queue for health
2025-06-15 21:11:41,308 ERROR scheduler Skipped queueing frappe.sessions.clear_expired_sessions because it was found in queue for health
2025-06-15 21:11:41,315 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for health
2025-06-15 21:11:41,325 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for health
2025-06-15 21:11:41,329 ERROR scheduler Skipped queueing erpnext.accounts.doctype.gl_entry.gl_entry.rename_gle_sle_docs because it was found in queue for health
2025-06-15 21:11:41,333 ERROR scheduler Skipped queueing csf_tz.csf_tz.doctype.parking_bill.parking_bill.check_bills_all_vehicles because it was found in queue for health
2025-06-15 21:11:41,341 ERROR scheduler Skipped queueing frappe.integrations.doctype.dropbox_settings.dropbox_settings.take_backups_daily because it was found in queue for health
2025-06-15 21:11:41,346 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for health
2025-06-15 21:11:41,358 ERROR scheduler Skipped queueing frappe.deferred_insert.save_to_db because it was found in queue for health
2025-06-15 21:11:41,370 ERROR scheduler Skipped queueing erpnext.projects.doctype.project.project.project_status_update_reminder because it was found in queue for health
2025-06-15 21:11:41,382 ERROR scheduler Skipped queueing erpnext.projects.doctype.project.project.collect_project_status because it was found in queue for health
2025-06-15 21:11:41,386 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_weekly_updates because it was found in queue for health
2025-06-15 21:11:41,390 ERROR scheduler Skipped queueing payments.payment_gateways.doctype.razorpay_settings.razorpay_settings.capture_payment because it was found in queue for health
2025-06-15 21:11:41,396 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for health
2025-06-15 21:11:41,402 ERROR scheduler Skipped queueing frappe.integrations.doctype.s3_backup_settings.s3_backup_settings.take_backups_daily because it was found in queue for health
2025-06-15 21:11:41,406 ERROR scheduler Skipped queueing frappe.model.utils.link_count.update_link_count because it was found in queue for health
2025-06-15 21:11:41,410 ERROR scheduler Skipped queueing erpnext.manufacturing.doctype.bom_update_tool.bom_update_tool.auto_update_latest_price_in_all_boms because it was found in queue for health
2025-06-15 21:11:41,413 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.pull because it was found in queue for health
2025-06-15 21:11:41,424 ERROR scheduler Skipped queueing csf_tz.csf_tz.doctype.vehicle_fine_record.vehicle_fine_record.check_fine_all_vehicles because it was found in queue for health
2025-06-15 21:11:41,430 ERROR scheduler Skipped queueing healthcare.healthcare.doctype.patient_appointment.patient_appointment.send_appointment_reminder because it was found in queue for health
2025-06-15 21:11:41,438 ERROR scheduler Skipped queueing frappe.social.doctype.energy_point_log.energy_point_log.send_weekly_summary because it was found in queue for health
2025-06-15 21:11:41,443 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.remove_unverified_record because it was found in queue for health
2025-06-15 21:11:41,449 ERROR scheduler Skipped queueing frappe.automation.doctype.auto_repeat.auto_repeat.make_auto_repeat_entry because it was found in queue for health
2025-06-15 21:11:41,454 ERROR scheduler Skipped queueing erpnext.setup.doctype.email_digest.email_digest.send because it was found in queue for health
2025-06-15 21:11:41,457 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for health
2025-06-15 21:11:41,462 ERROR scheduler Skipped queueing hrms.hr.utils.allocate_earned_leaves because it was found in queue for health
2025-06-15 21:11:41,471 ERROR scheduler Skipped queueing erpnext.manufacturing.doctype.bom_update_log.bom_update_log.resume_bom_cost_update_jobs because it was found in queue for health
2025-06-15 21:11:41,476 ERROR scheduler Skipped queueing hrms.hr.doctype.daily_work_summary_group.daily_work_summary_group.trigger_emails because it was found in queue for health
2025-06-15 21:11:41,480 ERROR scheduler Skipped queueing erpnext.utilities.doctype.video.video.update_youtube_data because it was found in queue for health
2025-06-15 21:11:41,487 ERROR scheduler Skipped queueing frappe.email.queue.flush because it was found in queue for health
2025-06-15 21:11:41,496 ERROR scheduler Skipped queueing erpnext.erpnext_integrations.doctype.plaid_settings.plaid_settings.automatic_synchronization because it was found in queue for health
2025-06-15 21:11:41,502 ERROR scheduler Skipped queueing frappe.utils.global_search.sync_global_search because it was found in queue for health
2025-06-15 21:11:41,510 ERROR scheduler Skipped queueing csf_tz.stanbic.sftp.process_download_files because it was found in queue for health
2025-06-15 21:11:41,516 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for health
2025-06-15 21:11:41,519 ERROR scheduler Skipped queueing frappe.email.doctype.auto_email_report.auto_email_report.send_daily because it was found in queue for health
2025-06-15 21:11:41,529 ERROR scheduler Skipped queueing csf_tz.stanbic.sftp.sync_all_stanbank_files because it was found in queue for health
2025-06-15 21:11:41,548 ERROR scheduler Skipped queueing frappe.desk.notifications.clear_notifications because it was found in queue for health
2025-06-15 21:11:41,557 ERROR scheduler Skipped queueing frappe.social.doctype.energy_point_settings.energy_point_settings.allocate_review_points because it was found in queue for health
2025-06-15 21:12:43,810 ERROR scheduler Skipped queueing erpnext.accounts.doctype.gl_entry.gl_entry.rename_gle_sle_docs because it was found in queue for health
2025-06-15 21:12:43,814 ERROR scheduler Skipped queueing erpnext.accounts.doctype.process_payment_reconciliation.process_payment_reconciliation.trigger_reconciliation_for_queued_docs because it was found in queue for health
2025-06-15 21:12:43,821 ERROR scheduler Skipped queueing hrms.hr.doctype.leave_ledger_entry.leave_ledger_entry.process_expired_allocation because it was found in queue for health
2025-06-15 21:12:43,830 ERROR scheduler Skipped queueing erpnext.manufacturing.doctype.bom_update_tool.bom_update_tool.auto_update_latest_price_in_all_boms because it was found in queue for health
2025-06-15 21:12:43,834 ERROR scheduler Skipped queueing erpnext.setup.doctype.email_digest.email_digest.send because it was found in queue for health
2025-06-15 21:12:43,838 ERROR scheduler Skipped queueing frappe.desk.notifications.clear_notifications because it was found in queue for health
2025-06-15 21:12:43,846 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.pull because it was found in queue for health
2025-06-15 21:12:43,852 ERROR scheduler Skipped queueing frappe.integrations.doctype.s3_backup_settings.s3_backup_settings.take_backups_daily because it was found in queue for health
2025-06-15 21:12:43,861 ERROR scheduler Skipped queueing hms_tz.nhif.api.healthcare_utils.create_invoiced_items_if_not_created because it was found in queue for health
2025-06-15 21:12:43,875 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_calendar.google_calendar.sync because it was found in queue for health
2025-06-15 21:12:43,879 ERROR scheduler Skipped queueing erpnext.projects.doctype.project.project.hourly_reminder because it was found in queue for health
2025-06-15 21:12:43,888 ERROR scheduler Skipped queueing csf_tz.csf_tz.doctype.vehicle_fine_record.vehicle_fine_record.check_fine_all_vehicles because it was found in queue for health
2025-06-15 21:12:43,893 ERROR scheduler Skipped queueing erpnext.utilities.doctype.video.video.update_youtube_data because it was found in queue for health
2025-06-15 21:12:43,900 ERROR scheduler Skipped queueing frappe.email.doctype.auto_email_report.auto_email_report.send_daily because it was found in queue for health
2025-06-15 21:12:43,911 ERROR scheduler Skipped queueing frappe.integrations.doctype.dropbox_settings.dropbox_settings.take_backups_daily because it was found in queue for health
2025-06-15 21:12:43,915 ERROR scheduler Skipped queueing frappe.utils.change_log.check_for_update because it was found in queue for health
