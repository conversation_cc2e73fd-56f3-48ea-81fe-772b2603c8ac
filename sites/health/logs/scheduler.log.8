2025-06-19 10:17:05,478 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.update_last_sync_of_checkin because it was found in queue for health
2025-06-19 10:17:05,488 ERROR scheduler Skipped queueing csf_tz.stanbic.sftp.sync_all_stanbank_files because it was found in queue for health
2025-06-19 10:17:05,502 ERROR scheduler Skipped queueing payments.payment_gateways.doctype.razorpay_settings.razorpay_settings.capture_payment because it was found in queue for health
2025-06-19 10:17:05,523 ERROR scheduler Skipped queueing frappe.utils.global_search.sync_global_search because it was found in queue for health
2025-06-19 10:17:05,531 ERROR scheduler Skipped queueing erpnext.crm.utils.open_leads_opportunities_based_on_todays_event because it was found in queue for health
2025-06-19 10:17:05,535 ERROR scheduler Skipped queueing frappe.integrations.doctype.dropbox_settings.dropbox_settings.take_backups_daily because it was found in queue for health
2025-06-19 10:17:05,541 ERROR scheduler Skipped queueing erpnext.accounts.doctype.process_subscription.process_subscription.create_subscription_process because it was found in queue for health
2025-06-19 10:17:05,550 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for health
2025-06-19 10:17:05,560 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.remove_unverified_record because it was found in queue for health
2025-06-19 10:17:05,566 ERROR scheduler Skipped queueing frappe.automation.doctype.reminder.reminder.send_reminders because it was found in queue for health
2025-06-19 10:17:05,570 ERROR scheduler Skipped queueing frappe.model.utils.link_count.update_link_count because it was found in queue for health
2025-06-19 10:17:05,581 ERROR scheduler Skipped queueing hrms.hr.doctype.leave_ledger_entry.leave_ledger_entry.process_expired_allocation because it was found in queue for health
2025-06-19 10:17:05,599 ERROR scheduler Skipped queueing frappe.integrations.doctype.s3_backup_settings.s3_backup_settings.take_backups_daily because it was found in queue for health
2025-06-19 10:17:05,606 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.notify_unreplied because it was found in queue for health
2025-06-19 10:17:05,611 ERROR scheduler Skipped queueing frappe.core.doctype.log_settings.log_settings.run_log_clean_up because it was found in queue for health
2025-06-19 10:17:05,619 ERROR scheduler Skipped queueing erpnext.manufacturing.doctype.bom_update_tool.bom_update_tool.auto_update_latest_price_in_all_boms because it was found in queue for health
2025-06-19 10:17:05,630 ERROR scheduler Skipped queueing erpnext.assets.doctype.asset.depreciation.post_depreciation_entries because it was found in queue for health
2025-06-19 10:17:05,635 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.process_auto_attendance_for_all_shifts because it was found in queue for health
2025-06-19 10:17:05,639 ERROR scheduler Skipped queueing erpnext.accounts.doctype.fiscal_year.fiscal_year.auto_create_fiscal_year because it was found in queue for health
2025-06-19 10:17:05,651 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for health
2025-06-19 10:17:05,659 ERROR scheduler Skipped queueing hms_tz.nhif.api.healthcare_utils.create_invoiced_items_if_not_created because it was found in queue for health
2025-06-19 10:17:05,665 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for health
2025-06-19 10:17:05,671 ERROR scheduler Skipped queueing erpnext.manufacturing.doctype.bom_update_log.bom_update_log.resume_bom_cost_update_jobs because it was found in queue for health
2025-06-19 10:17:05,677 ERROR scheduler Skipped queueing healthcare.healthcare.doctype.fee_validity.fee_validity.update_validity_status because it was found in queue for health
2025-06-19 10:17:05,696 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_contacts.google_contacts.sync because it was found in queue for health
2025-06-19 10:17:05,704 ERROR scheduler Skipped queueing hrms.hr.doctype.interview.interview.send_interview_reminder because it was found in queue for health
2025-06-19 10:17:05,708 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for health
2025-06-19 10:17:05,714 ERROR scheduler Skipped queueing frappe.email.queue.flush because it was found in queue for health
2025-06-19 10:17:05,718 ERROR scheduler Skipped queueing frappe.sessions.clear_expired_sessions because it was found in queue for health
2025-06-19 10:17:05,721 ERROR scheduler Skipped queueing hrms.hr.utils.allocate_earned_leaves because it was found in queue for health
2025-06-19 10:17:05,725 ERROR scheduler Skipped queueing frappe.automation.doctype.auto_repeat.auto_repeat.make_auto_repeat_entry because it was found in queue for health
2025-06-19 10:17:05,730 ERROR scheduler Skipped queueing frappe.email.doctype.auto_email_report.auto_email_report.send_daily because it was found in queue for health
2025-06-19 10:17:05,734 ERROR scheduler Skipped queueing erpnext.setup.doctype.email_digest.email_digest.send because it was found in queue for health
2025-06-19 10:17:05,741 ERROR scheduler Skipped queueing erpnext.selling.doctype.quotation.quotation.set_expired_status because it was found in queue for health
2025-06-19 10:17:05,745 ERROR scheduler Skipped queueing hrms.hr.utils.generate_leave_encashment because it was found in queue for health
2025-06-19 10:17:05,752 ERROR scheduler Skipped queueing erpnext.support.doctype.issue.issue.auto_close_tickets because it was found in queue for health
2025-06-19 10:17:05,759 ERROR scheduler Skipped queueing erpnext.crm.doctype.contract.contract.update_status_for_contracts because it was found in queue for health
2025-06-19 10:17:05,763 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for health
2025-06-19 10:17:05,770 ERROR scheduler Skipped queueing hrms.hr.doctype.interview.interview.send_daily_feedback_reminder because it was found in queue for health
2025-06-19 10:17:05,787 ERROR scheduler Skipped queueing healthcare.healthcare.doctype.patient_appointment.patient_appointment.update_appointment_status because it was found in queue for health
2025-06-19 10:18:06,856 ERROR scheduler Skipped queueing erpnext.crm.utils.open_leads_opportunities_based_on_todays_event because it was found in queue for health
2025-06-19 10:18:06,871 ERROR scheduler Skipped queueing frappe.email.queue.flush because it was found in queue for health
2025-06-19 10:18:06,875 ERROR scheduler Skipped queueing frappe.automation.doctype.reminder.reminder.send_reminders because it was found in queue for health
2025-06-19 10:18:06,885 ERROR scheduler Skipped queueing hrms.hr.utils.allocate_earned_leaves because it was found in queue for health
2025-06-19 10:18:06,911 ERROR scheduler Skipped queueing frappe.model.utils.link_count.update_link_count because it was found in queue for health
2025-06-19 10:18:06,926 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for health
2025-06-19 10:18:06,937 ERROR scheduler Skipped queueing csf_tz.stanbic.sftp.sync_all_stanbank_files because it was found in queue for health
2025-06-19 10:18:06,941 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for health
2025-06-19 10:18:06,957 ERROR scheduler Skipped queueing erpnext.setup.doctype.email_digest.email_digest.send because it was found in queue for health
2025-06-19 10:18:06,967 ERROR scheduler Skipped queueing healthcare.healthcare.doctype.patient_appointment.patient_appointment.send_appointment_reminder because it was found in queue for health
2025-06-19 10:18:06,972 ERROR scheduler Skipped queueing payments.payment_gateways.doctype.razorpay_settings.razorpay_settings.capture_payment because it was found in queue for health
2025-06-19 10:18:06,981 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for health
2025-06-19 10:18:06,987 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for health
2025-06-19 10:18:06,993 ERROR scheduler Skipped queueing frappe.integrations.doctype.s3_backup_settings.s3_backup_settings.take_backups_daily because it was found in queue for health
2025-06-19 10:18:06,997 ERROR scheduler Skipped queueing frappe.email.doctype.auto_email_report.auto_email_report.send_daily because it was found in queue for health
2025-06-19 10:18:07,000 ERROR scheduler Skipped queueing frappe.social.doctype.energy_point_settings.energy_point_settings.allocate_review_points because it was found in queue for health
2025-06-19 10:18:07,004 ERROR scheduler Skipped queueing frappe.automation.doctype.auto_repeat.auto_repeat.make_auto_repeat_entry because it was found in queue for health
2025-06-19 10:18:07,015 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for health
2025-06-19 10:18:07,020 ERROR scheduler Skipped queueing hrms.hr.doctype.leave_ledger_entry.leave_ledger_entry.process_expired_allocation because it was found in queue for health
2025-06-19 10:18:07,052 ERROR scheduler Skipped queueing erpnext.accounts.doctype.process_subscription.process_subscription.create_subscription_process because it was found in queue for health
2025-06-19 10:18:07,057 ERROR scheduler Skipped queueing erpnext.manufacturing.doctype.bom_update_tool.bom_update_tool.auto_update_latest_price_in_all_boms because it was found in queue for health
2025-06-19 10:18:07,060 ERROR scheduler Skipped queueing frappe.integrations.doctype.dropbox_settings.dropbox_settings.take_backups_daily because it was found in queue for health
2025-06-19 10:18:07,063 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_schedule_assignment.shift_schedule_assignment.process_auto_shift_creation because it was found in queue for health
2025-06-19 10:18:07,067 ERROR scheduler Skipped queueing hrms.hr.utils.generate_leave_encashment because it was found in queue for health
2025-06-19 10:18:07,071 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for health
2025-06-19 10:18:07,085 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for health
2025-06-19 10:18:07,103 ERROR scheduler Skipped queueing frappe.core.doctype.log_settings.log_settings.run_log_clean_up because it was found in queue for health
2025-06-19 10:18:07,125 ERROR scheduler Skipped queueing erpnext.manufacturing.doctype.bom_update_log.bom_update_log.resume_bom_cost_update_jobs because it was found in queue for health
2025-06-19 10:18:07,131 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_contacts.google_contacts.sync because it was found in queue for health
2025-06-19 10:18:07,148 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.process_auto_attendance_for_all_shifts because it was found in queue for health
2025-06-19 10:18:07,156 ERROR scheduler Skipped queueing frappe.sessions.clear_expired_sessions because it was found in queue for health
2025-06-19 10:18:07,165 ERROR scheduler Skipped queueing hrms.hr.doctype.interview.interview.send_interview_reminder because it was found in queue for health
2025-06-19 10:18:07,183 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_drive.google_drive.daily_backup because it was found in queue for health
2025-06-19 10:18:07,186 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.remove_unverified_record because it was found in queue for health
2025-06-19 10:18:07,190 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for health
2025-06-19 10:18:07,194 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.update_last_sync_of_checkin because it was found in queue for health
2025-06-19 10:18:07,197 ERROR scheduler Skipped queueing frappe.utils.global_search.sync_global_search because it was found in queue for health
2025-06-19 10:18:07,201 ERROR scheduler Skipped queueing erpnext.assets.doctype.asset.depreciation.post_depreciation_entries because it was found in queue for health
2025-06-19 10:18:07,219 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for health
2025-06-19 10:18:07,230 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for health
2025-06-19 10:18:07,234 ERROR scheduler Skipped queueing frappe.desk.notifications.clear_notifications because it was found in queue for health
2025-06-19 10:19:08,928 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for health
2025-06-19 10:19:08,936 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for health
2025-06-19 10:19:08,945 ERROR scheduler Skipped queueing hrms.hr.doctype.leave_ledger_entry.leave_ledger_entry.process_expired_allocation because it was found in queue for health
2025-06-19 10:19:08,948 ERROR scheduler Skipped queueing frappe.sessions.clear_expired_sessions because it was found in queue for health
2025-06-19 10:19:08,953 ERROR scheduler Skipped queueing frappe.integrations.doctype.s3_backup_settings.s3_backup_settings.take_backups_daily because it was found in queue for health
2025-06-19 10:19:08,958 ERROR scheduler Skipped queueing frappe.email.doctype.auto_email_report.auto_email_report.send_daily because it was found in queue for health
2025-06-19 10:19:08,968 ERROR scheduler Skipped queueing frappe.social.doctype.energy_point_settings.energy_point_settings.allocate_review_points because it was found in queue for health
2025-06-19 10:19:08,980 ERROR scheduler Skipped queueing frappe.automation.doctype.reminder.reminder.send_reminders because it was found in queue for health
2025-06-19 10:19:08,988 ERROR scheduler Skipped queueing frappe.model.utils.link_count.update_link_count because it was found in queue for health
2025-06-19 10:19:08,994 ERROR scheduler Skipped queueing erpnext.manufacturing.doctype.bom_update_tool.bom_update_tool.auto_update_latest_price_in_all_boms because it was found in queue for health
2025-06-19 10:19:08,997 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_drive.google_drive.daily_backup because it was found in queue for health
2025-06-19 10:19:09,002 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for health
2025-06-19 10:19:09,008 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for health
2025-06-19 10:19:09,015 ERROR scheduler Skipped queueing frappe.automation.doctype.auto_repeat.auto_repeat.make_auto_repeat_entry because it was found in queue for health
2025-06-19 10:19:09,023 ERROR scheduler Skipped queueing healthcare.healthcare.doctype.patient_appointment.patient_appointment.send_appointment_reminder because it was found in queue for health
2025-06-19 10:19:09,032 ERROR scheduler Skipped queueing erpnext.setup.doctype.email_digest.email_digest.send because it was found in queue for health
2025-06-19 10:19:09,037 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for health
2025-06-19 10:19:09,056 ERROR scheduler Skipped queueing hrms.hr.doctype.interview.interview.send_interview_reminder because it was found in queue for health
2025-06-19 10:19:09,060 ERROR scheduler Skipped queueing erpnext.assets.doctype.asset.depreciation.post_depreciation_entries because it was found in queue for health
2025-06-19 10:19:09,065 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.remove_unverified_record because it was found in queue for health
2025-06-19 10:19:09,073 ERROR scheduler Skipped queueing frappe.utils.global_search.sync_global_search because it was found in queue for health
2025-06-19 10:19:09,077 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for health
2025-06-19 10:19:09,080 ERROR scheduler Skipped queueing csf_tz.stanbic.sftp.sync_all_stanbank_files because it was found in queue for health
2025-06-19 10:19:09,098 ERROR scheduler Skipped queueing hrms.hr.utils.allocate_earned_leaves because it was found in queue for health
2025-06-19 10:19:09,109 ERROR scheduler Skipped queueing erpnext.crm.utils.open_leads_opportunities_based_on_todays_event because it was found in queue for health
2025-06-19 10:19:09,123 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for health
2025-06-19 10:19:09,128 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_contacts.google_contacts.sync because it was found in queue for health
2025-06-19 10:19:09,136 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.update_last_sync_of_checkin because it was found in queue for health
2025-06-19 10:19:09,140 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_schedule_assignment.shift_schedule_assignment.process_auto_shift_creation because it was found in queue for health
2025-06-19 10:19:09,143 ERROR scheduler Skipped queueing frappe.core.doctype.log_settings.log_settings.run_log_clean_up because it was found in queue for health
2025-06-19 10:19:09,148 ERROR scheduler Skipped queueing frappe.integrations.doctype.dropbox_settings.dropbox_settings.take_backups_daily because it was found in queue for health
2025-06-19 10:19:09,151 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for health
2025-06-19 10:19:09,155 ERROR scheduler Skipped queueing hrms.hr.utils.generate_leave_encashment because it was found in queue for health
2025-06-19 10:19:09,160 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for health
2025-06-19 10:19:09,167 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for health
2025-06-19 10:19:09,180 ERROR scheduler Skipped queueing erpnext.manufacturing.doctype.bom_update_log.bom_update_log.resume_bom_cost_update_jobs because it was found in queue for health
2025-06-19 10:19:09,208 ERROR scheduler Skipped queueing erpnext.accounts.doctype.process_subscription.process_subscription.create_subscription_process because it was found in queue for health
2025-06-19 10:19:09,211 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.process_auto_attendance_for_all_shifts because it was found in queue for health
2025-06-19 10:19:09,222 ERROR scheduler Skipped queueing frappe.desk.notifications.clear_notifications because it was found in queue for health
2025-06-19 10:20:10,081 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.process_auto_attendance_for_all_shifts because it was found in queue for health
2025-06-19 10:20:10,084 ERROR scheduler Skipped queueing frappe.model.utils.link_count.update_link_count because it was found in queue for health
2025-06-19 10:20:10,100 ERROR scheduler Skipped queueing erpnext.assets.doctype.asset.depreciation.post_depreciation_entries because it was found in queue for health
2025-06-19 10:20:10,108 ERROR scheduler Skipped queueing frappe.integrations.doctype.dropbox_settings.dropbox_settings.take_backups_daily because it was found in queue for health
2025-06-19 10:20:10,111 ERROR scheduler Skipped queueing frappe.social.doctype.energy_point_settings.energy_point_settings.allocate_review_points because it was found in queue for health
2025-06-19 10:20:10,113 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.update_last_sync_of_checkin because it was found in queue for health
2025-06-19 10:20:10,126 ERROR scheduler Skipped queueing healthcare.healthcare.doctype.patient_appointment.patient_appointment.send_appointment_reminder because it was found in queue for health
2025-06-19 10:20:10,132 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for health
2025-06-19 10:20:10,139 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for health
2025-06-19 10:20:10,155 ERROR scheduler Skipped queueing frappe.automation.doctype.auto_repeat.auto_repeat.make_auto_repeat_entry because it was found in queue for health
2025-06-19 10:20:10,166 ERROR scheduler Skipped queueing hrms.hr.utils.generate_leave_encashment because it was found in queue for health
2025-06-19 10:20:10,170 ERROR scheduler Skipped queueing erpnext.manufacturing.doctype.bom_update_tool.bom_update_tool.auto_update_latest_price_in_all_boms because it was found in queue for health
2025-06-19 10:20:10,183 ERROR scheduler Skipped queueing frappe.automation.doctype.reminder.reminder.send_reminders because it was found in queue for health
2025-06-19 10:20:10,198 ERROR scheduler Skipped queueing frappe.utils.global_search.sync_global_search because it was found in queue for health
2025-06-19 10:20:10,202 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.remove_unverified_record because it was found in queue for health
2025-06-19 10:20:10,212 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_contacts.google_contacts.sync because it was found in queue for health
2025-06-19 10:20:10,215 ERROR scheduler Skipped queueing erpnext.setup.doctype.email_digest.email_digest.send because it was found in queue for health
2025-06-19 10:20:10,227 ERROR scheduler Skipped queueing hrms.hr.doctype.interview.interview.send_interview_reminder because it was found in queue for health
2025-06-19 10:20:10,230 ERROR scheduler Skipped queueing hrms.hr.utils.allocate_earned_leaves because it was found in queue for health
2025-06-19 10:20:10,232 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for health
2025-06-19 10:20:10,249 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for health
2025-06-19 10:20:10,266 ERROR scheduler Skipped queueing erpnext.accounts.doctype.process_subscription.process_subscription.create_subscription_process because it was found in queue for health
2025-06-19 10:20:10,269 ERROR scheduler Skipped queueing frappe.integrations.doctype.s3_backup_settings.s3_backup_settings.take_backups_daily because it was found in queue for health
2025-06-19 10:20:10,284 ERROR scheduler Skipped queueing erpnext.manufacturing.doctype.bom_update_log.bom_update_log.resume_bom_cost_update_jobs because it was found in queue for health
2025-06-19 10:20:10,287 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for health
2025-06-19 10:20:10,296 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for health
2025-06-19 10:20:10,310 ERROR scheduler Skipped queueing csf_tz.stanbic.sftp.sync_all_stanbank_files because it was found in queue for health
2025-06-19 10:20:10,315 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_drive.google_drive.daily_backup because it was found in queue for health
2025-06-19 10:20:10,317 ERROR scheduler Skipped queueing frappe.desk.notifications.clear_notifications because it was found in queue for health
2025-06-19 10:20:10,322 ERROR scheduler Skipped queueing frappe.sessions.clear_expired_sessions because it was found in queue for health
2025-06-19 10:20:10,325 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for health
2025-06-19 10:20:10,331 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for health
2025-06-19 10:20:10,334 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for health
2025-06-19 10:20:10,337 ERROR scheduler Skipped queueing frappe.core.doctype.log_settings.log_settings.run_log_clean_up because it was found in queue for health
2025-06-19 10:20:10,355 ERROR scheduler Skipped queueing erpnext.crm.utils.open_leads_opportunities_based_on_todays_event because it was found in queue for health
2025-06-19 10:20:10,358 ERROR scheduler Skipped queueing frappe.email.doctype.auto_email_report.auto_email_report.send_daily because it was found in queue for health
2025-06-19 10:20:10,364 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for health
2025-06-19 10:20:10,368 ERROR scheduler Skipped queueing hrms.hr.doctype.leave_ledger_entry.leave_ledger_entry.process_expired_allocation because it was found in queue for health
2025-06-19 10:20:10,388 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_schedule_assignment.shift_schedule_assignment.process_auto_shift_creation because it was found in queue for health
2025-06-19 10:21:10,868 ERROR scheduler Skipped queueing csf_tz.stanbic.sftp.sync_all_stanbank_files because it was found in queue for health
2025-06-19 10:21:10,899 ERROR scheduler Skipped queueing frappe.integrations.doctype.s3_backup_settings.s3_backup_settings.take_backups_daily because it was found in queue for health
2025-06-19 10:21:10,902 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for health
2025-06-19 10:21:10,907 ERROR scheduler Skipped queueing frappe.automation.doctype.auto_repeat.auto_repeat.make_auto_repeat_entry because it was found in queue for health
2025-06-19 10:21:10,910 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for health
2025-06-19 10:21:10,913 ERROR scheduler Skipped queueing frappe.integrations.doctype.dropbox_settings.dropbox_settings.take_backups_daily because it was found in queue for health
2025-06-19 10:21:10,919 ERROR scheduler Skipped queueing hms_tz.nhif.api.healthcare_utils.create_invoiced_items_if_not_created because it was found in queue for health
2025-06-19 10:21:10,927 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for health
2025-06-19 10:21:10,932 ERROR scheduler Skipped queueing frappe.email.queue.flush because it was found in queue for health
2025-06-19 10:21:10,939 ERROR scheduler Skipped queueing erpnext.crm.utils.open_leads_opportunities_based_on_todays_event because it was found in queue for health
2025-06-19 10:21:10,947 ERROR scheduler Skipped queueing erpnext.setup.doctype.email_digest.email_digest.send because it was found in queue for health
2025-06-19 10:21:10,950 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_schedule_assignment.shift_schedule_assignment.process_auto_shift_creation because it was found in queue for health
2025-06-19 10:21:10,958 ERROR scheduler Skipped queueing payments.payment_gateways.doctype.razorpay_settings.razorpay_settings.capture_payment because it was found in queue for health
2025-06-19 10:21:10,964 ERROR scheduler Skipped queueing hrms.hr.utils.generate_leave_encashment because it was found in queue for health
2025-06-19 10:21:10,967 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.update_last_sync_of_checkin because it was found in queue for health
2025-06-19 10:21:10,972 ERROR scheduler Skipped queueing hrms.hr.doctype.leave_ledger_entry.leave_ledger_entry.process_expired_allocation because it was found in queue for health
2025-06-19 10:21:10,975 ERROR scheduler Skipped queueing erpnext.assets.doctype.asset.depreciation.post_depreciation_entries because it was found in queue for health
2025-06-19 10:21:10,981 ERROR scheduler Skipped queueing frappe.social.doctype.energy_point_settings.energy_point_settings.allocate_review_points because it was found in queue for health
2025-06-19 10:21:10,985 ERROR scheduler Skipped queueing healthcare.healthcare.doctype.patient_appointment.patient_appointment.send_appointment_reminder because it was found in queue for health
2025-06-19 10:21:11,001 ERROR scheduler Skipped queueing frappe.core.doctype.log_settings.log_settings.run_log_clean_up because it was found in queue for health
2025-06-19 10:21:11,007 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for health
2025-06-19 10:21:11,012 ERROR scheduler Skipped queueing frappe.automation.doctype.reminder.reminder.send_reminders because it was found in queue for health
2025-06-19 10:21:11,027 ERROR scheduler Skipped queueing frappe.monitor.flush because it was found in queue for health
2025-06-19 10:21:11,038 ERROR scheduler Skipped queueing frappe.desk.notifications.clear_notifications because it was found in queue for health
2025-06-19 10:21:11,047 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for health
2025-06-19 10:21:11,050 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_contacts.google_contacts.sync because it was found in queue for health
2025-06-19 10:21:11,057 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for health
2025-06-19 10:21:11,063 ERROR scheduler Skipped queueing frappe.sessions.clear_expired_sessions because it was found in queue for health
2025-06-19 10:21:11,071 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for health
2025-06-19 10:21:11,076 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for health
2025-06-19 10:21:11,080 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for health
2025-06-19 10:21:11,085 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.pull because it was found in queue for health
2025-06-19 10:21:11,088 ERROR scheduler Skipped queueing erpnext.manufacturing.doctype.bom_update_log.bom_update_log.resume_bom_cost_update_jobs because it was found in queue for health
2025-06-19 10:21:11,092 ERROR scheduler Skipped queueing erpnext.accounts.doctype.process_subscription.process_subscription.create_subscription_process because it was found in queue for health
2025-06-19 10:21:11,101 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_calendar.google_calendar.sync because it was found in queue for health
2025-06-19 10:21:11,123 ERROR scheduler Skipped queueing frappe.utils.global_search.sync_global_search because it was found in queue for health
2025-06-19 10:21:11,126 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.process_auto_attendance_for_all_shifts because it was found in queue for health
2025-06-19 10:21:11,130 ERROR scheduler Skipped queueing hrms.hr.utils.allocate_earned_leaves because it was found in queue for health
2025-06-19 10:21:11,133 ERROR scheduler Skipped queueing frappe.model.utils.link_count.update_link_count because it was found in queue for health
2025-06-19 10:21:11,146 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for health
2025-06-19 10:21:11,151 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.remove_unverified_record because it was found in queue for health
2025-06-19 10:21:11,155 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_drive.google_drive.daily_backup because it was found in queue for health
2025-06-19 10:21:11,161 ERROR scheduler Skipped queueing hrms.hr.doctype.interview.interview.send_interview_reminder because it was found in queue for health
2025-06-19 10:21:11,164 ERROR scheduler Skipped queueing frappe.email.doctype.auto_email_report.auto_email_report.send_daily because it was found in queue for health
2025-06-19 10:21:11,169 ERROR scheduler Skipped queueing erpnext.manufacturing.doctype.bom_update_tool.bom_update_tool.auto_update_latest_price_in_all_boms because it was found in queue for health
2025-06-19 10:22:11,926 ERROR scheduler Skipped queueing frappe.monitor.flush because it was found in queue for health
2025-06-19 10:22:11,931 ERROR scheduler Skipped queueing frappe.sessions.clear_expired_sessions because it was found in queue for health
2025-06-19 10:22:11,940 ERROR scheduler Skipped queueing frappe.integrations.doctype.s3_backup_settings.s3_backup_settings.take_backups_daily because it was found in queue for health
2025-06-19 10:22:11,942 ERROR scheduler Skipped queueing frappe.automation.doctype.auto_repeat.auto_repeat.make_auto_repeat_entry because it was found in queue for health
2025-06-19 10:22:11,945 ERROR scheduler Skipped queueing erpnext.setup.doctype.email_digest.email_digest.send because it was found in queue for health
2025-06-19 10:22:11,948 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for health
2025-06-19 10:22:11,953 ERROR scheduler Skipped queueing frappe.social.doctype.energy_point_settings.energy_point_settings.allocate_review_points because it was found in queue for health
2025-06-19 10:22:11,958 ERROR scheduler Skipped queueing frappe.email.doctype.auto_email_report.auto_email_report.send_daily because it was found in queue for health
2025-06-19 10:22:11,968 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_schedule_assignment.shift_schedule_assignment.process_auto_shift_creation because it was found in queue for health
2025-06-19 10:22:11,987 ERROR scheduler Skipped queueing frappe.desk.notifications.clear_notifications because it was found in queue for health
2025-06-19 10:22:11,991 ERROR scheduler Skipped queueing frappe.integrations.doctype.dropbox_settings.dropbox_settings.take_backups_daily because it was found in queue for health
2025-06-19 10:22:12,002 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_calendar.google_calendar.sync because it was found in queue for health
2025-06-19 10:22:12,005 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for health
2025-06-19 10:22:12,022 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for health
2025-06-19 10:22:12,025 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for health
2025-06-19 10:22:12,031 ERROR scheduler Skipped queueing payments.payment_gateways.doctype.razorpay_settings.razorpay_settings.capture_payment because it was found in queue for health
2025-06-19 10:22:12,034 ERROR scheduler Skipped queueing healthcare.healthcare.doctype.patient_appointment.patient_appointment.send_appointment_reminder because it was found in queue for health
2025-06-19 10:22:12,037 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for health
2025-06-19 10:22:12,040 ERROR scheduler Skipped queueing erpnext.crm.utils.open_leads_opportunities_based_on_todays_event because it was found in queue for health
2025-06-19 10:22:12,048 ERROR scheduler Skipped queueing hrms.hr.doctype.interview.interview.send_interview_reminder because it was found in queue for health
2025-06-19 10:22:12,051 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_drive.google_drive.daily_backup because it was found in queue for health
2025-06-19 10:22:12,057 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.process_auto_attendance_for_all_shifts because it was found in queue for health
2025-06-19 10:22:12,064 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for health
2025-06-19 10:22:12,069 ERROR scheduler Skipped queueing hms_tz.nhif.api.healthcare_utils.create_invoiced_items_if_not_created because it was found in queue for health
2025-06-19 10:22:12,077 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_contacts.google_contacts.sync because it was found in queue for health
2025-06-19 10:22:12,088 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for health
2025-06-19 10:22:12,091 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.update_last_sync_of_checkin because it was found in queue for health
2025-06-19 10:22:12,111 ERROR scheduler Skipped queueing erpnext.accounts.doctype.process_subscription.process_subscription.create_subscription_process because it was found in queue for health
2025-06-19 10:22:12,115 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for health
2025-06-19 10:22:12,118 ERROR scheduler Skipped queueing hrms.hr.utils.allocate_earned_leaves because it was found in queue for health
2025-06-19 10:22:12,123 ERROR scheduler Skipped queueing frappe.email.queue.flush because it was found in queue for health
2025-06-19 10:22:12,146 ERROR scheduler Skipped queueing hrms.hr.utils.generate_leave_encashment because it was found in queue for health
2025-06-19 10:22:12,153 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for health
2025-06-19 10:22:12,168 ERROR scheduler Skipped queueing frappe.core.doctype.log_settings.log_settings.run_log_clean_up because it was found in queue for health
2025-06-19 10:22:12,171 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.remove_unverified_record because it was found in queue for health
2025-06-19 10:22:12,174 ERROR scheduler Skipped queueing erpnext.assets.doctype.asset.depreciation.post_depreciation_entries because it was found in queue for health
2025-06-19 10:22:12,177 ERROR scheduler Skipped queueing hrms.hr.doctype.leave_ledger_entry.leave_ledger_entry.process_expired_allocation because it was found in queue for health
2025-06-19 10:22:12,197 ERROR scheduler Skipped queueing erpnext.manufacturing.doctype.bom_update_tool.bom_update_tool.auto_update_latest_price_in_all_boms because it was found in queue for health
2025-06-19 10:22:12,216 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for health
2025-06-19 10:23:12,537 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for health
2025-06-19 10:23:12,552 ERROR scheduler Skipped queueing erpnext.crm.utils.open_leads_opportunities_based_on_todays_event because it was found in queue for health
2025-06-19 10:23:12,559 ERROR scheduler Skipped queueing erpnext.accounts.doctype.process_subscription.process_subscription.create_subscription_process because it was found in queue for health
2025-06-19 10:23:12,596 ERROR scheduler Skipped queueing frappe.integrations.doctype.s3_backup_settings.s3_backup_settings.take_backups_daily because it was found in queue for health
2025-06-19 10:23:12,615 ERROR scheduler Skipped queueing frappe.integrations.doctype.dropbox_settings.dropbox_settings.take_backups_daily because it was found in queue for health
2025-06-19 10:23:12,618 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for health
2025-06-19 10:23:12,637 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_contacts.google_contacts.sync because it was found in queue for health
2025-06-19 10:23:12,643 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for health
2025-06-19 10:23:12,659 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for health
2025-06-19 10:23:12,665 ERROR scheduler Skipped queueing frappe.automation.doctype.auto_repeat.auto_repeat.make_auto_repeat_entry because it was found in queue for health
2025-06-19 10:23:12,668 ERROR scheduler Skipped queueing frappe.sessions.clear_expired_sessions because it was found in queue for health
2025-06-19 10:23:12,672 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for health
2025-06-19 10:23:12,680 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.process_auto_attendance_for_all_shifts because it was found in queue for health
2025-06-19 10:23:12,688 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for health
2025-06-19 10:23:12,722 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_drive.google_drive.daily_backup because it was found in queue for health
2025-06-19 10:23:12,732 ERROR scheduler Skipped queueing frappe.email.doctype.auto_email_report.auto_email_report.send_daily because it was found in queue for health
2025-06-19 10:23:12,740 ERROR scheduler Skipped queueing hrms.hr.doctype.leave_ledger_entry.leave_ledger_entry.process_expired_allocation because it was found in queue for health
2025-06-19 10:23:12,743 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for health
2025-06-19 10:23:12,747 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for health
2025-06-19 10:23:12,755 ERROR scheduler Skipped queueing frappe.social.doctype.energy_point_settings.energy_point_settings.allocate_review_points because it was found in queue for health
2025-06-19 10:23:12,763 ERROR scheduler Skipped queueing hrms.hr.utils.allocate_earned_leaves because it was found in queue for health
2025-06-19 10:23:12,773 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.remove_unverified_record because it was found in queue for health
2025-06-19 10:23:12,791 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.update_last_sync_of_checkin because it was found in queue for health
2025-06-19 10:23:12,803 ERROR scheduler Skipped queueing frappe.desk.notifications.clear_notifications because it was found in queue for health
2025-06-19 10:23:12,830 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for health
2025-06-19 10:23:12,833 ERROR scheduler Skipped queueing erpnext.manufacturing.doctype.bom_update_tool.bom_update_tool.auto_update_latest_price_in_all_boms because it was found in queue for health
2025-06-19 10:23:12,878 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_schedule_assignment.shift_schedule_assignment.process_auto_shift_creation because it was found in queue for health
2025-06-19 10:23:12,888 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for health
2025-06-19 10:23:12,906 ERROR scheduler Skipped queueing hrms.hr.utils.generate_leave_encashment because it was found in queue for health
2025-06-19 10:24:13,799 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.remove_unverified_record because it was found in queue for health
2025-06-19 10:24:13,805 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for health
2025-06-19 10:24:13,810 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_drive.google_drive.daily_backup because it was found in queue for health
2025-06-19 10:24:13,815 ERROR scheduler Skipped queueing frappe.sessions.clear_expired_sessions because it was found in queue for health
2025-06-19 10:24:13,866 ERROR scheduler Skipped queueing frappe.social.doctype.energy_point_settings.energy_point_settings.allocate_review_points because it was found in queue for health
2025-06-19 10:24:13,876 ERROR scheduler Skipped queueing erpnext.crm.utils.open_leads_opportunities_based_on_todays_event because it was found in queue for health
2025-06-19 10:24:13,938 ERROR scheduler Skipped queueing hrms.hr.doctype.leave_ledger_entry.leave_ledger_entry.process_expired_allocation because it was found in queue for health
2025-06-19 10:24:13,942 ERROR scheduler Skipped queueing frappe.desk.notifications.clear_notifications because it was found in queue for health
2025-06-19 10:24:13,955 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for health
2025-06-19 10:24:13,968 ERROR scheduler Skipped queueing hrms.hr.utils.allocate_earned_leaves because it was found in queue for health
2025-06-19 10:24:13,977 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for health
2025-06-19 10:24:13,986 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.process_auto_attendance_for_all_shifts because it was found in queue for health
2025-06-19 10:24:14,037 ERROR scheduler Skipped queueing hrms.hr.utils.generate_leave_encashment because it was found in queue for health
2025-06-19 10:24:14,087 ERROR scheduler Skipped queueing erpnext.manufacturing.doctype.bom_update_tool.bom_update_tool.auto_update_latest_price_in_all_boms because it was found in queue for health
2025-06-19 10:24:14,100 ERROR scheduler Skipped queueing frappe.integrations.doctype.dropbox_settings.dropbox_settings.take_backups_daily because it was found in queue for health
2025-06-19 10:24:14,116 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for health
2025-06-19 10:24:14,120 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for health
2025-06-19 10:24:14,129 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for health
2025-06-19 10:24:14,139 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for health
2025-06-19 10:24:14,144 ERROR scheduler Skipped queueing frappe.automation.doctype.auto_repeat.auto_repeat.make_auto_repeat_entry because it was found in queue for health
2025-06-19 10:24:14,170 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for health
2025-06-19 10:24:14,190 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for health
2025-06-19 10:24:14,197 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for health
2025-06-19 10:24:14,224 ERROR scheduler Skipped queueing frappe.integrations.doctype.s3_backup_settings.s3_backup_settings.take_backups_daily because it was found in queue for health
2025-06-19 10:24:14,237 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_schedule_assignment.shift_schedule_assignment.process_auto_shift_creation because it was found in queue for health
2025-06-19 10:24:14,241 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.update_last_sync_of_checkin because it was found in queue for health
2025-06-19 10:24:14,246 ERROR scheduler Skipped queueing erpnext.accounts.doctype.process_subscription.process_subscription.create_subscription_process because it was found in queue for health
2025-06-19 10:24:14,255 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_contacts.google_contacts.sync because it was found in queue for health
2025-06-19 10:24:14,258 ERROR scheduler Skipped queueing frappe.email.doctype.auto_email_report.auto_email_report.send_daily because it was found in queue for health
2025-06-19 10:25:15,275 ERROR scheduler Skipped queueing hrms.hr.doctype.leave_ledger_entry.leave_ledger_entry.process_expired_allocation because it was found in queue for health
2025-06-19 10:25:15,290 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for health
2025-06-19 10:25:15,303 ERROR scheduler Skipped queueing frappe.sessions.clear_expired_sessions because it was found in queue for health
2025-06-19 10:25:15,309 ERROR scheduler Skipped queueing frappe.email.queue.flush because it was found in queue for health
2025-06-19 10:25:15,317 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for health
2025-06-19 10:25:15,332 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for health
2025-06-19 10:25:15,335 ERROR scheduler Skipped queueing hrms.hr.utils.allocate_earned_leaves because it was found in queue for health
2025-06-19 10:25:15,342 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_contacts.google_contacts.sync because it was found in queue for health
2025-06-19 10:25:15,360 ERROR scheduler Skipped queueing frappe.email.doctype.auto_email_report.auto_email_report.send_daily because it was found in queue for health
2025-06-19 10:25:15,367 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.update_last_sync_of_checkin because it was found in queue for health
2025-06-19 10:25:15,375 ERROR scheduler Skipped queueing frappe.desk.notifications.clear_notifications because it was found in queue for health
2025-06-19 10:25:15,387 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_calendar.google_calendar.sync because it was found in queue for health
2025-06-19 10:25:15,390 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for health
2025-06-19 10:25:15,400 ERROR scheduler Skipped queueing frappe.integrations.doctype.s3_backup_settings.s3_backup_settings.take_backups_daily because it was found in queue for health
2025-06-19 10:25:15,411 ERROR scheduler Skipped queueing erpnext.crm.utils.open_leads_opportunities_based_on_todays_event because it was found in queue for health
2025-06-19 10:25:15,424 ERROR scheduler Skipped queueing healthcare.healthcare.doctype.patient_appointment.patient_appointment.send_appointment_reminder because it was found in queue for health
2025-06-19 10:25:15,428 ERROR scheduler Skipped queueing hrms.hr.doctype.interview.interview.send_interview_reminder because it was found in queue for health
2025-06-19 10:25:15,434 ERROR scheduler Skipped queueing erpnext.accounts.doctype.process_subscription.process_subscription.create_subscription_process because it was found in queue for health
2025-06-19 10:25:15,440 ERROR scheduler Skipped queueing hrms.hr.utils.generate_leave_encashment because it was found in queue for health
2025-06-19 10:25:15,455 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_drive.google_drive.daily_backup because it was found in queue for health
2025-06-19 10:25:15,458 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for health
2025-06-19 10:25:15,466 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.process_auto_attendance_for_all_shifts because it was found in queue for health
2025-06-19 10:25:15,474 ERROR scheduler Skipped queueing erpnext.manufacturing.doctype.bom_update_tool.bom_update_tool.auto_update_latest_price_in_all_boms because it was found in queue for health
2025-06-19 10:25:15,480 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for health
2025-06-19 10:25:15,497 ERROR scheduler Skipped queueing frappe.social.doctype.energy_point_settings.energy_point_settings.allocate_review_points because it was found in queue for health
2025-06-19 10:25:15,501 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for health
2025-06-19 10:25:15,507 ERROR scheduler Skipped queueing payments.payment_gateways.doctype.razorpay_settings.razorpay_settings.capture_payment because it was found in queue for health
2025-06-19 10:25:15,525 ERROR scheduler Skipped queueing frappe.integrations.doctype.dropbox_settings.dropbox_settings.take_backups_daily because it was found in queue for health
2025-06-19 10:25:15,558 ERROR scheduler Skipped queueing frappe.automation.doctype.auto_repeat.auto_repeat.make_auto_repeat_entry because it was found in queue for health
2025-06-19 10:25:15,562 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.remove_unverified_record because it was found in queue for health
2025-06-19 10:25:15,571 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for health
2025-06-19 10:25:15,580 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_schedule_assignment.shift_schedule_assignment.process_auto_shift_creation because it was found in queue for health
2025-06-19 10:25:15,586 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for health
2025-06-19 10:25:15,605 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for health
2025-06-19 10:25:15,617 ERROR scheduler Skipped queueing frappe.monitor.flush because it was found in queue for health
2025-06-19 10:26:16,818 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for health
2025-06-19 10:26:16,822 ERROR scheduler Skipped queueing payments.payment_gateways.doctype.razorpay_settings.razorpay_settings.capture_payment because it was found in queue for health
2025-06-19 10:26:16,828 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for health
2025-06-19 10:26:16,843 ERROR scheduler Skipped queueing erpnext.accounts.doctype.process_subscription.process_subscription.create_subscription_process because it was found in queue for health
2025-06-19 10:26:16,852 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_contacts.google_contacts.sync because it was found in queue for health
2025-06-19 10:26:16,856 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.process_auto_attendance_for_all_shifts because it was found in queue for health
2025-06-19 10:26:16,873 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_drive.google_drive.daily_backup because it was found in queue for health
2025-06-19 10:26:16,880 ERROR scheduler Skipped queueing frappe.desk.notifications.clear_notifications because it was found in queue for health
2025-06-19 10:26:16,887 ERROR scheduler Skipped queueing frappe.integrations.doctype.s3_backup_settings.s3_backup_settings.take_backups_daily because it was found in queue for health
2025-06-19 10:26:16,909 ERROR scheduler Skipped queueing frappe.social.doctype.energy_point_settings.energy_point_settings.allocate_review_points because it was found in queue for health
2025-06-19 10:26:16,919 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for health
2025-06-19 10:26:16,931 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for health
2025-06-19 10:26:16,936 ERROR scheduler Skipped queueing frappe.email.doctype.auto_email_report.auto_email_report.send_daily because it was found in queue for health
2025-06-19 10:26:16,957 ERROR scheduler Skipped queueing hrms.hr.utils.allocate_earned_leaves because it was found in queue for health
2025-06-19 10:26:16,961 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_schedule_assignment.shift_schedule_assignment.process_auto_shift_creation because it was found in queue for health
2025-06-19 10:26:16,965 ERROR scheduler Skipped queueing hrms.hr.utils.generate_leave_encashment because it was found in queue for health
2025-06-19 10:26:16,976 ERROR scheduler Skipped queueing frappe.automation.doctype.auto_repeat.auto_repeat.make_auto_repeat_entry because it was found in queue for health
2025-06-19 10:26:16,987 ERROR scheduler Skipped queueing erpnext.manufacturing.doctype.bom_update_tool.bom_update_tool.auto_update_latest_price_in_all_boms because it was found in queue for health
2025-06-19 10:26:16,999 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for health
2025-06-19 10:26:17,014 ERROR scheduler Skipped queueing frappe.sessions.clear_expired_sessions because it was found in queue for health
2025-06-19 10:26:17,027 ERROR scheduler Skipped queueing frappe.email.queue.flush because it was found in queue for health
2025-06-19 10:26:17,040 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for health
2025-06-19 10:26:17,053 ERROR scheduler Skipped queueing hrms.hr.doctype.interview.interview.send_interview_reminder because it was found in queue for health
2025-06-19 10:26:17,057 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for health
2025-06-19 10:26:17,067 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for health
2025-06-19 10:26:17,071 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.remove_unverified_record because it was found in queue for health
2025-06-19 10:26:17,078 ERROR scheduler Skipped queueing frappe.integrations.doctype.dropbox_settings.dropbox_settings.take_backups_daily because it was found in queue for health
2025-06-19 10:26:17,093 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.update_last_sync_of_checkin because it was found in queue for health
2025-06-19 10:26:17,098 ERROR scheduler Skipped queueing healthcare.healthcare.doctype.patient_appointment.patient_appointment.send_appointment_reminder because it was found in queue for health
2025-06-19 10:26:17,104 ERROR scheduler Skipped queueing erpnext.crm.utils.open_leads_opportunities_based_on_todays_event because it was found in queue for health
2025-06-19 10:26:17,115 ERROR scheduler Skipped queueing hrms.hr.doctype.leave_ledger_entry.leave_ledger_entry.process_expired_allocation because it was found in queue for health
2025-06-19 10:26:17,126 ERROR scheduler Skipped queueing frappe.monitor.flush because it was found in queue for health
2025-06-19 10:26:17,149 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_calendar.google_calendar.sync because it was found in queue for health
2025-06-19 10:26:17,153 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for health
2025-06-19 10:26:17,170 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for health
2025-06-19 10:27:17,774 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for health
2025-06-19 10:27:17,784 ERROR scheduler Skipped queueing payments.payment_gateways.doctype.razorpay_settings.razorpay_settings.capture_payment because it was found in queue for health
2025-06-19 10:27:17,794 ERROR scheduler Skipped queueing hrms.hr.utils.generate_leave_encashment because it was found in queue for health
2025-06-19 10:27:17,809 ERROR scheduler Skipped queueing frappe.email.queue.flush because it was found in queue for health
2025-06-19 10:27:17,837 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for health
2025-06-19 10:27:17,853 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.process_auto_attendance_for_all_shifts because it was found in queue for health
2025-06-19 10:27:17,882 ERROR scheduler Skipped queueing frappe.social.doctype.energy_point_settings.energy_point_settings.allocate_review_points because it was found in queue for health
2025-06-19 10:27:17,888 ERROR scheduler Skipped queueing erpnext.crm.utils.open_leads_opportunities_based_on_todays_event because it was found in queue for health
2025-06-19 10:27:17,903 ERROR scheduler Skipped queueing frappe.integrations.doctype.s3_backup_settings.s3_backup_settings.take_backups_daily because it was found in queue for health
2025-06-19 10:27:17,908 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for health
2025-06-19 10:27:17,917 ERROR scheduler Skipped queueing erpnext.accounts.doctype.process_subscription.process_subscription.create_subscription_process because it was found in queue for health
2025-06-19 10:27:17,929 ERROR scheduler Skipped queueing frappe.integrations.doctype.dropbox_settings.dropbox_settings.take_backups_daily because it was found in queue for health
2025-06-19 10:27:17,942 ERROR scheduler Skipped queueing hrms.hr.doctype.interview.interview.send_interview_reminder because it was found in queue for health
2025-06-19 10:27:17,959 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for health
2025-06-19 10:27:17,967 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for health
2025-06-19 10:27:17,971 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.update_last_sync_of_checkin because it was found in queue for health
2025-06-19 10:27:17,985 ERROR scheduler Skipped queueing frappe.email.doctype.auto_email_report.auto_email_report.send_daily because it was found in queue for health
2025-06-19 10:27:18,001 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for health
2025-06-19 10:27:18,015 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for health
2025-06-19 10:27:18,019 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for health
2025-06-19 10:27:18,023 ERROR scheduler Skipped queueing hrms.hr.utils.allocate_earned_leaves because it was found in queue for health
2025-06-19 10:27:18,033 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for health
2025-06-19 10:27:18,044 ERROR scheduler Skipped queueing erpnext.manufacturing.doctype.bom_update_tool.bom_update_tool.auto_update_latest_price_in_all_boms because it was found in queue for health
2025-06-19 10:27:18,049 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_calendar.google_calendar.sync because it was found in queue for health
2025-06-19 10:27:18,053 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_drive.google_drive.daily_backup because it was found in queue for health
2025-06-19 10:27:18,068 ERROR scheduler Skipped queueing frappe.sessions.clear_expired_sessions because it was found in queue for health
2025-06-19 10:27:18,073 ERROR scheduler Skipped queueing frappe.monitor.flush because it was found in queue for health
2025-06-19 10:27:18,077 ERROR scheduler Skipped queueing hrms.hr.doctype.leave_ledger_entry.leave_ledger_entry.process_expired_allocation because it was found in queue for health
2025-06-19 10:27:18,081 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.remove_unverified_record because it was found in queue for health
2025-06-19 10:27:18,088 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for health
2025-06-19 10:27:18,099 ERROR scheduler Skipped queueing frappe.desk.notifications.clear_notifications because it was found in queue for health
2025-06-19 10:27:18,114 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_contacts.google_contacts.sync because it was found in queue for health
2025-06-19 10:27:18,124 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_schedule_assignment.shift_schedule_assignment.process_auto_shift_creation because it was found in queue for health
2025-06-19 10:27:18,128 ERROR scheduler Skipped queueing frappe.automation.doctype.auto_repeat.auto_repeat.make_auto_repeat_entry because it was found in queue for health
2025-06-19 10:27:18,136 ERROR scheduler Skipped queueing healthcare.healthcare.doctype.patient_appointment.patient_appointment.send_appointment_reminder because it was found in queue for health
2025-06-19 10:28:19,268 ERROR scheduler Skipped queueing erpnext.manufacturing.doctype.bom_update_tool.bom_update_tool.auto_update_latest_price_in_all_boms because it was found in queue for health
2025-06-19 10:28:19,277 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for health
2025-06-19 10:28:19,297 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.update_last_sync_of_checkin because it was found in queue for health
2025-06-19 10:28:19,308 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for health
2025-06-19 10:28:19,317 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.remove_unverified_record because it was found in queue for health
2025-06-19 10:28:19,339 ERROR scheduler Skipped queueing frappe.integrations.doctype.dropbox_settings.dropbox_settings.take_backups_daily because it was found in queue for health
2025-06-19 10:28:19,348 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.process_auto_attendance_for_all_shifts because it was found in queue for health
2025-06-19 10:28:19,351 ERROR scheduler Skipped queueing frappe.sessions.clear_expired_sessions because it was found in queue for health
2025-06-19 10:28:19,359 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for health
2025-06-19 10:28:19,407 ERROR scheduler Skipped queueing frappe.automation.doctype.auto_repeat.auto_repeat.make_auto_repeat_entry because it was found in queue for health
2025-06-19 10:28:19,447 ERROR scheduler Skipped queueing hrms.hr.utils.allocate_earned_leaves because it was found in queue for health
2025-06-19 10:28:19,450 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_contacts.google_contacts.sync because it was found in queue for health
2025-06-19 10:28:19,461 ERROR scheduler Skipped queueing erpnext.crm.utils.open_leads_opportunities_based_on_todays_event because it was found in queue for health
2025-06-19 10:28:19,477 ERROR scheduler Skipped queueing frappe.email.doctype.auto_email_report.auto_email_report.send_daily because it was found in queue for health
2025-06-19 10:28:19,496 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for health
2025-06-19 10:28:19,499 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for health
2025-06-19 10:28:19,505 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for health
2025-06-19 10:28:19,510 ERROR scheduler Skipped queueing frappe.desk.notifications.clear_notifications because it was found in queue for health
2025-06-19 10:28:19,537 ERROR scheduler Skipped queueing hrms.hr.doctype.leave_ledger_entry.leave_ledger_entry.process_expired_allocation because it was found in queue for health
2025-06-19 10:29:20,442 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for health
2025-06-19 10:29:20,495 ERROR scheduler Skipped queueing frappe.email.doctype.auto_email_report.auto_email_report.send_daily because it was found in queue for health
2025-06-19 10:29:20,508 ERROR scheduler Skipped queueing frappe.sessions.clear_expired_sessions because it was found in queue for health
2025-06-19 10:29:20,552 ERROR scheduler Skipped queueing frappe.integrations.doctype.dropbox_settings.dropbox_settings.take_backups_daily because it was found in queue for health
2025-06-19 10:29:20,572 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.remove_unverified_record because it was found in queue for health
2025-06-19 10:29:20,643 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for health
2025-06-19 10:29:20,653 ERROR scheduler Skipped queueing frappe.automation.doctype.auto_repeat.auto_repeat.make_auto_repeat_entry because it was found in queue for health
2025-06-19 10:29:20,664 ERROR scheduler Skipped queueing hrms.hr.utils.allocate_earned_leaves because it was found in queue for health
2025-06-19 10:29:20,694 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_contacts.google_contacts.sync because it was found in queue for health
2025-06-19 10:29:20,722 ERROR scheduler Skipped queueing erpnext.manufacturing.doctype.bom_update_tool.bom_update_tool.auto_update_latest_price_in_all_boms because it was found in queue for health
2025-06-19 10:29:20,726 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for health
2025-06-19 10:30:22,054 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.remove_unverified_record because it was found in queue for health
2025-06-19 10:30:22,062 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_contacts.google_contacts.sync because it was found in queue for health
2025-06-19 10:30:22,066 ERROR scheduler Skipped queueing frappe.automation.doctype.auto_repeat.auto_repeat.make_auto_repeat_entry because it was found in queue for health
2025-06-19 10:30:22,089 ERROR scheduler Skipped queueing hrms.hr.utils.allocate_earned_leaves because it was found in queue for health
2025-06-19 10:30:22,103 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for health
2025-06-19 10:30:22,127 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for health
2025-06-19 10:30:22,152 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for health
2025-06-19 10:30:22,207 ERROR scheduler Skipped queueing frappe.integrations.doctype.dropbox_settings.dropbox_settings.take_backups_daily because it was found in queue for health
2025-06-19 10:30:22,237 ERROR scheduler Skipped queueing frappe.email.doctype.auto_email_report.auto_email_report.send_daily because it was found in queue for health
2025-06-19 10:30:22,310 ERROR scheduler Skipped queueing frappe.sessions.clear_expired_sessions because it was found in queue for health
2025-06-19 10:30:22,313 ERROR scheduler Skipped queueing erpnext.manufacturing.doctype.bom_update_tool.bom_update_tool.auto_update_latest_price_in_all_boms because it was found in queue for health
2025-06-19 10:31:23,125 ERROR scheduler Skipped queueing frappe.automation.doctype.auto_repeat.auto_repeat.make_auto_repeat_entry because it was found in queue for health
2025-06-19 10:31:23,144 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.pull because it was found in queue for health
2025-06-19 10:31:23,151 ERROR scheduler Skipped queueing frappe.integrations.doctype.dropbox_settings.dropbox_settings.take_backups_daily because it was found in queue for health
2025-06-19 10:31:23,188 ERROR scheduler Skipped queueing csf_tz.stanbic.sftp.sync_all_stanbank_files because it was found in queue for health
2025-06-19 10:31:23,194 ERROR scheduler Skipped queueing frappe.deferred_insert.save_to_db because it was found in queue for health
2025-06-19 10:31:23,198 ERROR scheduler Skipped queueing erpnext.accounts.doctype.process_payment_reconciliation.process_payment_reconciliation.trigger_reconciliation_for_queued_docs because it was found in queue for health
2025-06-19 10:31:23,225 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for health
2025-06-19 10:31:23,231 ERROR scheduler Skipped queueing csf_tz.stanbic.sftp.process_download_files because it was found in queue for health
2025-06-19 10:31:23,254 ERROR scheduler Skipped queueing erpnext.utilities.doctype.video.video.update_youtube_data because it was found in queue for health
2025-06-19 10:31:23,272 ERROR scheduler Skipped queueing hrms.hr.utils.allocate_earned_leaves because it was found in queue for health
2025-06-19 10:31:23,276 ERROR scheduler Skipped queueing erpnext.accounts.doctype.gl_entry.gl_entry.rename_gle_sle_docs because it was found in queue for health
2025-06-19 10:31:23,295 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.notify_unreplied because it was found in queue for health
2025-06-19 10:31:23,316 ERROR scheduler Skipped queueing erpnext.manufacturing.doctype.bom_update_tool.bom_update_tool.auto_update_latest_price_in_all_boms because it was found in queue for health
2025-06-19 10:31:23,320 ERROR scheduler Skipped queueing frappe.automation.doctype.reminder.reminder.send_reminders because it was found in queue for health
2025-06-19 10:31:23,326 ERROR scheduler Skipped queueing erpnext.manufacturing.doctype.bom_update_log.bom_update_log.resume_bom_cost_update_jobs because it was found in queue for health
2025-06-19 10:31:23,330 ERROR scheduler Skipped queueing frappe.sessions.clear_expired_sessions because it was found in queue for health
2025-06-19 10:31:23,336 ERROR scheduler Skipped queueing frappe.email.doctype.auto_email_report.auto_email_report.send_daily because it was found in queue for health
2025-06-19 10:31:23,375 ERROR scheduler Skipped queueing hms_tz.nhif.api.healthcare_utils.create_invoiced_items_if_not_created because it was found in queue for health
2025-06-19 10:31:23,378 ERROR scheduler Skipped queueing frappe.model.utils.link_count.update_link_count because it was found in queue for health
2025-06-19 10:31:23,396 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for health
2025-06-19 10:31:23,406 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.remove_unverified_record because it was found in queue for health
2025-06-19 10:31:23,418 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for health
2025-06-19 10:31:23,422 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_contacts.google_contacts.sync because it was found in queue for health
2025-06-19 10:31:23,441 ERROR scheduler Skipped queueing csf_tz.csftz_hooks.items_revaluation.process_incorrect_balance_qty because it was found in queue for health
2025-06-19 10:32:24,275 ERROR scheduler Skipped queueing frappe.automation.doctype.auto_repeat.auto_repeat.make_auto_repeat_entry because it was found in queue for health
2025-06-19 10:32:24,318 ERROR scheduler Skipped queueing hms_tz.nhif.api.healthcare_utils.create_invoiced_items_if_not_created because it was found in queue for health
2025-06-19 10:32:24,342 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for health
2025-06-19 10:32:24,345 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.remove_unverified_record because it was found in queue for health
2025-06-19 10:32:24,355 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for health
2025-06-19 10:32:24,373 ERROR scheduler Skipped queueing frappe.integrations.doctype.dropbox_settings.dropbox_settings.take_backups_daily because it was found in queue for health
2025-06-19 10:32:24,376 ERROR scheduler Skipped queueing frappe.deferred_insert.save_to_db because it was found in queue for health
2025-06-19 10:32:24,381 ERROR scheduler Skipped queueing frappe.sessions.clear_expired_sessions because it was found in queue for health
2025-06-19 10:32:24,386 ERROR scheduler Skipped queueing erpnext.utilities.doctype.video.video.update_youtube_data because it was found in queue for health
2025-06-19 10:32:24,389 ERROR scheduler Skipped queueing frappe.automation.doctype.reminder.reminder.send_reminders because it was found in queue for health
2025-06-19 10:32:24,398 ERROR scheduler Skipped queueing erpnext.manufacturing.doctype.bom_update_tool.bom_update_tool.auto_update_latest_price_in_all_boms because it was found in queue for health
2025-06-19 10:32:24,410 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_contacts.google_contacts.sync because it was found in queue for health
2025-06-19 10:32:24,435 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for health
2025-06-19 10:32:24,455 ERROR scheduler Skipped queueing erpnext.accounts.doctype.process_payment_reconciliation.process_payment_reconciliation.trigger_reconciliation_for_queued_docs because it was found in queue for health
2025-06-19 10:32:24,475 ERROR scheduler Skipped queueing hrms.hr.utils.allocate_earned_leaves because it was found in queue for health
2025-06-19 10:32:24,478 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.pull because it was found in queue for health
2025-06-19 10:32:24,508 ERROR scheduler Skipped queueing erpnext.accounts.doctype.gl_entry.gl_entry.rename_gle_sle_docs because it was found in queue for health
2025-06-19 10:32:24,524 ERROR scheduler Skipped queueing frappe.utils.global_search.sync_global_search because it was found in queue for health
2025-06-19 10:32:24,529 ERROR scheduler Skipped queueing csf_tz.csftz_hooks.items_revaluation.process_incorrect_balance_qty because it was found in queue for health
2025-06-19 10:32:24,539 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.notify_unreplied because it was found in queue for health
2025-06-19 10:32:24,561 ERROR scheduler Skipped queueing frappe.email.doctype.auto_email_report.auto_email_report.send_daily because it was found in queue for health
2025-06-19 10:32:24,569 ERROR scheduler Skipped queueing csf_tz.stanbic.sftp.process_download_files because it was found in queue for health
2025-06-19 10:33:25,403 ERROR scheduler Skipped queueing csf_tz.stanbic.sftp.process_download_files because it was found in queue for health
2025-06-19 10:33:25,420 ERROR scheduler Skipped queueing erpnext.utilities.doctype.video.video.update_youtube_data because it was found in queue for health
2025-06-19 10:33:25,423 ERROR scheduler Skipped queueing frappe.email.queue.flush because it was found in queue for health
2025-06-19 10:33:25,436 ERROR scheduler Skipped queueing csf_tz.stanbic.sftp.sync_all_stanbank_files because it was found in queue for health
2025-06-19 10:33:25,445 ERROR scheduler Skipped queueing frappe.email.doctype.auto_email_report.auto_email_report.send_daily because it was found in queue for health
2025-06-19 10:33:25,450 ERROR scheduler Skipped queueing hrms.hr.utils.allocate_earned_leaves because it was found in queue for health
2025-06-19 10:33:25,453 ERROR scheduler Skipped queueing hrms.hr.doctype.interview.interview.send_interview_reminder because it was found in queue for health
2025-06-19 10:33:25,461 ERROR scheduler Skipped queueing erpnext.manufacturing.doctype.bom_update_log.bom_update_log.resume_bom_cost_update_jobs because it was found in queue for health
2025-06-19 10:33:25,469 ERROR scheduler Skipped queueing frappe.monitor.flush because it was found in queue for health
2025-06-19 10:33:25,497 ERROR scheduler Skipped queueing frappe.model.utils.link_count.update_link_count because it was found in queue for health
2025-06-19 10:33:25,510 ERROR scheduler Skipped queueing erpnext.accounts.doctype.gl_entry.gl_entry.rename_gle_sle_docs because it was found in queue for health
2025-06-19 10:33:25,520 ERROR scheduler Skipped queueing healthcare.healthcare.doctype.patient_appointment.patient_appointment.send_appointment_reminder because it was found in queue for health
2025-06-19 10:33:25,527 ERROR scheduler Skipped queueing frappe.integrations.doctype.dropbox_settings.dropbox_settings.take_backups_daily because it was found in queue for health
2025-06-19 10:33:25,535 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for health
2025-06-19 10:33:25,549 ERROR scheduler Skipped queueing frappe.automation.doctype.auto_repeat.auto_repeat.make_auto_repeat_entry because it was found in queue for health
2025-06-19 10:33:25,565 ERROR scheduler Skipped queueing csf_tz.csftz_hooks.items_revaluation.process_incorrect_balance_qty because it was found in queue for health
2025-06-19 10:33:25,605 ERROR scheduler Skipped queueing payments.payment_gateways.doctype.razorpay_settings.razorpay_settings.capture_payment because it was found in queue for health
2025-06-19 10:33:25,620 ERROR scheduler Skipped queueing erpnext.accounts.doctype.process_payment_reconciliation.process_payment_reconciliation.trigger_reconciliation_for_queued_docs because it was found in queue for health
2025-06-19 10:33:25,632 ERROR scheduler Skipped queueing erpnext.manufacturing.doctype.bom_update_tool.bom_update_tool.auto_update_latest_price_in_all_boms because it was found in queue for health
2025-06-19 10:33:25,635 ERROR scheduler Skipped queueing frappe.deferred_insert.save_to_db because it was found in queue for health
2025-06-19 10:33:25,644 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_contacts.google_contacts.sync because it was found in queue for health
2025-06-19 10:33:25,647 ERROR scheduler Skipped queueing frappe.utils.global_search.sync_global_search because it was found in queue for health
2025-06-19 10:33:25,650 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_calendar.google_calendar.sync because it was found in queue for health
2025-06-19 10:33:25,664 ERROR scheduler Skipped queueing frappe.sessions.clear_expired_sessions because it was found in queue for health
2025-06-19 10:33:25,667 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.notify_unreplied because it was found in queue for health
2025-06-19 10:33:25,676 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.remove_unverified_record because it was found in queue for health
2025-06-19 10:33:25,679 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for health
2025-06-19 10:33:25,691 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for health
2025-06-19 10:34:25,724 ERROR scheduler Skipped queueing frappe.email.doctype.auto_email_report.auto_email_report.send_daily because it was found in queue for health
2025-06-19 10:34:25,733 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for health
2025-06-19 10:34:25,762 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for health
2025-06-19 10:34:25,766 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.remove_unverified_record because it was found in queue for health
2025-06-19 10:34:25,781 ERROR scheduler Skipped queueing hrms.hr.doctype.interview.interview.send_interview_reminder because it was found in queue for health
2025-06-19 10:34:25,800 ERROR scheduler Skipped queueing frappe.integrations.doctype.dropbox_settings.dropbox_settings.take_backups_daily because it was found in queue for health
2025-06-19 10:34:25,839 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_contacts.google_contacts.sync because it was found in queue for health
2025-06-19 10:34:25,853 ERROR scheduler Skipped queueing hrms.hr.utils.allocate_earned_leaves because it was found in queue for health
2025-06-19 10:34:25,872 ERROR scheduler Skipped queueing frappe.sessions.clear_expired_sessions because it was found in queue for health
2025-06-19 10:34:25,900 ERROR scheduler Skipped queueing frappe.automation.doctype.auto_repeat.auto_repeat.make_auto_repeat_entry because it was found in queue for health
2025-06-19 10:34:25,921 ERROR scheduler Skipped queueing erpnext.manufacturing.doctype.bom_update_tool.bom_update_tool.auto_update_latest_price_in_all_boms because it was found in queue for health
2025-06-19 10:34:25,939 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for health
2025-06-19 10:34:25,973 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_calendar.google_calendar.sync because it was found in queue for health
2025-06-19 11:18:39,359 ERROR scheduler Skipped queueing csf_tz.stanbic.sftp.sync_all_stanbank_files because it was found in queue for health
2025-06-19 11:18:39,406 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for health
2025-06-19 11:18:39,411 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.pull because it was found in queue for health
2025-06-19 11:18:39,446 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.process_auto_attendance_for_all_shifts because it was found in queue for health
2025-06-19 11:18:39,457 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for health
2025-06-19 11:18:39,465 ERROR scheduler Skipped queueing hrms.hr.doctype.interview.interview.send_interview_reminder because it was found in queue for health
2025-06-19 11:18:39,470 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for health
2025-06-19 11:18:39,476 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for health
2025-06-19 11:18:39,501 ERROR scheduler Skipped queueing hms_tz.nhif.api.healthcare_utils.set_uninvoiced_so_closed because it was found in queue for health
2025-06-19 11:18:39,519 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for health
2025-06-19 11:18:39,567 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_schedule_assignment.shift_schedule_assignment.process_auto_shift_creation because it was found in queue for health
2025-06-19 11:18:39,608 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for health
2025-06-19 11:18:39,632 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for health
2025-06-19 11:18:39,672 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for health
2025-06-19 11:18:39,679 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.update_last_sync_of_checkin because it was found in queue for health
2025-06-19 11:18:39,687 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for health
2025-06-19 11:18:39,697 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for health
2025-06-19 11:19:39,770 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for health
2025-06-19 11:19:39,776 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for health
2025-06-19 11:19:39,802 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for health
2025-06-19 11:19:39,810 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.update_last_sync_of_checkin because it was found in queue for health
2025-06-19 11:19:39,824 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for health
2025-06-19 11:19:39,833 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_schedule_assignment.shift_schedule_assignment.process_auto_shift_creation because it was found in queue for health
2025-06-19 11:19:39,836 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for health
2025-06-19 11:19:39,840 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for health
2025-06-19 11:19:39,870 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for health
2025-06-19 11:19:39,887 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for health
2025-06-19 11:19:39,936 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.process_auto_attendance_for_all_shifts because it was found in queue for health
2025-06-19 11:19:40,009 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for health
2025-06-19 11:19:40,066 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for health
2025-06-19 11:20:42,444 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.update_last_sync_of_checkin because it was found in queue for health
2025-06-19 11:20:42,509 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for health
2025-06-19 11:21:43,411 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.update_last_sync_of_checkin because it was found in queue for health
2025-06-19 11:21:43,418 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.pull because it was found in queue for health
2025-06-19 11:21:43,467 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for health
2025-06-19 11:21:43,485 ERROR scheduler Skipped queueing hrms.hr.doctype.interview.interview.send_interview_reminder because it was found in queue for health
2025-06-19 11:21:43,491 ERROR scheduler Skipped queueing hms_tz.nhif.api.healthcare_utils.create_invoiced_items_if_not_created because it was found in queue for health
2025-06-19 11:21:43,529 ERROR scheduler Skipped queueing healthcare.healthcare.doctype.patient_appointment.patient_appointment.send_appointment_reminder because it was found in queue for health
2025-06-19 11:21:43,566 ERROR scheduler Skipped queueing payments.payment_gateways.doctype.razorpay_settings.razorpay_settings.capture_payment because it was found in queue for health
2025-06-19 11:21:43,581 ERROR scheduler Skipped queueing frappe.monitor.flush because it was found in queue for health
2025-06-19 11:21:43,587 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_calendar.google_calendar.sync because it was found in queue for health
2025-06-19 11:21:43,642 ERROR scheduler Skipped queueing frappe.email.queue.flush because it was found in queue for health
2025-06-19 11:22:44,553 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_calendar.google_calendar.sync because it was found in queue for health
2025-06-19 11:22:44,557 ERROR scheduler Skipped queueing hrms.hr.doctype.interview.interview.send_interview_reminder because it was found in queue for health
2025-06-19 11:22:44,568 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.update_last_sync_of_checkin because it was found in queue for health
2025-06-19 11:22:44,588 ERROR scheduler Skipped queueing frappe.email.queue.flush because it was found in queue for health
2025-06-19 11:22:44,618 ERROR scheduler Skipped queueing frappe.monitor.flush because it was found in queue for health
2025-06-19 11:22:44,675 ERROR scheduler Skipped queueing hms_tz.nhif.api.healthcare_utils.create_invoiced_items_if_not_created because it was found in queue for health
2025-06-19 11:22:44,733 ERROR scheduler Skipped queueing healthcare.healthcare.doctype.patient_appointment.patient_appointment.send_appointment_reminder because it was found in queue for health
2025-06-19 11:22:44,737 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.pull because it was found in queue for health
2025-06-19 11:22:44,757 ERROR scheduler Skipped queueing payments.payment_gateways.doctype.razorpay_settings.razorpay_settings.capture_payment because it was found in queue for health
2025-06-19 11:22:44,813 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for health
2025-06-19 11:23:44,949 ERROR scheduler Skipped queueing healthcare.healthcare.doctype.patient_appointment.patient_appointment.send_appointment_reminder because it was found in queue for health
2025-06-19 11:23:44,954 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for health
2025-06-19 11:23:45,002 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.update_last_sync_of_checkin because it was found in queue for health
2025-06-19 11:23:45,021 ERROR scheduler Skipped queueing hrms.hr.doctype.interview.interview.send_interview_reminder because it was found in queue for health
2025-06-19 11:23:45,033 ERROR scheduler Skipped queueing payments.payment_gateways.doctype.razorpay_settings.razorpay_settings.capture_payment because it was found in queue for health
2025-06-19 11:23:45,044 ERROR scheduler Skipped queueing frappe.email.queue.flush because it was found in queue for health
2025-06-19 11:23:45,092 ERROR scheduler Skipped queueing hms_tz.nhif.api.healthcare_utils.create_invoiced_items_if_not_created because it was found in queue for health
2025-06-19 11:23:45,122 ERROR scheduler Skipped queueing frappe.monitor.flush because it was found in queue for health
2025-06-19 11:23:45,125 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_calendar.google_calendar.sync because it was found in queue for health
2025-06-19 11:23:45,172 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.pull because it was found in queue for health
2025-06-19 11:24:46,453 ERROR scheduler Skipped queueing hrms.hr.doctype.interview.interview.send_interview_reminder because it was found in queue for health
2025-06-19 11:24:46,486 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for health
2025-06-19 11:24:46,526 ERROR scheduler Skipped queueing healthcare.healthcare.doctype.patient_appointment.patient_appointment.send_appointment_reminder because it was found in queue for health
2025-06-19 11:24:46,545 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_calendar.google_calendar.sync because it was found in queue for health
2025-06-19 11:24:46,592 ERROR scheduler Skipped queueing frappe.email.queue.flush because it was found in queue for health
2025-06-19 11:24:46,628 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.pull because it was found in queue for health
2025-06-19 11:24:46,650 ERROR scheduler Skipped queueing payments.payment_gateways.doctype.razorpay_settings.razorpay_settings.capture_payment because it was found in queue for health
2025-06-19 11:24:46,690 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.update_last_sync_of_checkin because it was found in queue for health
2025-06-19 11:24:46,737 ERROR scheduler Skipped queueing frappe.monitor.flush because it was found in queue for health
2025-06-19 11:24:46,749 ERROR scheduler Skipped queueing hms_tz.nhif.api.healthcare_utils.create_invoiced_items_if_not_created because it was found in queue for health
2025-06-19 12:01:32,473 ERROR scheduler Skipped queueing csf_tz.stanbic.sftp.process_download_files because it was found in queue for health
2025-06-19 12:01:32,483 ERROR scheduler Skipped queueing frappe.email.queue.flush because it was found in queue for health
2025-06-19 12:01:32,497 ERROR scheduler Skipped queueing erpnext.projects.doctype.project.project.collect_project_status because it was found in queue for health
2025-06-19 12:01:32,512 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for health
